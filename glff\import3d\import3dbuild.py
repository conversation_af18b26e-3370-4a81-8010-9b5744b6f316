import os
import logging
import json
import pandas as pd
import geopandas as gpd
from django.views.decorators.csrf import csrf_exempt
from sqlalchemy import create_engine, text
from shapely.geometry import shape, MultiPolygon
from sqlalchemy.exc import SQLAlchemyError
from tqdm import tqdm
from django.http import JsonResponse
from django.views import View
from django.conf import settings
import uuid
import zipfile
from typing import Any

# 新增 redis 模块导入
import redis

# 从 settings.py 读取 Redis 配置
REDIS_HOST = settings.REDIS_CONFIG.get("HOST", "localhost")
REDIS_PORT = settings.REDIS_CONFIG.get("PORT", 6379)
REDIS_DB = settings.REDIS_CONFIG.get("DB", 0)
redis_client = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB, decode_responses=True)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


class ProgressTracker:
    @staticmethod
    def update(task_id: str, current: int, total: int, stage: str):
        if total > 0:
            logging.info(f"[{stage}] 进度: {current}/{total} ({current / total:.1%})")
        else:
            logging.info(f"[{stage}] 进度: {current}/{total}")
        progress_data = json.dumps({"current": current, "total": total, "stage": stage})
        redis_client.set(f"progress:{task_id}", progress_data)


def create_db_engine() -> Any:
    db_config = settings.DATABASES['default']
    db_url = f"postgresql://{db_config['USER']}:{db_config['PASSWORD']}@{db_config['HOST']}:{db_config['PORT']}/{db_config['NAME']}"
    engine = create_engine(db_url)
    return engine


def table_exists(engine, table_name: str) -> bool:
    schema_name = "geodata"
    with engine.connect() as conn:
        return conn.execute(text(f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = '{schema_name.lower()}' AND table_name = '{table_name.lower()}'
            );
        """)).scalar()


def read_shapefile(shp_path: str) -> gpd.GeoDataFrame:
    if not os.path.exists(shp_path):
        raise FileNotFoundError(f"Shapefile 不存在：{shp_path}")
    return gpd.read_file(shp_path)


def ensure_multipolygonz(geom) -> MultiPolygon:
    if geom is None:
        return None
    if geom.geom_type == 'Polygon' or geom.geom_type == 'PolygonZ':
        return MultiPolygon([geom])
    return geom


def get_grid_id(lon: float, lat: float, grid_size: int = 10) -> str:
    grid_lon = int(lon // grid_size * grid_size)
    grid_lat = int(lat // grid_size * grid_size)
    return f"{grid_lon}_{grid_lat}"


def encode_grid_id(grid_id: str) -> str:
    return grid_id.replace("-", "n").replace("_", "p")


# 【核心修改】: 在创建表的同时创建必要的索引
def create_partitioned_table(engine, base_table_name: str):
    if table_exists(engine, base_table_name):
        logging.info(f"主表 {base_table_name} 已存在，跳过创建。")
        return

    with engine.begin() as conn:
        try:
            # 步骤 1: 创建主分区表，包含所有最终需要的字段
            conn.execute(text(f"""
                CREATE TABLE geodata.{base_table_name} (
                    "GXP_ID" int8, "Type" text, "Status" text, "Confidence" text, "ImgDateTim" text, "OBJECTID" int4,
                    buidingind text, 
                    building_id SERIAL, 
                    geometry Geometry(MultiPolygonZ, 4326),
                    geom_3d_wgs84 Geometry(MultiPolygonZ, 4326),
                    center_3d Geometry(PointZ, 4326),
                    lon FLOAT, lat FLOAT, z FLOAT,
                    length FLOAT, width FLOAT, height FLOAT,
                    direction_angle FLOAT, orientation VARCHAR(20),
                    grid_id VARCHAR(20),
                    renwu_id VARCHAR(64), file_id VARCHAR(64),
                    smid SERIAL, task_id VARCHAR(64), geo_id VARCHAR(64),
                    examid_status INT8, geopolygon TEXT,
                    target_code VARCHAR(64), child_target_code VARCHAR(64),
                    PRIMARY KEY (building_id, grid_id)
                ) PARTITION BY LIST (grid_id);
            """))
            logging.info(f"主表 geodata.{base_table_name} 创建成功，包含所有字段。")

            # 步骤 2: 创建子分区
            for lon in range(-180, 180, 10):
                for lat in range(-90, 90, 10):
                    grid_id = f"{lon}_{lat}"
                    table_partition_name = f"{base_table_name}_grid_{encode_grid_id(grid_id)}"
                    conn.execute(text(f"""
                        CREATE TABLE IF NOT EXISTS geodata.{table_partition_name} PARTITION OF geodata.{base_table_name}
                            FOR VALUES IN ('{grid_id}');
                    """))
            logging.info(f"geodata.{base_table_name} 的所有子表（分区）创建或确认完毕。")

            # --- 【新增逻辑】: 创建必要的索引 ---
            logging.info(f"为 geodata.{base_table_name} 添加索引...")

            # 空间索引 (GIST) - 用于加速空间查询
            conn.execute(text(f"""
                CREATE INDEX IF NOT EXISTS idx_{base_table_name}_geom 
                ON geodata.{base_table_name} USING GIST (geometry);
            """))
            conn.execute(text(f"""
                CREATE INDEX IF NOT EXISTS idx_{base_table_name}_geom_3d_wgs84 
                ON geodata.{base_table_name} USING GIST (geom_3d_wgs84);
            """))

            # 标准索引 (B-Tree) - 用于加速基于ID的筛选
            conn.execute(text(f"""
                CREATE INDEX IF NOT EXISTS idx_{base_table_name}_renwu_id 
                ON geodata.{base_table_name} (renwu_id);
            """))
            conn.execute(text(f"""
                CREATE INDEX IF NOT EXISTS idx_{base_table_name}_file_id 
                ON geodata.{base_table_name} (file_id);
            """))

            logging.info(f"geodata.{base_table_name} 的索引创建完毕。")
            # --- 结束新增逻辑 ---

        except SQLAlchemyError as e:
            logging.error(f"创建分区表或索引失败: {e}")
            raise


def import_to_postgis(gdf: gpd.GeoDataFrame, engine, table_name: str,
                      srid_local: int, renwu_id: str, file_id: str,
                      append: bool = False, task_id: str = None) -> None:
    gdf['geometry'] = gdf['geometry'].apply(ensure_multipolygonz)
    total = len(gdf)
    gdf['renwu_id'] = renwu_id
    gdf['file_id'] = file_id
    if task_id:
        gdf['task_id'] = task_id

    if task_id:
        ProgressTracker.update(task_id, 0, total, "数据导入")

    batch_size = 1000
    for i in tqdm(range(0, len(gdf), batch_size), desc="导入进度", unit="features"):
        batch = gdf.iloc[i:i + batch_size].copy()
        batch['grid_id'] = batch['geometry'].apply(
            lambda geom: get_grid_id(geom.centroid.x, geom.centroid.y) if geom else None)
        batch.dropna(subset=['grid_id'], inplace=True)
        if batch.empty:
            continue
        batch.to_postgis(name=table_name, con=engine, schema="geodata", if_exists="append", index=False)
        if task_id:
            ProgressTracker.update(task_id, min(i + len(batch), total), total, "数据导入")

    logging.info(f"数据已导入到 PostGIS 表 '{table_name}'，并记录了 renwu_id='{renwu_id}' 和 file_id='{file_id}'")


def update_fields(engine, table_name: str, srid_local: int, srid_wgs84: int, task_id: str = None) -> None:
    sql_update_wgs84_tpl = f"""
        UPDATE geodata.{{partition_name}}
        SET geom_3d_wgs84 = ST_Transform(geometry, {srid_wgs84})
        WHERE geom_3d_wgs84 IS NULL;
    """
    sql_update_attributes_tpl = f"""
        UPDATE geodata.{{partition_name}} AS t
        SET
            length = (calc.xmax - calc.xmin), width = (calc.ymax - calc.ymin), height = (calc.zmax - calc.zmin),
            center_3d = ST_SetSRID(ST_MakePoint((calc.xmin + calc.xmax) / 2, (calc.ymin + calc.ymax) / 2, (calc.zmin + calc.zmax) / 2), {srid_local}),
            lon = (calc.xmin + calc.xmax) / 2, lat = (calc.ymin + calc.ymax) / 2, z = (calc.zmin + calc.zmax) / 2,
            direction_angle = calc.direction_angle,
            orientation = CASE 
                WHEN MOD(calc.direction_angle::numeric, 180.0) BETWEEN 22.5 AND 67.5 THEN '东北-西南'
                WHEN MOD(calc.direction_angle::numeric, 180.0) BETWEEN 67.5 AND 112.5 THEN '东西'
                WHEN MOD(calc.direction_angle::numeric, 180.0) BETWEEN 112.5 AND 157.5 THEN '西北-东南'
                ELSE '南北'
            END,
            geopolygon = ST_AsText(geometry)
        FROM (
            SELECT
                building_id, grid_id,
                ST_XMin(geometry) as xmin, ST_XMax(geometry) as xmax, ST_YMin(geometry) as ymin, ST_YMax(geometry) as ymax,
                ST_ZMin(geometry) as zmin, ST_ZMax(geometry) as zmax,
                DEGREES(ST_Azimuth(ST_PointN(ST_ExteriorRing(ST_OrientedEnvelope(geometry)), 1), ST_PointN(ST_ExteriorRing(ST_OrientedEnvelope(geometry)), 2))) AS direction_angle
            FROM geodata.{{partition_name}} WHERE center_3d IS NULL
        ) AS calc
        WHERE t.building_id = calc.building_id AND t.grid_id = calc.grid_id;
    """

    partition_names = []
    with engine.connect() as conn:
        partitions_query = text("""
            SELECT child.relname FROM pg_inherits
            JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
            JOIN pg_class child ON pg_inherits.inhrelid = child.oid
            WHERE parent.relname = :table_name AND parent.relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'geodata');
        """)
        partitions = conn.execute(partitions_query, {"table_name": table_name}).fetchall()
        partition_names = [p[0] for p in partitions]

    if not partition_names:
        logging.warning(f"表 {table_name} 没有任何分区，跳过字段更新。")
        if task_id: ProgressTracker.update(task_id, 1, 1, "字段计算完成 (无分区)")
        return

    logging.info(f"开始对 {len(partition_names)} 个分区进行逐一更新...")
    total_partitions = len(partition_names)
    if task_id: ProgressTracker.update(task_id, 0, total_partitions, "字段计算")

    for i, part_name in enumerate(tqdm(partition_names, desc="更新分区属性")):
        try:
            with engine.begin() as conn:
                quoted_part_name = f'"{part_name}"'
                sql_wgs84 = sql_update_wgs84_tpl.format(partition_name=quoted_part_name)
                conn.execute(text(sql_wgs84))

                sql_attr = sql_update_attributes_tpl.format(partition_name=quoted_part_name)
                conn.execute(text(sql_attr))
        except SQLAlchemyError as e:
            logging.error(f"更新分区 {part_name} 失败: {e}", exc_info=True)
            continue

        if task_id:
            ProgressTracker.update(task_id, i + 1, total_partitions, "字段计算")

    logging.info(f"表 geodata.{table_name} 的字段属性计算成功。")


def export_geojson(engine, table_name: str, output_file: str,
                   srid_wgs84: int, task_id: str = None) -> None:
    sql_select = f"""
        SELECT 
            building_id, ST_AsGeoJSON(ST_Force2D(geom_3d_wgs84)) AS geometry, 
            lon, lat, z,
            ROUND(length::numeric, 2) AS length, ROUND(width::numeric, 2) AS width, ROUND(height::numeric, 2) AS height,
            ROUND(direction_angle::numeric, 1) AS direction_angle, orientation, 
            renwu_id, file_id, smid, task_id, geo_id, examid_status, geopolygon, target_code, child_target_code
        FROM geodata.{table_name}
    """
    page_size = 5000
    dfs = []
    with engine.connect() as conn:
        total = conn.execute(text(f"SELECT COUNT(*) FROM geodata.{table_name}")).scalar_one_or_none() or 0
        if total == 0:
            logging.warning("没有数据可以导出。")
            gpd.GeoDataFrame([], columns=['geometry']).to_file(output_file, driver="GeoJSON")
            return

        if task_id: ProgressTracker.update(task_id, 0, total, "数据导出")

        for offset in tqdm(range(0, total, page_size), desc="导出进度"):
            page_sql = f"{sql_select} LIMIT {page_size} OFFSET {offset}"
            df = pd.read_sql(page_sql, conn)
            dfs.append(df)
            if task_id: ProgressTracker.update(task_id, min(offset + len(df), total), total, "数据导出")

    if not dfs:
        logging.warning("没有数据可以导出。")
        gpd.GeoDataFrame([], columns=['geometry']).to_file(output_file, driver="GeoJSON")
        return

    full_df = pd.concat(dfs, ignore_index=True)
    full_df["geometry"] = full_df["geometry"].apply(lambda x: shape(json.loads(x)) if x and x.strip() else None)
    gdf = gpd.GeoDataFrame(full_df, geometry="geometry", crs=f"EPSG:{srid_wgs84}")
    gdf.to_file(output_file, driver="GeoJSON")
    logging.info(f"GeoJSON 文件已导出至 '{output_file}'")


# --- Django Views ---

class ImportDataView(View):
    @csrf_exempt
    def post(self, request):
        task_id = str(uuid.uuid4())
        zip_path = request.POST.get("zip_path")
        renwu_id = request.POST.get("renwu_id")
        file_id = request.POST.get("file_id")

        if not zip_path or not os.path.isfile(zip_path):
            return JsonResponse({"error": "ZIP文件路径不存在或不是文件"}, status=400)
        if not renwu_id:
            return JsonResponse({"error": "缺少参数: renwu_id"}, status=400)
        if not file_id:
            return JsonResponse({"error": "缺少参数: file_id"}, status=400)

        temp_extract_dir = os.path.join(settings.MEDIA_ROOT, "extracted", str(uuid.uuid4()))
        os.makedirs(temp_extract_dir, exist_ok=True)
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(temp_extract_dir)
            logging.info(f"已解压ZIP文件至：{temp_extract_dir}")
            shp_file = next(
                (os.path.join(root, file) for root, _, files in os.walk(temp_extract_dir) for file in files if
                 file.lower().endswith('.shp')), None)

            if not shp_file:
                return JsonResponse({"error": "ZIP文件中未找到SHP文件"}, status=400)

            config = {"input_shp": shp_file, "srid_local": 4326, "srid_wgs84": 4326,
                      "table_name": "buildings_tin", "append_mode": True}

            engine = create_db_engine()
            logging.info("数据库连接已建立")

            create_partitioned_table(engine, config["table_name"])

            gdf = read_shapefile(config["input_shp"])
            logging.info(f"成功读取Shapefile：{config['input_shp']}，包含 {len(gdf)} 条记录。")

            import_to_postgis(gdf, engine, config["table_name"], config["srid_local"],
                              renwu_id=renwu_id, file_id=file_id, append=config["append_mode"], task_id=task_id)

            update_fields(engine, config["table_name"], config["srid_local"], config["srid_wgs84"], task_id=task_id)

            return JsonResponse(
                {"message": "数据导入和处理成功", "task_id": task_id, "renwu_id": renwu_id, "file_id": file_id})

        except Exception as e:
            logging.error(f"处理任务失败: {str(e)}", exc_info=True)
            return JsonResponse({"error": f"处理任务失败: {str(e)}"}, status=500)
        finally:
            import shutil
            if os.path.exists(temp_extract_dir):
                shutil.rmtree(temp_extract_dir)
                logging.info(f"已清理临时目录: {temp_extract_dir}")


class ExportGeoJSONView(View):
    @csrf_exempt
    def get(self, request):
        task_id = str(uuid.uuid4())
        config = {"table_name": "buildings_tin",
                  "output_geojson": os.path.join(settings.MEDIA_ROOT, "exports", f"export_{task_id}.geojson"),
                  "srid_wgs84": 4326}
        os.makedirs(os.path.dirname(config["output_geojson"]), exist_ok=True)
        engine = create_db_engine()
        export_geojson(engine, config["table_name"], config["output_geojson"], config["srid_wgs84"], task_id)
        return JsonResponse({"message": "GeoJSON 文件已导出", "file_path": config["output_geojson"]})


class ProgressView(View):
    @csrf_exempt
    def get(self, request):
        task_id = request.GET.get("task_id")
        if not task_id:
            return JsonResponse({"error": "缺少 task_id 参数"}, status=400)
        progress_data = redis_client.get(f"progress:{task_id}")
        if progress_data:
            progress = json.loads(progress_data)
            return JsonResponse(progress)
        else:
            return JsonResponse({"status": "pending", "message": "任务正在等待或ID无效"}, status=404)
