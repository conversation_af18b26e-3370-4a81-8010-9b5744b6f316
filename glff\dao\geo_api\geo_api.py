from django.db import models, DatabaseError, IntegrityError, transaction, connection
from django.core.exceptions import ObjectDoesNotExist, MultipleObjectsReturned
import json
import logging

logger = logging.getLogger(__name__)


class GeoApi(models.Model):
    id = models.CharField(verbose_name='Api ID', primary_key=True, max_length=32)
    url = models.CharField(verbose_name='API地址', max_length=1024)
    type = models.CharField(verbose_name='API类型', max_length=32)
    file_id = models.Char<PERSON>ield(verbose_name='文件id', max_length=32)
    remarks = models.Char<PERSON>ield(verbose_name='描述信息', max_length=255, null=True, blank=True)
    params = models.TextField(verbose_name='接口参数信息', null=True, blank=True)

    class Meta:
        db_table = '"geoapi"."geo_api"'
        verbose_name = '地理API'
        verbose_name_plural = '地理APIs'

    def __str__(self):
        return f"{self.type} API ({self.id})"

    @property
    def params_dict(self):
        """增强版参数解析"""
        try:
            return json.loads(self.params) if self.params else {}
        except (TypeError, json.JSONDecodeError) as e:
            logger.error(f"参数解析失败 ID:{self.id} | 错误:{str(e)}")
            return {}

    @params_dict.setter
    def params_dict(self, value):
        """增强版参数序列化"""
        try:
            self.params = json.dumps(value, ensure_ascii=False) if value else None
        except (TypeError, OverflowError) as e:
            logger.error(f"参数序列化失败 ID:{self.id} | 错误:{str(e)}")
            self.params = None

    @classmethod
    def create_geo_api(cls, id, url, api_type, file_id, remarks=None, params=None):
        """增强创建方法，包含完整事务和错误处理"""
        try:
            with transaction.atomic():
                # 前置验证
                if not all([id, url, api_type, file_id]):
                    raise ValueError("必填字段(id/url/type/file_id)不能为空")

                obj = cls.objects.create(
                    id=id,
                    url=url,
                    type=api_type,
                    file_id=file_id,
                    remarks=remarks,
                    params=params
                )
                logger.info(f"API创建成功 ID:{id}")
                return obj

        except IntegrityError as e:
            error_msg = f"数据冲突 ID:{id} | 错误:{str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg) from e
        except DatabaseError as e:
            error_msg = f"数据库错误 ID:{id} | 错误:{str(e)}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e
        except Exception as e:
            error_msg = f"未知错误 ID:{id} | 类型:{type(e).__name__} | 错误:{str(e)}"
            logger.error(error_msg)
            raise

    @classmethod
    def update_geo_api(cls, id, **kwargs):
        """增强更新方法，支持字段验证和原子操作"""
        try:
            with transaction.atomic():
                valid_fields = {
                    'url': kwargs.get('url'),
                    'type': kwargs.get('type'),
                    'file_id': kwargs.get('file_id'),
                    'remarks': kwargs.get('remarks'),
                    'params': kwargs.get('params')
                }

                # 清理空值
                update_fields = {k: v for k, v in valid_fields.items() if v is not None}

                rows = cls.objects.filter(id=id).update(**update_fields)
                if rows == 0:
                    raise ObjectDoesNotExist(f"API不存在 ID:{id}")

                logger.info(f"API更新成功 ID:{id} | 更新字段:{list(update_fields.keys())}")
                return cls.objects.get(id=id)

        except (ObjectDoesNotExist, MultipleObjectsReturned) as e:
            error_msg = f"查询异常 ID:{id} | 错误:{str(e)}"
            logger.error(error_msg)
            raise
        except IntegrityError as e:
            error_msg = f"数据冲突 ID:{id} | 错误:{str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg) from e
        except DatabaseError as e:
            error_msg = f"数据库错误 ID:{id} | 错误:{str(e)}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    @classmethod
    def delete_geo_api(cls, id):
        """增强删除方法"""
        try:
            with transaction.atomic():
                obj = cls.objects.get(id=id)
                obj.delete()
                logger.info(f"API删除成功 ID:{id}")
                return True

        except ObjectDoesNotExist as e:
            error_msg = f"删除目标不存在 ID:{id}"
            logger.warning(error_msg)
            raise ValueError(error_msg) from e
        except DatabaseError as e:
            error_msg = f"数据库删除失败 ID:{id} | 错误:{str(e)}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    @classmethod
    def get_by_id(cls, id: str, for_update: bool = False):
        """使用原始SQL查询单个记录（支持行锁）"""
        try:
            with connection.cursor() as cursor:
                # 基础查询语句
                base_sql = """
                       SELECT id, url, type, file_id, remarks, params 
                       FROM geoapi.geo_api 
                       WHERE id = %s
                   """

                # 添加行锁（必须在事务中）
                if for_update:
                    base_sql += " FOR UPDATE"

                # 执行参数化查询（防SQL注入）
                cursor.execute(base_sql, [id])
                row = cursor.fetchone()

                if not row:
                    logger.warning(f"SQL查询无结果 ID: {id}")
                    return None

                if cursor.fetchone() is not None:
                    raise MultipleObjectsReturned(f"主键重复 ID: {id}")

                # 将原始数据转换为模型实例
                return cls._from_db_row(row)

        except DatabaseError as e:
            logger.error(f"SQL查询失败 ID: {id} | 错误: {str(e)}")
            raise
        except MultipleObjectsReturned as e:
            logger.critical(f"主键冲突 ID: {id} | 错误: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"未知错误 ID: {id} | 类型: {type(e).__name__}")
            raise

    @classmethod
    def _from_db_row(cls, row: tuple):
        """将数据库行转换为模型实例（私有方法）"""
        return cls(
            id=row[0],
            url=row[1],
            type=row[2],
            file_id=row[3],
            remarks=row[4],
            params=row[5]
        )

    @classmethod
    def get_by_file_id(cls, file_id):
        """增强批量查询方法"""
        try:
            return cls.objects.filter(file_id=file_id).order_by('type')
        except DatabaseError as e:
            error_msg = f"查询失败 file_id:{file_id} | 错误:{str(e)}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e


# 使用示例
if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    try:
        # 创建测试数据
        test_id = "test_" + str(hash('demo'))
        new_api = GeoApi.create_geo_api(
            id=test_id,
            url="https://demo.example.com/wms",
            api_type="WMS",
            file_id="demo_123",
            remarks="压力测试API",
            params=json.dumps({"layers": "base"})
        )

        # 更新测试
        updated_api = GeoApi.update_geo_api(test_id, url="https://updated.example.com")

        # 查询测试
        api_record = GeoApi.get_by_id(test_id)
        print(f"查询结果: {api_record.url if api_record else '无记录'}")

        # 删除测试
        GeoApi.delete_geo_api(test_id)

    except Exception as e:
        logger.exception("主流程异常")
