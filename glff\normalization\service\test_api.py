import unittest
import requests
import json
import os
import geopandas as gpd
import rasterio
import numpy as np
from shapely.geometry import Point, Polygon, LineString

BASE_URL = 'http://127.0.0.1:8000/glff/'
DATA_PATH = os.path.abspath(os.path.join(os.getcwd(), "../../.."))

def post(api, data):
    url = BASE_URL + api
    resp = requests.post(url, json=data)
    return resp

def create_test_shp(path, geom_type='Point', with_invalid=False, with_missing_fields=False, empty=False):
    """生成测试Shapefile，支持点/面/线、空数据、无效几何、缺字段"""
    if empty:
        gdf = gpd.GeoDataFrame({'geometry': []}, geometry='geometry', crs='EPSG:4326')
    else:
        if geom_type == 'Point':
            data = {'id': [1, 2], 'geometry': [Point(0, 0), Point(1, 1)]}
        elif geom_type == 'Polygon':
            poly = Polygon([(0,0),(1,0),(1,1),(0,1)])
            data = {'id': [1], 'geometry': [poly]}
            if with_invalid:
                # 添加一个自相交无效多边形
                invalid_poly = Polygon([(0,0),(1,1),(0,1),(1,0),(0,0)])
                data['id'].append(2)
                data['geometry'].append(invalid_poly)
        elif geom_type == 'LineString':
            data = {'id': [1], 'geometry': [LineString([(0,0),(1,1)])]}
        else:
            raise ValueError('Unsupported geom_type')
        gdf = gpd.GeoDataFrame(data, geometry='geometry', crs='EPSG:4326')
        if with_missing_fields:
            gdf = gdf.drop(columns=['id'])
    gdf.to_file(path, driver='ESRI Shapefile', encoding='utf-8')

def create_test_tif(path):
    """生成1x1像素的测试TIF文件"""
    arr = np.ones((1,1), dtype=np.uint8)
    profile = {
        'driver': 'GTiff', 'height': 1, 'width': 1, 'count': 1, 'dtype': 'uint8',
        'crs': 'EPSG:4326', 'transform': rasterio.transform.from_origin(0, 1, 1, 1)
    }
    with rasterio.open(path, 'w', **profile) as dst:
        dst.write(arr, 1)

def create_test_geojson(path):
    """生成简单的Point类型GeoJSON"""
    geojson = {
        "type": "FeatureCollection",
        "features": [
            {"type": "Feature", "geometry": {"type": "Point", "coordinates": [0,0]}, "properties": {"id": 1}}
        ]
    }
    with open(path, 'w', encoding='utf-8') as f:
        json.dump(geojson, f)
    return geojson

def create_test_geojson_str():
    """返回GeoJSON字符串"""
    return json.dumps({
        "type": "FeatureCollection",
        "features": [
            {"type": "Feature", "geometry": {"type": "Point", "coordinates": [0,0]}, "properties": {"id": 1}}
        ]
    })

class TestNormalizationAPI(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        # 生成所有测试数据
        create_test_shp('test_shp.shp', geom_type='Point')
        create_test_shp('test_shp_poly.shp', geom_type='Polygon')
        create_test_shp('../../../data/geodata/empty/test_shp_invalid.shp', geom_type='Polygon', with_invalid=True)
        # create_test_shp('../../../data/geodata/miss/test_shp_missing.shp', geom_type='Point', with_missing_fields=True)
        create_test_shp('../../../data/geodata/empty/test_shp_empty.shp', geom_type='Point', empty=True)
        create_test_tif('test_tif.tif')
        create_test_geojson('test.geojson')

    def assertInPrint(self, member, container, msg=None):
        print(f"断言: {member!r} in {container!r}")
        self.assertIn(member, container, msg)

    def assertEqualPrint(self, first, second, msg=None):
        print(f"断言: {first!r} == {second!r}")
        self.assertEqual(first, second, msg)

    ##############################################文件预处理##############################################################
    def test_file_extract_text(self):
        """测试文本提取API"""
        test_path = os.path.join(DATA_PATH, 'data/file-test/鲁迅简介.doc')   # doc 要想办法处理下
        resp = post('normalization/file/extract_text/', {'file_path': test_path})
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_file_preprocess(self):
        """测试文档格式转换API"""
        test_path = os.path.join(DATA_PATH, 'data/file-test/鲁迅简介.doc')   # doc 要想办法处理下
        test_dir = os.path.join(DATA_PATH, 'data/file-test/')   # doc 要想办法处理下
        resp = post('normalization/file/preprocess/', {'file_path': test_path, 'output_path': test_dir})
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())


    ##############################################重投影测试##############################################################
    def test_reproject_transform(self):
        """测试单文件重投影API（需要考虑异步）"""
        seven_params = {
            "dx": 100, "dy": 200, "dz": 50,
            "rx": 0.1, "ry": 0.2, "rz": 0.3,
            "scale": 1.0001
        }
        resp = post('normalization/reproject/transform/', {
            'input_path': os.path.join(DATA_PATH, 'data/geodata/ffff.tif'),
            'output_path': os.path.join(DATA_PATH, 'data/geodata/ffff_test1.tif'),
            'src_crs': 'EPSG:32648',
            'dst_crs': 'EPSG:4326',
            'resample_method': 'bilinear',
            'precision': 'double',
            'seven_params': seven_params
        })
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_reproject_batch_transform(self):
        """测试批量重投影API （需要考虑异步）"""
        seven_params = {
            "dx": 100, "dy": 200, "dz": 50,
            "rx": 0.1, "ry": 0.2, "rz": 0.3,
            "scale": 1.0001
        }
        resp = post('normalization/reproject/batch_transform/', {
            'input_files': [os.path.join(DATA_PATH, 'data/geodata/ffff.tif')],
            'output_dir': os.path.join(DATA_PATH, 'data/geodata/test2/'),
            'src_crs': 'EPSG:32648',
            'dst_crs': 'EPSG:4326',
            'resample_method': 'bilinear',
            'precision': 'double',
            'seven_params': seven_params
        })
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_reproject_single_coord(self):
        """测试单点坐标转换API"""
        resp = post('normalization/reproject/transform_single_coord/', {'coord': [565853.00763, 2899633.08075],'src_crs': 'EPSG:32648', 'dst_crs': 'EPSG:4326'})
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_reproject_convert_geojson(self):
        """测试GeoJSON坐标转换API"""
        geojson = '{"type": "FeatureCollection","features": [{"type": "Feature","geometry": {"type": "Point","coordinates": [566353.96, 2899559.66]}}]}';
        resp = post('normalization/reproject/convert_geojson/', {'geojson': json.loads(geojson),'src_crs': 'EPSG:32648', 'dst_crs': 'EPSG:4326'})
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_reproject_convert_geojson_shp(self):
        """测试Shapefile坐标转换API"""
        resp = post('normalization/reproject/convert_geojson_shp/', {
            'input_path': os.path.join(DATA_PATH,'data/geodata/geojson_shp/aaaa/aaaa.shp'), 'output_path': os.path.join(DATA_PATH,'data/geodata/geojson_shp/aaaa/out/aaaa_out.shp'),
            'src_crs': 'EPSG:4326', 'dst_crs': 'EPSG:3857'
        })
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_geo_info_shp(self):
        """测试Shapefile空间信息提取API"""
        # resp = post('normalization/geo_info/shp_info/', {'shp_path': os.path.join(DATA_PATH,'data/3d-testdata/unzip/merge.shp')})
        resp = post('normalization/geo_info/shp_info/', {'shp_path': os.path.join(DATA_PATH,'H:\\code\\nank-geo-dev\\nank_geo-v3.0\\media\\preprocess\\temp\\shp_10ea5de251674108afc5a4bc327d33b02\\merge\\merge.shp')})
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_geo_info_tif(self):
        """测试TIF空间信息提取API  (返回的结果有点大，可以直接去读文件)"""
        resp = post('normalization/geo_info/tif_info/', {'tif_path': os.path.join(DATA_PATH,'data/geodata/ffff.tif')})
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_image_process(self):
        """测试影像预处理API（最复杂参数）"""
        # testFilePath = os.path.join(DATA_PATH,'data/geodata/aaa/aaaaa.img')
        # resp = post('normalization/image/process/', {'input_path': testFilePath,'denoise':False,'resample_resolution':[200,200],'build_pyramids':False,'create_thumbnail':False}) # 'resample_resolution':[0.1,0.1]
        testFilePath = os.path.join(DATA_PATH,'H:\\code\\nank-geo-dev\\nank_geo-v3.0\\data\\geodata\\tifs\\world.tif')
        resp = post('normalization/image/process/', {'input_path': testFilePath,'denoise':False,'resample_resolution':None,'build_pyramids':False,'create_thumbnail':False}) # 'resample_resolution':[0.1,0.1]
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_mosaic(self):
        """测试影像接边拼接API，options参数全  (很慢做成异步的，并将异步信息存放到数据库或者一个方便取得到的地方)"""
        inputFolder = os.path.join(DATA_PATH,'data/geodata/jiebian')
        outputPath = os.path.join(DATA_PATH,'data/geodata/jiebian_jiebian.tif')
        resp = post('normalization/mosaic/', {
            'input_paths': inputFolder,
            'output_path': outputPath,
            'options': {
                'target_crs': 'EPSG:4326',
                'blend_distance': 0,
                'nodata_value': 'auto',
                'num_threads': 'ALL_CPUS',
                'build_overviews': True,
                'overview_resampling': 'AVERAGE',
                'overview_levels': [2,4,8,16,32],
                'resample_alg': 'cubic',
                'output_format': 'GTiff',
                'creationOptions': ['COMPRESS=LZW', 'TILED=YES', 'BIGTIFF=YES']
            }
        })
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())


    ####################################################################################################################
    def test_attribute_check(self):
        """测试属性结构与几何检查API（shp文件）"""
        testShpPath = os.path.join(DATA_PATH,'data/geodata/shp/4326/4326.shp')
        schemaSucess = {'id': {'required': True, 'unique': False, 'type': 'int'}}
        # schemaError = {'id': {'required': True, 'unique': False, 'type': 'int'}}
        resp = post('normalization/quality/attribute_check/', {'shp_path': testShpPath, 'schema': schemaSucess, 'id_field': 'id'})
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_attribute_check_db(self):
        """测试属性结构与几何检查API（数据库）"""
        """
            # 测试表SQL
        """

        # 这里只做接口连通性测试，实际需有数据库环境
        schema = {'id': {'required': True, 'unique': True, 'type': 'int'}}
        resp = post('normalization/quality/attribute_check_db/', {
            'db_conn_str': 'postgresql://postgres:postgres@localhost:5432/skgeo-manager',
            'schema': schema,
            'table_name': 'db_final_test_table',
            'db_schema': 'attr_check_demo_v12',
            'id_field': 'id'
        })
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_logical_consistency(self):
        """测试逻辑一致性检查API（数据库规则）"""
        """
                -- 创建一个公园表
        CREATE TABLE public.parks (
            park_id VARCHAR(10) PRIMARY KEY,
            park_name VARCHAR(50),
            has_playground BOOLEAN,
            geometry GEOMETRY(POLYGON, 4326) -- 假设使用 WGS84 坐标系
        );
        
        -- 创建一个设施表
        CREATE TABLE public.facilities (
            facility_id SERIAL PRIMARY KEY,
            facility_type VARCHAR(20),
            geometry GEOMETRY(POINT, 4326)
        );
        
        -- 插入一些测试数据 (包含一些故意设置的错误)
        INSERT INTO public.parks (park_id, park_name, has_playground, geometry) VALUES
        ('P-01', 'Central Park', true, ST_GeomFromText('POLYGON((0 0, 10 0, 10 10, 0 10, 0 0))', 4326)),
        ('P-02', 'Riverfront Park', true, ST_GeomFromText('POLYGON((20 0, 30 0, 30 10, 20 10, 20 0))', 4326)),
        ('P-03', NULL, false, ST_GeomFromText('POLYGON((0 20, 10 20, 10 30, 0 30, 0 20))', 4326)); -- 错误1: park_name 为空
        
        INSERT INTO public.facilities (facility_type, geometry) VALUES
        ('Playground', ST_GeomFromText('POINT(5 5)', 4326)),      -- 在 Central Park 内
        ('Restroom', ST_GeomFromText('POINT(25 5)', 4326)),     -- 在 Riverfront Park 内
        ('Playground', ST_GeomFromText('POINT(100 100)', 4326)); -- 错误2: 不在任何公园内的游乐场
                
        
        """
        suite_rules = [    {
        "rule_id": "park_name_must_exist",
        "type": "ATTRIBUTE_NOT_NULL",
        "spec": {
            "layer": "attr_check_demo_v12.parks",
            "id_field": "park_id",
            "attribute": "park_name"
        }
    },
    {
        "rule_id": "playgrounds_must_be_inside_a_park",
        "type": "SPATIAL_RELATIONSHIP",
        "spec": {
            "quantifier": "all",
            "predicate": "within",
            "layer1": "attr_check_demo_v12.facilities",
            "id_field1": "facility_id",
            "where1": "facility_type == 'Playground'",
            "layer2": "public.parks",
            "id_field2": "park_id"
        }
    }]
        resp = post('normalization/quality/logical_consistency/', {
            'suite_rules': suite_rules,
            'db_conn_str': 'postgresql://postgres:postgres@localhost:5432/skgeo-manager',
            'context': None
        })
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_topology_shp(self):
        """测试拓扑关系检查API（shp文件）"""
        testPath = os.path.join(DATA_PATH,'data/geodata/topology/test_shp_poly.shp')
        # testPath = os.path.join(DATA_PATH,'data/geodata/shp/4326/4326.shp')
        # testPath = os.path.join(DATA_PATH,'data/geodata/topology/test_shp.shp')
        resp = post('normalization/quality/topology/', {'file_path': testPath, 'id_field': 'id'})
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_topology_geojson(self):
        """测试拓扑关系检查API（geojson文件）"""
        testPath = os.path.join(DATA_PATH,'data/geodata/topology/test.geojson')
        # testPath = os.path.join(DATA_PATH,'data/geodata/output/output_actual_polygon.geojson')
        resp = post('normalization/quality/topology/', {'file_path': testPath, 'id_field': 'id'})
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_topology_geojson_str(self):
        """测试拓扑关系检查API（geojson字符串）"""
        geojson_str = create_test_geojson_str()
        path = os.path.join(DATA_PATH,'data/geodata/topology/test_poly.geojson')
        with open(path, 'w', encoding='utf-8') as f:
            f.write(geojson_str)
        resp = post('normalization/quality/topology/', {'file_path': path, 'id_field': 'id'})
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_integrity_perfect(self):
        """测试Shapefile数据完整性检查API（完美shp）"""
        testPath = os.path.join(DATA_PATH,'data/geodata/topology/test_shp_poly.shp')
        # resp = post('normalization/quality/integrity/', {'shp_path': testPath, 'required_fields': ['id']})
        resp = post('normalization/quality/integrity/', {'shp_path': testPath, 'required_fields': ['queshiziduan']})
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_integrity_missing_file(self):
        """测试Shapefile数据完整性检查API（缺文件）"""
        # 删除shx文件
        shx_path = os.path.join(DATA_PATH, 'data/geodata/miss/test_shp_missing.shp')
        resp = post('normalization/quality/integrity/', {'shp_path': shx_path, 'required_fields': ['id']})
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_integrity_empty(self):
        """测试Shapefile数据完整性检查API（空数据集）"""
        shx_path = os.path.join(DATA_PATH, 'data/geodata/empty/test_shp_poly.shp')
        resp = post('normalization/quality/integrity/', {'shp_path': shx_path, 'required_fields': ['id']})
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_integrity_invalid_geom(self):
        """测试Shapefile数据完整性检查API（无效几何）"""
        # shx_path = os.path.join(DATA_PATH, 'data/geodata/empty/test_shp_invalid.shp')
        shx_path = os.path.join(DATA_PATH,'data/geodata/shp/4326/4326.shp')
        resp = post('normalization/quality/integrity/', {'shp_path':shx_path, 'required_fields': ['id']})
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_integrity_missing_field(self):
        """测试Shapefile数据完整性检查API（缺字段）"""
        shx_path = os.path.join(DATA_PATH, 'data/geodata/topology/test_shp_poly.shp')
        resp = post('normalization/quality/integrity/', {'shp_path': shx_path, 'required_fields': ['id','ziduanqueshi']})
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_spatial_reference_shp(self):
        """测试空间参考检查API（shp文件）"""
        testPath = os.path.join(DATA_PATH,'data/geodata/topology/test_shp.shp')
        resp = post('normalization/quality/spatial_reference/', {'shp_path': testPath, 'gcs': 'EPSG:4326'})
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_spatial_reference_tif(self):
        """测试空间参考检查API（tif文件）"""
        # tifpath = os.path.join(DATA_PATH, 'data/geodata/ffff.tif')
        # resp = post('normalization/quality/spatial_reference/', {'shp_path': tifpath, 'gcs': 'EPSG:4326','pcs':'EPSG:4547'})
        tifpath = os.path.join(DATA_PATH, 'data/geodata/test_tif.tif')
        resp = post('normalization/quality/spatial_reference/', {'shp_path': tifpath, 'gcs': 'EPSG:4326'})
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_reproject_case1(self):
        """TIF最基本重投影"""
        # tifpath = os.path.join(DATA_PATH, 'data/geodata/test_tif.tif')
        # tifoutpath = os.path.join(DATA_PATH, 'data/geodata/test_tif_out1.tif')

        tifpath = os.path.join(DATA_PATH, 'data/geodata/ffff.tif')
        tifoutpath = os.path.join(DATA_PATH, 'data/geodata/ffff_out1.tif')

        resp = post('normalization/reproject/transform/', {
            'input_path': tifpath,
            'output_path': tifoutpath,
            'dst_crs': 'EPSG:4326'
        })
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_reproject_case2(self):
        """TIF指定源/目标CRS和重采样方法"""
        tifpath = os.path.join(DATA_PATH, 'data/geodata/test_tif.tif')
        tifoutpath = os.path.join(DATA_PATH, 'data/geodata/test_tif_out2.tif')
        resp = post('normalization/reproject/transform/', {
            'input_path': tifpath,
            'output_path': tifoutpath,
            'src_crs': 'EPSG:32648',
            'dst_crs': 'EPSG:3857',
            'resample_method': 'cubic'
        })
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_reproject_case3(self):
        """TIF强制输出单精度"""
        tifpath = os.path.join(DATA_PATH, 'data/geodata/test_tif.tif')
        tifoutpath = os.path.join(DATA_PATH, 'data/geodata/test_tif_out3.tif')
        resp = post('normalization/reproject/transform/', {
            'input_path': tifpath,
            'output_path': tifoutpath,
            'dst_crs': 'EPSG:3857',
            'precision': 'single'
        })
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_reproject_case4(self):
        """TIF七参数转换（高精度）"""
        tifpath = os.path.join(DATA_PATH, 'data/geodata/test_tif.tif')
        tifoutpath = os.path.join(DATA_PATH, 'data/geodata/test_tif_out4.tif')
        seven_params = {
            "dx": 100, "dy": 200, "dz": 50,
            "rx": 0.1, "ry": 0.2, "rz": 0.3,
            "scale": 1.0001
        }
        resp = post('normalization/reproject/transform/', {
            'input_path': tifpath,
            'output_path': tifoutpath,
            'seven_params': seven_params,
            'precision': 'double'
        })
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_reproject_case5(self):
        """GeoJSON文件重投影 输出的文件要支持高并发，所以文件命名要加上时间戳"""
        testPath = os.path.join(DATA_PATH,'data/geodata/topology/test.geojson')
        outpath = os.path.join(DATA_PATH, 'data/geodata/topology/test_out1.geojson')
        resp = post('normalization/reproject/transform/', {
            'input_path': testPath,
            'output_path': outpath,
            'dst_crs': 'EPSG:3857'
        })
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_reproject_case6(self):
        """Shapefile重投影，指定源/目标CRS"""
        testPath = os.path.join(DATA_PATH,'data/geodata/topology/test_shp.shp')
        testOutPath = os.path.join(DATA_PATH,'data/geodata/topology/test_shp_out1.shp')
        resp = post('normalization/reproject/transform/', {
            'input_path': testPath,
            'output_path': testOutPath,
            'src_crs': 'EPSG:4326',
            'dst_crs': 'EPSG:3857'
        })
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_reproject_case7(self):
        """批量TIF重投影"""
        testPath = os.path.join(DATA_PATH,'data/geodata/test_tif.tif')
        testPath2 = os.path.join(DATA_PATH,'data/geodata/test.tif')
        testOutPath = os.path.join(DATA_PATH,'data/geodata/batch_out')
        resp = post('normalization/reproject/batch_transform/', {
            'input_files': [testPath, testPath2],
            'output_dir': testOutPath,
            'dst_crs': 'EPSG:4326'
        })
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_reproject_case8(self):
        """批量TIF七参数重投影"""
        testPath = os.path.join(DATA_PATH,'data/geodata/test_tif.tif')
        testPath2 = os.path.join(DATA_PATH,'data/geodata/test.tif')
        testOutPath = os.path.join(DATA_PATH,'data/geodata/batch_out2')
        seven_params = {
            "dx": 100, "dy": 200, "dz": 50,
            "rx": 0.1, "ry": 0.2, "rz": 0.3,
            "scale": 1.0001
        }
        resp = post('normalization/reproject/batch_transform/', {
            'input_files': [testPath, testPath2],
            'output_dir': testOutPath,
            'seven_params': seven_params,
            'precision': 'double'
        })
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_reproject_case9(self):
        """批量GeoJSON重投影，分别指定源CRS"""
        testPath1 = os.path.join(DATA_PATH,'data/geodata/topology/test.geojson')
        testPath2 = os.path.join(DATA_PATH, 'data/geodata/topology/test_poly.geojson')
        testOutPath = os.path.join(DATA_PATH,'data/geodata/batch_out3')
        resp = post('normalization/reproject/batch_transform/', {
            'input_files': [testPath1, testPath2],
            'output_dir': testOutPath,
            'src_crs': ['EPSG:4326', 'EPSG:32648'],
            'dst_crs': 'EPSG:3857'
        })
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_reproject_case10(self):
        """错误用例：混合类型批量重投影（应返回错误）"""
        testPath1 = os.path.join(DATA_PATH,'data/geodata/test.tif')
        testPath2 = os.path.join(DATA_PATH, 'data/geodata/topology/test_poly.geojson')
        testOutPath = os.path.join(DATA_PATH,'data/geodata/batch_out4')
        resp = post('normalization/reproject/batch_transform/', {
            'input_files': [testPath1, testPath2],
            'output_dir': testOutPath,
            'dst_crs': 'EPSG:4326'
        })
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_topology_db(self):
        """测试拓扑关系检查API（数据库空间表，分批处理）"""
        postgis_conn = "postgresql://postgres:postgres@localhost:5432/skgeo-manager"
        table = "spatial_table"
        schema = 'geodata'
        chunk_size = 10000
        payload = {
            'db_conn_str': postgis_conn,
            'table_name': table,
            'db_schema': schema,  # 改为 schema
            'id_field': 'id',
            'geom_column': 'geometry',
            'chunk_size': chunk_size
        }
        resp = post('normalization/quality/topology_db/', payload)
        self.assertInPrint(resp.status_code, [200, 500])
        self.assertInPrint('status', resp.json())

    def test_spatial_reference_db(self):
        """测试数据库空间参考检查API"""
        from glff.normalization.quality_inspection.spatial_reference_check.spatial_reference_check import SpatialReferenceChecker
        checker = SpatialReferenceChecker(standard_gcs_str="EPSG:4490", standard_pcs_str="EPSG:4547")
        report = checker.check_db(
            db_conn_str="postgresql://postgres:postgres@localhost:5432/skgeo-manager",
            db_schema="geodata",
            table_name="spatial_table",
            geom_column="geometry"
        )
        print(report)
        self.assertIn(report['overall_status'], ['通过', '失败', '错误'])

if __name__ == '__main__':
    unittest.main()