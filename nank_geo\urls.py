"""
URL configuration for nank_geo project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from glff.normalization.service.views import index_view, preprocess_view, quality_view
from nank_geo import settings

schema_view = get_schema_view(
   openapi.Info(
      title="Nank Geo API",
      default_version='v1',
      description="交互式API文档，支持在线测试所有接口",
      terms_of_service="https://www.example.com/terms/",
      contact=openapi.Contact(email="<EMAIL>"),
      license=openapi.License(name="BSD License"),
   ),
   public=True,
   permission_classes=(permissions.AllowAny,),
)

urlpatterns = [
    path('', index_view, name='index'),
    path('preprocess/', preprocess_view, name='preprocess'),
    path('quality/', quality_view, name='quality'),
    path('admin/', admin.site.urls),
    path('glff/', include('glff.urls')),
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
]

# 开发环境下提供媒体文件服务
if settings.DEBUG:
    from django.conf.urls.static import static
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

# 开发环境下提供静态文件和媒体文件服务
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATICFILES_DIRS[0])
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    # 添加lib和webfonts的直接访问路径
    urlpatterns += static('/lib/', document_root=settings.BASE_DIR / 'templates' / 'lib')
    urlpatterns += static('/webfonts/', document_root=settings.BASE_DIR / 'templates' / 'webfonts')
    urlpatterns += static('/quality/', document_root=settings.BASE_DIR / 'templates' / 'quality')
    urlpatterns += static('/preprocess/', document_root=settings.BASE_DIR / 'templates' / 'preprocess')
