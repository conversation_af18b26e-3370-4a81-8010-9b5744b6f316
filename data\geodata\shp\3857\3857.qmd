<!DOCTYPE qgis PUBLIC 'http://mrcc.com/qgis.dtd' 'SYSTEM'>
<qgis version="3.44.0-Solothurn">
  <identifier></identifier>
  <parentidentifier></parentidentifier>
  <language></language>
  <type>dataset</type>
  <title></title>
  <abstract></abstract>
  <links/>
  <dates/>
  <fees></fees>
  <encoding></encoding>
  <crs>
    <spatialrefsys nativeFormat="Wkt">
      <wkt>GEOGCRS["WGS 84",ENSEMBLE["World Geodetic System 1984 ensemble",MEMBER["World Geodetic System 1984 (Transit)"],ME<PERSON>ER["World Geodetic System 1984 (G730)"],<PERSON>MBER["World Geodetic System 1984 (G873)"],<PERSON><PERSON>ER["World Geodetic System 1984 (G1150)"],MEMBER["World Geodetic System 1984 (G1674)"],<PERSON><PERSON>ER["World Geodetic System 1984 (G1762)"],<PERSON><PERSON><PERSON>["World Geodetic System 1984 (G2139)"],ME<PERSON>ER["World Geodetic System 1984 (G2296)"],<PERSON><PERSON><PERSON><PERSON><PERSON>["WGS 84",6378137,298.257223563,<PERSON><PERSON><PERSON><PERSON><PERSON>IT["metre",1]],ENSEMBLEACCURACY[2.0]],PRIMEM["Greenwich",0,ANGLEUNIT["degree",0.0174532925199433]],CS[ellipsoidal,2],AXIS["geodetic latitude (Lat)",north,ORDER[1],ANGLEUNIT["degree",0.0174532925199433]],AXIS["geodetic longitude (Lon)",east,ORDER[2],ANGLEUNIT["degree",0.0174532925199433]],USAGE[SCOPE["Horizontal component of 3D system."],AREA["World."],BBOX[-90,-180,90,180]],ID["EPSG",4326]]</wkt>
      <proj4>+proj=longlat +datum=WGS84 +no_defs</proj4>
      <srsid>3452</srsid>
      <srid>4326</srid>
      <authid>EPSG:4326</authid>
      <description>WGS 84</description>
      <projectionacronym>longlat</projectionacronym>
      <ellipsoidacronym>EPSG:7030</ellipsoidacronym>
      <geographicflag>true</geographicflag>
    </spatialrefsys>
  </crs>
  <extent/>
</qgis>
