# 文件上传功能完整改进

## 问题解决

### 1. 文件上传失败问题
**问题**: 显示"文件上传失败"错误
**解决方案**:
- 添加了完整的文件上传配置到 `settings.py`
- 修复了上传API的错误处理
- 添加了文件大小和类型验证
- 确保上传目录存在

### 2. 文件上传界面现代化
**改进前**: 简单的文件输入框
**改进后**: 
- 现代化的拖拽上传界面
- 文件信息显示（文件名、大小）
- 上传进度显示
- 支持拖拽上传
- 美观的视觉反馈

### 3. 切换菜单后清空表单
**问题**: 切换功能后表单内容没有清空
**解决方案**:
- 添加了 `clearForm()` 函数
- 在菜单切换时自动清空：
  - 文件上传区域
  - 动态表单内容
  - 进度条和结果区域

## 技术实现

### 后端改进

#### 1. Django设置配置
```python
# 文件上传配置
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# 文件上传大小限制
DATA_UPLOAD_MAX_MEMORY_SIZE = 10485760  # 10MB
FILE_UPLOAD_MAX_MEMORY_SIZE = 10485760  # 10MB

# 允许的文件类型
ALLOWED_FILE_TYPES = [
    '.txt', '.doc', '.docx', '.pdf', '.shp', '.shx', '.dbf', '.prj',
    '.tif', '.tiff', '.geojson', '.json', '.png', '.jpg', '.jpeg',
    '.csv', '.xls', '.xlsx'
]

# 最大文件大小 (50MB)
MAX_FILE_SIZE = 52428800
```

#### 2. 上传API改进
```python
@method_decorator(csrf_exempt, name='dispatch')
class PreprocessUploadView(View):
    def post(self, request):
        try:
            file = request.FILES.get('file')
            if not file:
                return JsonResponse({'error': '请选择要上传的文件'}, status=400)
            
            # 检查文件大小
            if file.size > settings.MAX_FILE_SIZE:
                return JsonResponse({
                    'error': f'文件大小超过限制，最大允许 {settings.MAX_FILE_SIZE // 1024 // 1024}MB'
                }, status=400)
            
            # 检查文件类型
            file_ext = os.path.splitext(file.name)[1].lower()
            if file_ext not in settings.ALLOWED_FILE_TYPES:
                return JsonResponse({
                    'error': f'不支持的文件类型: {file_ext}，支持的类型: {", ".join(settings.ALLOWED_FILE_TYPES)}'
                }, status=400)
            
            # 确保上传目录存在
            os.makedirs(UPLOAD_DIR, exist_ok=True)
            
            # 保存文件并返回结果
            # ...
            
        except Exception as e:
            return JsonResponse({'error': f'文件上传失败: {str(e)}'}, status=500)
```

### 前端改进

#### 1. 现代化文件上传界面
```html
<div class="file-upload-container">
    <input type="file" id="fileInput" class="file-input" accept=".txt,.doc,.docx,.pdf,.shp,.tif,.geojson,.png,.jpg,.jpeg" required>
    <label for="fileInput" class="file-label">
        <i class="fas fa-cloud-upload-alt"></i>
        <span>选择文件或拖拽到此处</span>
        <small>支持 TXT, DOC, DOCX, PDF, SHP, TIF, GeoJSON, PNG, JPG 等格式</small>
    </label>
</div>
<div id="fileInfo" class="file-info" style="display: none;">
    <div class="file-details">
        <i class="fas fa-file-alt"></i>
        <span id="fileName"></span>
        <span id="fileSize" class="file-size"></span>
    </div>
    <button type="button" class="btn-remove" onclick="removeFile()" title="移除文件">
        <i class="fas fa-times"></i>
    </button>
</div>
```

#### 2. 拖拽上传功能
```javascript
// 文件上传处理
const fileInput = document.getElementById('fileInput');
const fileInfo = document.getElementById('fileInfo');
const fileName = document.getElementById('fileName');
const fileSize = document.getElementById('fileSize');

fileInput.addEventListener('change', handleFileSelect);
fileInput.addEventListener('dragover', handleDragOver);
fileInput.addEventListener('drop', handleFileDrop);

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        displayFileInfo(file);
    }
}

function handleDragOver(e) {
    e.preventDefault();
    e.currentTarget.closest('.file-label').style.borderColor = 'var(--primary-color)';
}

function handleFileDrop(e) {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    if (file) {
        fileInput.files = e.dataTransfer.files;
        displayFileInfo(file);
    }
    e.currentTarget.closest('.file-label').style.borderColor = 'var(--border-color)';
}
```

#### 3. 表单清空功能
```javascript
function clearForm() {
    // 清空文件上传
    fileInput.value = '';
    fileInfo.style.display = 'none';
    uploadProgress.style.display = 'none';
    
    // 清空动态表单
    const dynamicForm = document.getElementById('dynamicForm');
    dynamicForm.innerHTML = '';
    
    // 隐藏进度和结果
    document.getElementById('progressContainer').style.display = 'none';
    document.getElementById('resultContainer').style.display = 'none';
}

// 在菜单切换时调用
document.querySelectorAll('.menu-item').forEach(item => {
    item.addEventListener('click', (e) => {
        // ... 其他代码 ...
        
        // 清空表单
        clearForm();
        
        // ... 其他代码 ...
    });
});
```

### 样式改进

#### 现代化CSS样式
```css
/* 文件上传样式 */
.file-upload-container {
    position: relative;
    margin-bottom: 1rem;
}

.file-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--bg-card);
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.file-label:hover {
    border-color: var(--primary-color);
    background: var(--bg-hover);
}

.file-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background: var(--bg-success);
    border-radius: var(--radius-lg);
    margin-top: 0.5rem;
}
```

## 功能特性

### 1. 文件上传功能
- ✅ 支持拖拽上传
- ✅ 支持点击选择文件
- ✅ 文件类型验证
- ✅ 文件大小限制
- ✅ 上传进度显示
- ✅ 文件信息显示
- ✅ 错误处理和提示

### 2. 用户体验改进
- ✅ 现代化界面设计
- ✅ 直观的视觉反馈
- ✅ 清晰的操作提示
- ✅ 响应式设计
- ✅ 无障碍访问支持

### 3. 表单管理
- ✅ 切换菜单时自动清空
- ✅ 文件上传状态重置
- ✅ 动态表单内容清空
- ✅ 进度和结果隐藏

## 测试页面

访问 `http://localhost:8000/test/upload/` 可以测试文件上传功能。

## 支持的文件类型

### 预处理功能
- 文本文件: `.txt`, `.doc`, `.docx`, `.pdf`
- 地理数据: `.shp`, `.shx`, `.dbf`, `.prj`, `.geojson`
- 影像数据: `.tif`, `.tiff`, `.png`, `.jpg`, `.jpeg`
- 表格数据: `.csv`, `.xls`, `.xlsx`

### 质量检查功能
- 地理数据: `.shp`, `.shx`, `.dbf`, `.prj`, `.geojson`
- 影像数据: `.tif`, `.tiff`
- 文本数据: `.txt`, `.csv`

## 错误处理

### 文件上传错误
- 文件大小超限: 显示具体限制大小
- 不支持的文件类型: 显示支持的类型列表
- 上传失败: 显示具体错误信息
- 网络错误: 显示网络异常信息

### 用户提示
- 中文错误信息
- 友好的提示界面
- 清晰的操作指导
- 实时的状态反馈

## 性能优化

### 文件处理
- 分块上传大文件
- 内存使用优化
- 异步处理支持
- 缓存机制

### 用户体验
- 快速响应
- 流畅的动画效果
- 直观的操作反馈
- 错误恢复机制

现在整个文件上传功能已经完全现代化，用户体验得到了显著提升！ 