1、建模式
attr_check_demo_v12

2、建表，导入数据
CREATE TABLE "attr_check_demo_v12"."db_final_test_table" (
  "id" float8,
  "is_active" text COLLATE "pg_catalog"."default",
  "created_at" date,
  "uuid_col" text COLLATE "pg_catalog"."default",
  "json_col" text COLLATE "pg_catalog"."default",
  "array_col" text COLLATE "pg_catalog"."default",
  "category" text COLLATE "pg_catalog"."default",
  "db_geom" geometry(GEOMETRY, 4326)
)
;

ALTER TABLE "attr_check_demo_v12"."db_final_test_table" 
  OWNER TO "postgres";

CREATE INDEX "idx_db_final_test_table_db_geom" ON "attr_check_demo_v12"."db_final_test_table" USING gist (
  "db_geom" "public"."gist_geometry_ops_2d"
);





INSERT INTO "attr_check_demo_v12"."db_final_test_table" ("id", "is_active", "created_at", "uuid_col", "json_col", "array_col", "category", "db_geom") VALUES ('101', 'True', '2024-01-01', 'fcbc0bd0-2a67-401c-96cc-ed56401b777e', '{"a": 1}', '1,2', 'X', ST_GeomFromText('POLYGON((0 0, 1 1, 1 0, 0 0))', 4326));
INSERT INTO "attr_check_demo_v12"."db_final_test_table" ("id", "is_active", "created_at", "uuid_col", "json_col", "array_col", "category", "db_geom") VALUES ('102', 'False', '2024-02-02', 'not-a-uuid', 'not-json', 'a,b,c', 'W', ST_GeomFromText('POINT(1 1)', 4326));
INSERT INTO "attr_check_demo_v12"."db_final_test_table" ("id", "is_active", "created_at", "uuid_col", "json_col", "array_col", "category", "db_geom") VALUES ('101', 'not-a-bool', NULL, '978a85cc-f523-4667-81eb-2f2593690317', '{"b": 2}', '3,4', 'Y', ST_GeomFromText('MULTIPOINT((2 2))', 4326));
INSERT INTO "attr_check_demo_v12"."db_final_test_table" ("id", "is_active", "created_at", "uuid_col", "json_col", "array_col", "category", "db_geom") VALUES (NULL, NULL, NULL, NULL, NULL, NULL, 'Y', NULL);
