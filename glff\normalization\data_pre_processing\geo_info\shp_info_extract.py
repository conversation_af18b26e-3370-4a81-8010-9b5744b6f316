import geopandas as gpd
import fiona
from shapely.geometry import Polygon, MultiPolygon, box, mapping
import json
import time
import os
import sys
from fiona.errors import DriverError
import warnings
from pyproj import CRS
import numpy as np
from shapely.ops import unary_union
from pyproj import Transformer

# 忽略特定警告
warnings.filterwarnings("ignore", category=DeprecationWarning, message=".*unary_union.*")


def convert_degrees_to_meters(length_degrees, lat_degrees, lon_degrees):
    """
    将度转换为米
    
    参数:
    length_degrees: 长度（度）
    lat_degrees: 纬度（度）
    lon_degrees: 经度（度）
    
    返回:
    float: 长度（米）
    """
    # 地球半径（米）
    earth_radius = 6371000
    
    # 纬度1度对应的距离（米）
    lat_meters_per_degree = earth_radius * np.pi / 180
    
    # 经度1度对应的距离（米）- 需要根据纬度调整
    lon_meters_per_degree = lat_meters_per_degree * np.cos(np.radians(lat_degrees))
    
    # 根据方向选择使用纬度还是经度的转换
    # 这里简化处理，使用平均转换比例
    avg_meters_per_degree = (lat_meters_per_degree + lon_meters_per_degree) / 2
    
    return length_degrees * avg_meters_per_degree


def convert_square_degrees_to_square_meters(area_square_degrees, lat_degrees, lon_degrees):
    """
    将平方度转换为平方米
    
    参数:
    area_square_degrees: 面积（平方度）
    lat_degrees: 纬度（度）
    lon_degrees: 经度（度）
    
    返回:
    float: 面积（平方米）
    """
    # 地球半径（米）
    earth_radius = 6371000
    
    # 纬度1度对应的距离（米）
    lat_meters_per_degree = earth_radius * np.pi / 180
    
    # 经度1度对应的距离（米）
    lon_meters_per_degree = lat_meters_per_degree * np.cos(np.radians(lat_degrees))
    
    # 平方度转换为平方米
    square_meters_per_square_degree = lat_meters_per_degree * lon_meters_per_degree
    
    return area_square_degrees * square_meters_per_square_degree


def calculate_polygon_attributes(geom):
    """
    计算面要素的空间属性
    
    参数:
    geom: shapely几何对象 (Polygon 或 MultiPolygon)
    
    返回:
    dict: 包含长、宽、面积、形状、角度、角度名称的字典（单位：米、平方米）
    """
    try:
        # 检查几何对象是否有效
        if geom is None or geom.is_empty:
            return {
                'length': 0.0,
                'width': 0.0,
                'skarea': 0.0,
                'sangle': 0.0,
                'sanglename': '无效几何',
                'sshape': '无效几何'
            }
        
        # 处理MultiPolygon
        if isinstance(geom, MultiPolygon):
            # 合并所有多边形
            geom = unary_union(geom)
        
        # 再次检查合并后的几何是否有效
        if geom is None or geom.is_empty:
            return {
                'length': 0.0,
                'width': 0.0,
                'skarea': 0.0,
                'sangle': 0.0,
                'sanglename': '无效几何',
                'sshape': '无效几何'
            }
        
        # 获取几何的中心点用于单位转换
        centroid = geom.centroid
        center_lat = centroid.y
        center_lon = centroid.x
        
        # 计算实际面积（平方度）
        skarea_degrees = geom.area
        
        # 计算最小外接矩形
        min_rect = geom.minimum_rotated_rectangle
        
        # 获取最小外接矩形的坐标点
        coords = list(min_rect.exterior.coords)[:-1]  # 去掉重复的最后一个点
        
        # 计算边长（度）
        lengths_degrees = []
        for i in range(len(coords)):
            p1 = coords[i]
            p2 = coords[(i + 1) % len(coords)]
            length_degrees = np.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
            lengths_degrees.append(length_degrees)
        
        # 长边为length，短边为width（度）
        length_degrees = max(lengths_degrees)
        width_degrees = min(lengths_degrees)
        
        # 转换为米
        length_meters = convert_degrees_to_meters(length_degrees, center_lat, center_lon)
        width_meters = convert_degrees_to_meters(width_degrees, center_lat, center_lon)
        
        # 转换为平方米
        skarea_square_meters = convert_square_degrees_to_square_meters(skarea_degrees, center_lat, center_lon)
        
        # 计算方向角度（最长边的方向）
        max_length_idx = lengths_degrees.index(length_degrees)
        p1 = coords[max_length_idx]
        p2 = coords[(max_length_idx + 1) % len(coords)]
        
        # 计算角度（与正北方向的夹角）
        dx = p2[0] - p1[0]
        dy = p2[1] - p1[1]
        angle_rad = np.arctan2(dx, dy)  # 注意：arctan2(dx, dy)得到的是与y轴（正北）的夹角
        sangle = np.degrees(angle_rad)
        
        # 标准化角度到0-180度范围
        sangle = abs(sangle)
        if sangle > 180:
            sangle = 360 - sangle
        
        # 计算角度名称
        if sangle <= 22.5 or sangle >= 157.5:
            sanglename = '南北'
        elif 67.5 <= sangle <= 112.5:
            sanglename = '东西'
        elif 22.5 < sangle < 67.5:
            sanglename = '东北-西南'
        else:  # 112.5 < sangle < 157.5
            sanglename = '西北-东南'
        
        # 计算形状描述（基于米为单位的长宽比）
        aspect_ratio = length_meters / width_meters if width_meters > 0 else 1
        if aspect_ratio > 2:
            sshape = '长条形'
        elif aspect_ratio < 1.2:
            sshape = '近正方形'
        else:
            sshape = '普通多边形'
        
        return {
            'length': round(length_meters, 2),  # 米
            'width': round(width_meters, 2),    # 米
            'skarea': round(skarea_square_meters, 2),  # 平方米
            'sangle': round(sangle, 2),
            'sanglename': sanglename,
            'sshape': sshape
        }
    except Exception as e:
        # 如果计算失败，返回默认值并继续处理下一个要素
        print(f"警告: 几何要素计算失败，跳过该要素。错误信息: {str(e)}")
        return {
            'length': 0.0,
            'width': 0.0,
            'skarea': 0.0,
            'sangle': 0.0,
            'sanglename': '计算失败',
            'sshape': '计算失败'
        }


def extract_shp_info(shp_path, max_features=10000, convex_hull_only=True, return_all_features=False):
    """
    提取 Shapefile 文件信息并输出 GeoJSON 格式的范围和有效多边形

    参数:
    shp_path: Shapefile 文件路径
    max_features: 最大处理要素数量（大文件限制）
    convex_hull_only: 是否只提取整个数据集的凸包（最小外接多边形）
    return_all_features: 是否返回所有要素的详细信息（默认False，只返回前10个样本）
    """
    result = {
        'metadata': {},
        'rpc_info': None,
        'bounds': {},
        'crs': {},
        'actual_polygons_geojson': None,
        'bbox_geojson': None
    }

    try:
        # 0. 验证文件存在
        if not os.path.exists(shp_path):
            return {"error": f"文件不存在: {shp_path}"}

        # 1. 基础元信息（不加载几何数据，快速获取）
        start_time = time.time()
        file_size = 0
        # 计算所有相关文件大小
        for ext in ['.shp', '.shx', '.dbf', '.prj']:
            file_path = shp_path.replace('.shp', ext)
            if os.path.exists(file_path):
                file_size += os.path.getsize(file_path)

        # 2. 获取要素总数和元数据
        try:
            with fiona.open(shp_path) as src:
                num_features = len(src)
                geometry_type = src.schema['geometry'] if 'geometry' in src.schema else "Unknown"
                crs_info = src.crs if src.crs else "Undefined"
                driver = src.driver

                # 获取属性字段
                columns = list(src.schema['properties'].keys()) if 'properties' in src.schema else []

                # 构建元数据
                result['metadata'] = {
                    'file_path': shp_path,
                    'file_size_mb': round(file_size / (1024 * 1024), 2),
                    'num_features': num_features,
                    'geometry_type': geometry_type,
                    'columns': columns,
                    'crs': str(crs_info),
                    'driver': driver
                }

                # 获取边界框
                if src.bounds:
                    result['bounds'] = {
                        'minx': src.bounds[0],
                        'miny': src.bounds[1],
                        'maxx': src.bounds[2],
                        'maxy': src.bounds[3],
                        'bbox': list(src.bounds)
                    }

                    # 创建边界框的GeoJSON
                    bbox_geom = box(src.bounds[0], src.bounds[1], src.bounds[2], src.bounds[3])
                    result['bbox_geojson'] = {
                        "type": "Feature",
                        "properties": {
                            "name": "Bounding Box",
                            "description": "Extent of the dataset"
                        },
                        "geometry": mapping(bbox_geom)
                    }

                # 获取坐标系信息 - 确保可序列化
                if src.crs:
                    crs_dict = {}
                    # 尝试获取CRS的字符串表示
                    try:
                        if isinstance(src.crs, dict):
                            crs_dict = src.crs
                        else:
                            # 使用pyproj解析CRS
                            pyproj_crs = CRS.from_user_input(src.crs)
                            crs_dict = {
                                'name': pyproj_crs.name,
                                'proj4': pyproj_crs.to_proj4(),
                                'wkt': pyproj_crs.to_wkt(),
                                'epsg': pyproj_crs.to_epsg(),
                                'is_geographic': pyproj_crs.is_geographic,
                                'is_projected': pyproj_crs.is_projected
                            }
                    except Exception as crs_err:
                        crs_dict = {'error': f"CRS解析错误: {str(crs_err)}"}

                    result['crs'] = {
                        'crs': str(src.crs),
                        'crs_data': crs_dict  # 确保这是可序列化的字典
                    }

                # 大文件处理策略
                process_full = num_features <= max_features

                # 3. 处理几何数据（仅当需要凸包时）
                if convex_hull_only:
                    # 读取几何数据
                    if process_full:
                        gdf = gpd.read_file(shp_path)
                    else:
                        # 大文件使用抽样
                        sample_size = min(1000, max(1, int(num_features * 0.01)))
                        gdf = gpd.read_file(shp_path, rows=sample_size)
                        result['metadata']['sample_size'] = sample_size

                    # 新增：为面要素计算空间属性并写入shp
                    if geometry_type in ['Polygon', 'MultiPolygon']:
                        print(f"检测到面要素，开始计算空间属性...")
                        
                        # 计算每个要素的空间属性
                        spatial_attrs = []
                        valid_count = 0
                        error_count = 0
                        
                        for idx, geom in enumerate(gdf.geometry):
                            try:
                                attrs = calculate_polygon_attributes(geom)
                                spatial_attrs.append(attrs)
                                if attrs['sanglename'] not in ['无效几何', '计算失败']:
                                    valid_count += 1
                                else:
                                    error_count += 1
                            except Exception as e:
                                print(f"警告: 处理第 {idx} 个要素时出错: {str(e)}")
                                # 添加默认值，确保列表长度一致
                                spatial_attrs.append({
                                    'length': 0.0,
                                    'width': 0.0,
                                    'skarea': 0.0,
                                    'sangle': 0.0,
                                    'sanglename': '处理失败',
                                    'sshape': '处理失败'
                                })
                                error_count += 1
                        
                        print(f"空间属性计算完成: 成功 {valid_count} 个，失败 {error_count} 个")
                        
                        # 添加新字段到gdf
                        gdf['length'] = [attr['length'] for attr in spatial_attrs]
                        gdf['width'] = [attr['width'] for attr in spatial_attrs]
                        gdf['skarea'] = [attr['skarea'] for attr in spatial_attrs]
                        gdf['sangle'] = [attr['sangle'] for attr in spatial_attrs]
                        gdf['sanglenm'] = [attr['sanglename'] for attr in spatial_attrs]
                        gdf['sshape'] = [attr['sshape'] for attr in spatial_attrs]
                        
                        # 保存回原shp文件
                        try:
                            gdf.to_file(shp_path, driver='ESRI Shapefile')
                            print(f"已成功为 {len(gdf)} 个面要素添加空间属性字段")
                        except Exception as save_err:
                            print(f"保存空间属性到shp文件时出错: {str(save_err)}")
                        
                        # 将空间属性信息添加到返回结果中
                        # 过滤掉无效的要素进行统计
                        valid_attrs = [attr for attr in spatial_attrs if attr['sanglename'] not in ['无效几何', '计算失败', '处理失败']]
                        
                        if valid_attrs:
                            result['spatial_attributes'] = {
                                'total_features': len(spatial_attrs),
                                'valid_features': len(valid_attrs),
                                'error_features': error_count,
                                'attributes_summary': {
                                    'length_range': {
                                        'min': min([attr['length'] for attr in valid_attrs]),
                                        'max': max([attr['length'] for attr in valid_attrs]),
                                        'avg': sum([attr['length'] for attr in valid_attrs]) / len(valid_attrs)
                                    },
                                    'width_range': {
                                        'min': min([attr['width'] for attr in valid_attrs]),
                                        'max': max([attr['width'] for attr in valid_attrs]),
                                        'avg': sum([attr['width'] for attr in valid_attrs]) / len(valid_attrs)
                                    },
                                    'area_range': {
                                        'min': min([attr['skarea'] for attr in valid_attrs]),
                                        'max': max([attr['skarea'] for attr in valid_attrs]),
                                        'avg': sum([attr['skarea'] for attr in valid_attrs]) / len(valid_attrs)
                                    },
                                    'angle_range': {
                                        'min': min([attr['sangle'] for attr in valid_attrs]),
                                        'max': max([attr['sangle'] for attr in valid_attrs]),
                                        'avg': sum([attr['sangle'] for attr in valid_attrs]) / len(valid_attrs)
                                    }
                                },
                                'shape_distribution': {
                                    shape: len([attr for attr in valid_attrs if attr['sshape'] == shape])
                                    for shape in set([attr['sshape'] for attr in valid_attrs])
                                },
                                'orientation_distribution': {
                                    orient: len([attr for attr in valid_attrs if attr['sanglename'] == orient])
                                    for orient in set([attr['sanglename'] for attr in valid_attrs])
                                },
                                'sample_features': [
                                    {
                                        'feature_id': idx,
                                        'fid': gdf.index[idx] if hasattr(gdf, 'index') else idx,
                                        **spatial_attrs[idx]
                                    }
                                    for idx in range(len(spatial_attrs) if return_all_features else min(10, len(spatial_attrs)))
                                ]  # 根据return_all_features参数决定返回所有要素还是样本
                            }
                        else:
                            # 如果没有有效要素，返回基本信息
                            result['spatial_attributes'] = {
                                'total_features': len(spatial_attrs),
                                'valid_features': 0,
                                'error_features': error_count,
                                'message': '所有要素处理失败，无法生成统计信息'
                            }

                    # 4. 创建凸包的GeoJSON
                    if convex_hull_only:
                        try:
                            # 整个数据集的凸包（最小外接多边形）
                            if process_full:
                                # 修复弃用警告：使用 union_all() 替代 unary_union
                                if hasattr(gdf.geometry, 'union_all'):
                                    union_geom = gdf.geometry.union_all()
                                else:
                                    # 兼容旧版本
                                    union_geom = gdf.geometry.unary_union

                                convex_hull = union_geom.convex_hull
                            else:
                                # 大文件使用边界框近似
                                convex_hull = box(*gdf.total_bounds)

                            # 创建GeoJSON特征
                            result['actual_polygons_geojson'] = {
                                "type": "Feature",
                                "properties": {
                                    "name": "Convex Hull",
                                    "is_approximation": not process_full,
                                    "area": round(convex_hull.area, 4),
                                    "centroid": [convex_hull.centroid.x, convex_hull.centroid.y]
                                },
                                "geometry": mapping(convex_hull)
                            }
                        except Exception as geom_err:
                            print(f"警告: 凸包计算失败，跳过凸包生成。错误信息: {str(geom_err)}")
                            result['actual_polygons_geojson'] = None
                            # 不设置result['error']，因为空间属性计算已经成功
        except DriverError:
            return {"error": f"无法打开文件: {shp_path}，可能不是有效的Shapefile"}
        except Exception as fiona_err:
            return {"error": f"Fiona错误: {str(fiona_err)}"}

        # 5. 添加处理信息
        result['metadata']['processing_time_sec'] = round(time.time() - start_time, 2)
        result['metadata']['full_processing'] = process_full if 'process_full' in locals() else False

        # 确保所有数据都是可序列化的
        return make_json_serializable(result)

    except Exception as e:
        return {"error": f"全局错误: {str(e)}"}


def make_json_serializable(data):
    """递归处理数据使其可JSON序列化"""
    if isinstance(data, (str, int, float, bool, type(None))):
        return data
    elif isinstance(data, dict):
        return {k: make_json_serializable(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [make_json_serializable(v) for v in data]
    elif isinstance(data, tuple):
        return tuple(make_json_serializable(v) for v in data)
    elif hasattr(data, '__dict__'):
        return make_json_serializable(data.__dict__)
    else:
        try:
            return str(data)
        except:
            return "不可序列化对象"


def save_geojson(data, filename):
    """将GeoJSON数据保存到文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存GeoJSON出错: {str(e)}")
        return False


if __name__ == "__main__":

    shp_file = r"C:\Users\<USER>\Desktop\测试数据\merge\merge.shp"

    output_dir = os.path.dirname(shp_file)
    base_name = os.path.splitext(os.path.basename(shp_file))[0]

    print(f"处理文件: {shp_file}")
    result = extract_shp_info(shp_file)

    if "error" in result:
        print(f"处理出错: {result['error']}")
        sys.exit(1)

    # 打印元信息
    print("\n元信息:")
    for k, v in result['metadata'].items():
        print(f"  - {k}: {v}")

    # 打印范围信息
    if result.get('bounds'):
        print("\n范围信息:")
        for k, v in result['bounds'].items():
            print(f"  - {k}: {v}")

    # 打印坐标系信息
    if result.get('crs'):
        print("\n坐标系:")
        if isinstance(result['crs'], dict):
            for k, v in result['crs'].items():
                if k == 'crs_data' and isinstance(v, dict):
                    print("  - crs_data:")
                    for subk, subv in v.items():
                        print(f"      - {subk}: {subv}")
                else:
                    print(f"  - {k}: {v}")

    # 保存并打印边界框GeoJSON
    if result.get('bbox_geojson'):
        bbox_file = os.path.join(output_dir, f"{base_name}_bbox.geojson")
        if save_geojson(result['bbox_geojson'], bbox_file):
            print(f"\n边界框GeoJSON已保存到: {bbox_file}")
            # 打印简化的GeoJSON信息
            bbox_geojson = result['bbox_geojson']
            print(f"边界框GeoJSON类型: {bbox_geojson['geometry']['type']}")
            coords = bbox_geojson['geometry']['coordinates'][0]
            print(f"边界框坐标点数: {len(coords)}")
            print(f"边界框示例坐标: {coords[0]} -> {coords[1]}")

    # 保存并打印有效多边形GeoJSON
    if result.get('actual_polygons_geojson'):
        polygon_file = os.path.join(output_dir, f"{base_name}_convex_hull.geojson")
        if save_geojson(result['actual_polygons_geojson'], polygon_file):
            print(f"\n有效多边形GeoJSON已保存到: {polygon_file}")
            # 打印简化的GeoJSON信息
            poly_geojson = result['actual_polygons_geojson']
            print(f"有效多边形类型: {poly_geojson['geometry']['type']}")
            print(f"面积: {poly_geojson['properties']['area']}")
            print(f"中心点: {poly_geojson['properties']['centroid']}")
            if poly_geojson['properties'].get('is_approximation'):
                print("注: 基于边界框的近似值")

    # 保存完整结果
    result_file = os.path.join(output_dir, f"{base_name}_metadata.json")
    try:
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"\n完整结果已保存到: {result_file}")
    except Exception as e:
        print(f"保存完整结果出错: {str(e)}")
        # 尝试保存简化版结果
        simple_result = {
            'metadata': result.get('metadata', {}),
            'error': '完整结果保存失败',
            'error_details': str(e)
        }
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(simple_result, f, ensure_ascii=False, indent=2)
        print(f"简化版结果已保存到: {result_file}")