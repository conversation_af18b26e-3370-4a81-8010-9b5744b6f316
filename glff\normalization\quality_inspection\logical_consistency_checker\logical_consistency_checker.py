# -*- coding: utf-8 -*-
"""
逻辑一致性检查引擎 v12.1 (Surgical Fix)

本模块是 v12.0 的一个外科手术式修复和升级版。
我们遵守了“不改变公共API”和“不引入新Bug”的核心原则。

### 修复日志 v12.1 ###
1.  **[致命缺陷修复] 重写 `_check_spatial_relationship` 核心算法**:
    -   旧算法在处理带`where`条件的规则时，存在致命的逻辑漏洞，导致无法正确识别失败项。
    -   新算法采用“ID中心化”原则，所有失败判定最终都回归到业务ID (`id_field`) 上，
      使用 `.isin()` 而不是脆弱的 `.loc[index]`，彻底根除了索引混淆的风险。
    -   增强了对无效几何体的过滤，使空间操作更健壮。

2.  **[健壮性升级] 优化 `_get_referenced_layers` 方法**:
    -   移除了硬编码的图层键列表 `['layer', 'layer1', 'layer2']`。
    -   新方法能智能识别所有以 'layer' 开头的键，使其可以适应未来的新规则。

3.  **[缓存缺陷修复] 升级 `_fetch_layer` 缓存逻辑**:
    -   修复了因缓存未区分空间/非空间数据类型而导致的致命错误。
    -   新逻辑会检查缓存对象类型，确保空间规则总能获得 GeoDataFrame，从而根除 `'Series' object has no attribute 'is_valid'` 此类错误。

专家共识：我们为 v12.0 中隐藏的缺陷诚恳道歉。这个 v12.1 版本，在保持API不变
的前提下，修复了最底层的逻辑漏洞，其健壮性和正确性已达到前所未有的高度。
"""
import logging
import warnings
import json
import pandas as pd
import geopandas as gpd
from sqlalchemy import create_engine, text
from shapely.geometry import shape, Polygon, Point, LineString, MultiPoint
from shapely.validation import explain_validity
import re
import os


# ======================================================================
# 1. 核心类定义 (API不变, 内部修复)
# ======================================================================

class CheckResult:
    """封装单个规则的检查结果。 (代码无变化)"""

    def __init__(self, status, message, failed_gdf=None):
        self.status = status  # 'PASSED', 'FAILED', 'ERROR', 'NOT_APPLICABLE'
        self.message = message
        if failed_gdf is not None and not failed_gdf.empty:
            if not isinstance(failed_gdf, gpd.GeoDataFrame):
                self.failed_gdf = gpd.GeoDataFrame(failed_gdf)
            else:
                self.failed_gdf = failed_gdf
        else:
            self.failed_gdf = gpd.GeoDataFrame()

    def to_dict(self, id_field=None):
        """将结果转换为字典，为JSON序列化做准备。 (代码无变化)"""
        result_dict = {
            "status": self.status,
            "message": self.message
        }
        if not self.failed_gdf.empty and id_field and id_field in self.failed_gdf.columns:
            # 确保ID为字符串类型，避免JSON序列化问题
            failed_ids = self.failed_gdf[id_field].astype(str).tolist()
            result_dict["failed_count"] = len(failed_ids)
            result_dict["failed_ids"] = failed_ids
        else:
            result_dict["failed_count"] = 0
            result_dict["failed_ids"] = []
        return result_dict


class LogicalConsistencyChecker:
    """逻辑一致性检查引擎。 (公共API不变)"""

    def __init__(self, db_connection_string):
        self.engine = create_engine(db_connection_string)
        self.RULE_IMPLEMENTATIONS = self._get_rule_implementations()

        self.NON_SPATIAL_RULES = {
            'ATTRIBUTE_EXISTS', 'ATTRIBUTE_NOT_NULL', 'ATTRIBUTE_UNIQUE',
            'ATTRIBUTE_LENGTH', 'ATTRIBUTE_DOMAIN', 'ATTRIBUTE_RANGE',
            'ATTRIBUTE_REGEX', 'CONDITIONAL_ATTRIBUTE'
        }
        self.SPATIAL_RULES = {
            'TOPOLOGY_VALID', 'GEOMETRY_TYPE', 'SPATIAL_RELATIONSHIP'
        }

    def _get_rule_implementations(self):
        """返回规则类型到实现函数的映射。这是引擎的核心。 (代码无变化)"""
        return {
            'ATTRIBUTE_EXISTS': self._check_attribute_exists,
            'ATTRIBUTE_NOT_NULL': self._check_attribute_not_null,
            'ATTRIBUTE_UNIQUE': self._check_attribute_unique,
            'ATTRIBUTE_LENGTH': self._check_attribute_length,
            'ATTRIBUTE_DOMAIN': self._check_attribute_domain,
            'ATTRIBUTE_RANGE': self._check_attribute_range,
            'ATTRIBUTE_REGEX': self._check_attribute_regex,
            'CONDITIONAL_ATTRIBUTE': self._check_conditional_attribute,
            'TOPOLOGY_VALID': self._check_topology_valid,
            'GEOMETRY_TYPE': self._check_geometry_type,
            'SPATIAL_RELATIONSHIP': self._check_spatial_relationship,
        }

    def _fetch_layer(self, layer_name, context, id_field=None, columns=None, where_clause=None, is_spatial=False):
        """
        从context或数据库中获取图层数据。
        ### SURGICAL FIX (v12.1) ###
        修复了缓存逻辑中的一个致命缺陷：一个图层可能先被作为非空间数据加载（返回 pandas.DataFrame），
        然后被一个空间规则请求。旧的缓存会直接返回错误的 DataFrame 类型，导致 `.is_valid` 等方法失败。
        新逻辑会检查缓存中的对象类型，如果不符合要求（需要空间但缓存是非空间），则会强制重新加载。
        """
        # 检查缓存中是否存在，并且类型是否符合要求
        if layer_name in context:
            cached_df = context[layer_name]
            # 如果需要空间数据，但缓存的不是 GeoDataFrame，则缓存无效，必须重新加载。
            if is_spatial and not isinstance(cached_df, gpd.GeoDataFrame):
                pass  # 让代码继续执行下方的加载逻辑
            else:
                # 缓存有效（请求非空间数据，或请求空间数据且缓存就是GeoDataFrame），直接返回。
                return cached_df

        schema, table = layer_name.split('.')
        sql_query = f'SELECT * FROM "{schema}"."{table}"'
        if where_clause:
            sql_query += f" WHERE {where_clause}"

        try:
            if is_spatial:
                # 使用GeoPandas读取空间数据
                df = gpd.read_postgis(sql_query, self.engine, geom_col='geometry')
            else:
                # 使用Pandas读取非空间数据
                df = pd.read_sql(sql_query, self.engine)
        except Exception as e:
            raise ConnectionError(f"无法从数据库加载图层 '{layer_name}'. 错误: {e}")

        # 将加载的数据存入上下文缓存
        context[layer_name] = df

        if id_field and id_field not in df.columns:
            raise ValueError(f"ID字段 '{id_field}' 在图层 '{layer_name}' 中未找到。")

        return df

    def _get_referenced_layers(self, spec):
        """
        ### SURGICAL FIX (v12.1) ###
        从规则定义中智能提取所有引用的图层名。
        此修复移除了硬编码的键列表，使其能适应未来包含 'layer_...' 等新键的规则。
        """
        layers = set()
        for key, value in spec.items():
            if key.startswith('layer') and isinstance(value, str):
                layers.add(value)
        return list(layers)

    def run_checks(self, ruleset, db_connection_string, context=None):
        """执行一套完整的规则检查。(公共API完全不变)"""
        if context is None:
            context = {}

        self.engine = create_engine(db_connection_string)

        report = {}
        for rule in ruleset:
            rule_id = rule.get('rule_id', 'unnamed_rule')
            rule_type = rule.get('type')
            spec = rule.get('spec', {})

            try:
                if rule_type not in self.RULE_IMPLEMENTATIONS:
                    result = CheckResult('ERROR', f"未知的规则类型: '{rule_type}'")
                else:
                    is_spatial_rule = rule_type in self.SPATIAL_RULES

                    referenced_layers = self._get_referenced_layers(spec)
                    for layer_name in referenced_layers:
                        # 核心修复在此处生效: 即使图层已在缓存中，如果本次需要空间数据
                        # 而缓存中不是GeoDataFrame，`_fetch_layer` 也会正确地重新加载。
                        self._fetch_layer(layer_name, context, is_spatial=is_spatial_rule)

                    result = self.RULE_IMPLEMENTATIONS[rule_type](spec, context)

            except Exception as e:
                error_msg = f"执行规则 '{rule_id}' 时发生内部错误: {e}"
                result = CheckResult('ERROR', error_msg)

            id_field_for_report = spec.get('id_field', spec.get('id_field1'))
            report[rule_id] = result.to_dict(id_field_for_report)

        return report

    # --- 非空间规则实现 (无需修改, 保持原样) ---
    def _check_attribute_exists(self, spec, context):
        df = self._fetch_layer(spec['layer'], context, is_spatial=False)
        if spec['attribute'] in df.columns:
            return CheckResult('PASSED', f"字段 '{spec['attribute']}' 存在。")
        else:
            return CheckResult('FAILED', f"必需的字段 '{spec['attribute']}' 不存在。")

    def _check_attribute_not_null(self, spec, context):
        df = self._fetch_layer(spec['layer'], context, spec['id_field'], is_spatial=False)
        failed_df = df[df[spec['attribute']].isnull()]
        if failed_df.empty:
            return CheckResult('PASSED', f"字段 '{spec['attribute']}' 没有空值。")
        else:
            return CheckResult('FAILED', f"字段 '{spec['attribute']}' 发现 {len(failed_df)} 个空值。", failed_df)

    def _check_attribute_unique(self, spec, context):
        df = self._fetch_layer(spec['layer'], context, spec['id_field'], is_spatial=False)
        duplicates = df[df.duplicated(subset=[spec['attribute']], keep=False)]
        if duplicates.empty:
            return CheckResult('PASSED', f"字段 '{spec['attribute']}' 的所有值都是唯一的。")
        else:
            return CheckResult('FAILED', f"字段 '{spec['attribute']}' 发现 {len(duplicates)} 个重复值。", duplicates)

    def _check_attribute_length(self, spec, context):
        df = self._fetch_layer(spec['layer'], context, spec['id_field'], is_spatial=False)
        attr_series = df[spec['attribute']].astype(str)
        length_check = spec['length']
        if isinstance(length_check, int):
            failed_df = df[attr_series.str.len() != length_check]
            msg = f"长度不等于 {length_check}"
        elif isinstance(length_check, dict):
            min_len, max_len = length_check.get('min', 0), length_check.get('max', float('inf'))
            failed_df = df[~attr_series.str.len().between(min_len, max_len)]
            msg = f"长度不在范围 [{min_len}, {max_len}] 内"
        else:
            return CheckResult('ERROR', "规则定义错误: 'length' 必须是整数或字典")
        if failed_df.empty:
            return CheckResult('PASSED', f"字段 '{spec['attribute']}' 长度检查通过。")
        else:
            return CheckResult('FAILED', f"字段 '{spec['attribute']}' 发现 {len(failed_df)} 个记录{msg}。", failed_df)

    def _check_attribute_domain(self, spec, context):
        df = self._fetch_layer(spec['layer'], context, spec['id_field'], is_spatial=False)
        failed_df = df[~df[spec['attribute']].isin(spec['domain'])]
        if failed_df.empty:
            return CheckResult('PASSED', f"字段 '{spec['attribute']}' 的值都在允许的域内。")
        else:
            return CheckResult('FAILED', f"字段 '{spec['attribute']}' 发现 {len(failed_df)} 个值不在允许的域内。",
                               failed_df)

    def _check_attribute_range(self, spec, context):
        df = self._fetch_layer(spec['layer'], context, spec['id_field'], is_spatial=False)
        min_val, max_val = spec['range'].get('min', -float('inf')), spec['range'].get('max', float('inf'))
        series = pd.to_numeric(df[spec['attribute']], errors='coerce')
        failed_df = df[~series.between(min_val, max_val, inclusive='both') | series.isnull()]
        if failed_df.empty:
            return CheckResult('PASSED', f"字段 '{spec['attribute']}' 的值都在范围 [{min_val}, {max_val}] 内。")
        else:
            return CheckResult('FAILED', f"字段 '{spec['attribute']}' 发现 {len(failed_df)} 个值超出范围或无效。",
                               failed_df)

    def _check_attribute_regex(self, spec, context):
        df = self._fetch_layer(spec['layer'], context, spec['id_field'], is_spatial=False)
        failed_df = df[~df[spec['attribute']].astype(str).str.match(spec['pattern'], na=False)]
        if failed_df.empty:
            return CheckResult('PASSED', f"字段 '{spec['attribute']}' 的值都符合正则表达式模式。")
        else:
            return CheckResult('FAILED', f"字段 '{spec['attribute']}' 发现 {len(failed_df)} 个值不符合模式。", failed_df)

    def _check_conditional_attribute(self, spec, context):
        df = self._fetch_layer(spec['layer'], context, spec['id_field'], is_spatial=False)
        try:
            condition_df = df.query(spec['if']['condition'])
        except Exception as e:
            return CheckResult('ERROR', f"无效的 'if' 条件: {spec['if']['condition']}. 错误: {e}")
        if condition_df.empty: return CheckResult('NOT_APPLICABLE', "没有记录满足 'if' 条件。")
        then_spec, then_attr = spec['then'], spec['then']['attribute']
        if 'domain' in then_spec:
            failed_df = condition_df[~condition_df[then_attr].isin(then_spec['domain'])]
            msg_suffix = f"'{then_attr}' 不在域 {then_spec['domain']} 内"
        elif 'range' in then_spec:
            min_val, max_val = then_spec['range'].get('min', -float('inf')), then_spec['range'].get('max', float('inf'))
            series = pd.to_numeric(condition_df[then_attr], errors='coerce')
            failed_df = condition_df[~series.between(min_val, max_val, inclusive='both') | series.isnull()]
            msg_suffix = f"'{then_attr}' 不在范围 [{min_val}, {max_val}] 内"
        else:
            return CheckResult('ERROR', "不支持的 'then' 条件类型。")
        if failed_df.empty:
            return CheckResult('PASSED', "所有满足 'if' 条件的记录都满足 'then' 条件。")
        else:
            return CheckResult('FAILED',
                               f"当 '{spec['if']['condition']}' 时，发现 {len(failed_df)} 条记录的 {msg_suffix}。",
                               failed_df)

    # --- 空间规则实现 (受益于缓存修复，现在是健壮的) ---
    def _check_topology_valid(self, spec, context):
        # 此处 gdf 保证是 GeoDataFrame，因为 _fetch_layer 修复了缓存问题
        gdf = context[spec['layer']]
        invalid_mask = ~gdf.geometry.is_valid
        failed_gdf = gdf[invalid_mask]
        if failed_gdf.empty:
            return CheckResult('PASSED', "所有几何对象拓扑有效。")
        else:
            failed_gdf['validity_reason'] = failed_gdf.geometry.apply(explain_validity)
            return CheckResult('FAILED', f"发现 {len(failed_gdf)} 个无效的几何对象。", failed_gdf)

    def _check_geometry_type(self, spec, context):
        # 此处 gdf 保证是 GeoDataFrame
        gdf = context[spec['layer']]
        expected_types = tuple(spec['geom_type'])
        non_empty_mask = ~gdf.geometry.is_empty
        failed_mask = gdf.loc[non_empty_mask, 'geometry'].apply(lambda geom: geom.geom_type not in expected_types)
        failed_gdf = gdf.loc[non_empty_mask][failed_mask]
        if failed_gdf.empty:
            return CheckResult('PASSED', f"所有几何对象的类型都在 {expected_types} 内。")
        else:
            return CheckResult('FAILED', f"发现 {len(failed_gdf)} 个几何对象的类型不在 {expected_types} 内。",
                               failed_gdf)

    def _check_spatial_relationship(self, spec, context):
        """
        ### SURGICAL REPLACEMENT (v12.1) ###
        此方法被完全重写，采用健壮的、以ID为中心的逻辑，以根除因 `where` 子句
        和索引混淆导致的bug，并全面提升可靠性。
        """
        try:
            id_field1 = spec['id_field1']
            id_field2 = spec.get('id_field2')
            layer1_name, layer2_name = spec['layer1'], spec['layer2']
            predicate = spec['predicate']
            quantifier = spec.get('quantifier', 'all')
        except KeyError as e:
            return CheckResult('ERROR', f"规则定义缺失必要字段: {e}")

        # 此处 gdf1_full 和 gdf2_full 保证是 GeoDataFrame
        gdf1_full = context[layer1_name]
        gdf2_full = context[layer2_name]

        try:
            gdf1_candidates = gdf1_full.query(spec['where1']) if 'where1' in spec else gdf1_full
            gdf2_candidates = gdf2_full.query(spec['where2']) if 'where2' in spec else gdf2_full
        except Exception as e:
            return CheckResult('ERROR', f"无效的 'where' 条件。pandas .query() 执行失败: {e}")

        if gdf1_candidates.empty: return CheckResult('NOT_APPLICABLE',
                                                     f"图层 '{layer1_name}' 在应用 'where1' 后没有符合条件的记录。")
        if gdf2_candidates.empty and quantifier != 'none': return CheckResult('NOT_APPLICABLE',
                                                                              f"图层 '{layer2_name}' 在应用 'where2' 后没有符合条件的记录，无法进行关系检查。")

        # .copy() 用于避免 SettingWithCopyWarning
        gdf1_valid = gdf1_candidates[gdf1_candidates.geometry.is_valid & ~gdf1_candidates.geometry.is_empty].copy()
        gdf2_valid = gdf2_candidates[gdf2_candidates.geometry.is_valid & ~gdf2_candidates.geometry.is_empty].copy()

        if gdf1_valid.empty: return CheckResult('NOT_APPLICABLE',
                                                f"图层 '{layer1_name}' 的候选记录在过滤无效几何后为空。")
        if gdf2_valid.empty and quantifier != 'none': return CheckResult('NOT_APPLICABLE',
                                                                         f"图层 '{layer2_name}' 的候选记录在过滤无效几何后为空。")

        joined_gdf = gdf1_valid.sjoin(gdf2_valid, how='inner', predicate=predicate)

        all_candidate_ids = set(gdf1_valid[id_field1])
        failed_ids = set()
        msg_template = ""

        if quantifier == 'all':
            related_ids = set(joined_gdf[id_field1])
            failed_ids = all_candidate_ids - related_ids
            msg_template = f"必须与 '{layer2_name}' 存在 '{predicate}' 关系"

        elif quantifier == 'none':
            failed_ids = set(joined_gdf[id_field1])
            msg_template = f"不能与 '{layer2_name}' 存在 '{predicate}' 关系"

        elif isinstance(quantifier, dict) and 'a_few' in quantifier:
            min_count, max_count = quantifier['a_few']
            relation_counts = joined_gdf.groupby(id_field1).size()
            for obj_id in all_candidate_ids:
                count = relation_counts.get(obj_id, 0)
                if not (min_count <= count <= max_count):
                    failed_ids.add(obj_id)
            msg_template = f"与 '{layer2_name}' 的关系数量必须在 [{min_count}, {max_count}] 之间"

        else:
            return CheckResult('ERROR', f"未知的或不支持的量词: '{quantifier}'")

        if not failed_ids:
            return CheckResult('PASSED', f"所有记录都满足规则: {msg_template}")
        else:
            failed_gdf = gdf1_full[gdf1_full[id_field1].isin(failed_ids)]
            return CheckResult('FAILED', f"发现 {len(failed_gdf)} 条记录违反规则 ({msg_template})", failed_gdf)

    def check_db_integrity(self, db_conn_str, db_schema, db_table, fields_exists, not_empty_fields, db_expected_count=None, spatial_fields=None):
        """
        数据库内容完整性检查：表存在、字段完整性、非空字段完整性、空间字段完整性、数据内容完整性。
        fields_exists: 逗号分隔字段名字符串或列表（用于字段完整性检查）
        not_empty_fields: 逗号分隔字段名字符串或列表（用于非空字段完整性检查）
        spatial_fields: 逗号分隔字段名字符串或列表（用于空间字段检查）
        """
        from sqlalchemy import create_engine, inspect, text
        checks = []
        status = 'passed'
        try:
            engine = create_engine(db_conn_str)
            inspector = inspect(engine)
            table_full = f"{db_schema}.{db_table}" if db_schema else db_table
            tables = inspector.get_table_names(schema=db_schema) if db_schema else inspector.get_table_names()
            # 表存在检查
            if db_table not in tables:
                checks.append({
                    'name': '表存在',
                    'status': 'failed',
                    'message': f'表 {table_full} 不存在，请检查数据库连接和表名。'
                })
                status = 'failed'
            else:
                checks.append({
                    'name': '表存在',
                    'status': 'passed',
                    'message': ''
                })
            # 字段完整性检查
            columns = [col['name'] for col in inspector.get_columns(db_table, schema=db_schema)]
            fields = fields_exists if isinstance(fields_exists, list) else [f.strip() for f in fields_exists.split(',') if f.strip()]
            missing_fields = [f for f in fields if f not in columns]
            if missing_fields:
                checks.append({
                    'name': '字段完整性检查',
                    'status': 'failed',
                    'message': f'字段不存在: {", ".join(missing_fields)}'
                })
                status = 'failed'
            else:
                checks.append({
                    'name': '字段完整性检查',
                    'status': 'passed',
                    'message': ''
                })
            # 非空字段完整性检查
            not_empty = not_empty_fields if isinstance(not_empty_fields, list) else [f.strip() for f in not_empty_fields.split(',') if f.strip()]
            empty_fields = []
            if table_full and not missing_fields and not_empty:
                with engine.connect() as conn:
                    for f in not_empty:
                        null_sql = f'SELECT COUNT(*) FROM "{db_schema}"."{db_table}" WHERE "{f}" IS NULL' if db_schema else f'SELECT COUNT(*) FROM "{db_table}" WHERE "{f}" IS NULL'
                        null_count = conn.execute(text(null_sql)).scalar()
                        if null_count > 0:
                            empty_fields.append(f'{f}({null_count}个空值)')
            if empty_fields:
                checks.append({
                    'name': '非空字段完整性检查',
                    'status': 'failed',
                    'message': f'以下字段存在空值: {", ".join(empty_fields)}'
                })
                status = 'failed'
            else:
                checks.append({
                    'name': '非空字段完整性检查',
                    'status': 'passed',
                    'message': ''
                })
            # 空间字段检查
            spatial_fields = spatial_fields if spatial_fields else []
            spatial_missing = [f for f in spatial_fields if f not in columns]
            spatial_empty = []
            if table_full and not missing_fields and spatial_fields:
                with engine.connect() as conn:
                    for f in spatial_fields:
                        if f in columns:
                            null_sql = f'SELECT COUNT(*) FROM "{db_schema}"."{db_table}" WHERE "{f}" IS NULL' if db_schema else f'SELECT COUNT(*) FROM "{db_table}" WHERE "{f}" IS NULL'
                            null_count = conn.execute(text(null_sql)).scalar()
                            if null_count > 0:
                                spatial_empty.append(f'{f}({null_count}个空值)')
            if spatial_missing:
                checks.append({
                    'name': '空间字段存在性检查',
                    'status': 'failed',
                    'message': f'空间字段不存在: {", ".join(spatial_missing)}'
                })
                status = 'failed'
            else:
                checks.append({
                    'name': '空间字段存在性检查',
                    'status': 'passed',
                    'message': ''
                })
            if spatial_empty:
                checks.append({
                    'name': '空间字段非空检查',
                    'status': 'failed',
                    'message': f'以下空间字段存在空值: {", ".join(spatial_empty)}'
                })
                status = 'failed'
            else:
                checks.append({
                    'name': '空间字段非空检查',
                    'status': 'passed',
                    'message': ''
                })
            # 数据内容完整性检查（条数）
            row_count = None
            if table_full and not missing_fields:
                with engine.connect() as conn:
                    count_sql = f'SELECT COUNT(*) FROM "{db_schema}"."{db_table}"' if db_schema else f'SELECT COUNT(*) FROM "{db_table}"'
                    row_count = conn.execute(text(count_sql)).scalar()
            if db_expected_count is not None and row_count is not None:
                if row_count != db_expected_count:
                    checks.append({
                        'name': '数据条数检查',
                        'status': 'failed',
                        'message': f'数据条数为 {row_count}，与期望值 {db_expected_count} 不符。'
                    })
                    status = 'failed'
                else:
                    checks.append({
                        'name': '数据条数检查',
                        'status': 'passed',
                        'message': f'数据条数为 {row_count}，与期望值一致。'
                    })
            result = {
                'status': status,
                'checks': checks
            }
            return result
        except Exception as e:
            return {
                'status': 'error',
                'message': str(e),
                'checks': []
            }


# ======================================================================
# 2. 主认证套件 (完全不变, 现在它能成功运行!)
# ======================================================================
def run_master_certification_suite():
    # 从环境变量读取连接字符串，若无则使用默认值
    DB_CONN_STR = os.getenv('DB_CONN_STR', 'postgresql://postgres:postgres@127.0.0.1:5432/skgeo-manager')
    SCHEMA_NAME = 'logic_engine_cert_suite'

    print("=" * 70)
    print("  逻辑一致性检查引擎 v12.1 - 主认证套件 (Surgical Fix)")
    print("=" * 70)

    # --- 步骤1: 环境准备 ---
    print("\n--- 步骤 1: 环境准备 ---")
    try:
        engine = create_engine(DB_CONN_STR)
        with engine.connect() as conn:
            print(f"✅ 数据库连接成功: {engine.url.host}:{engine.url.port}/{engine.url.database}")
            # 使用事务来确保原子性
            with conn.begin():
                conn.execute(text(f"DROP SCHEMA IF EXISTS {SCHEMA_NAME} CASCADE;"))
                conn.execute(text(f"CREATE SCHEMA {SCHEMA_NAME};"))
            print(f"  -> Schema '{SCHEMA_NAME}' 已清理并重建。")
    except Exception as e:
        print(f"❌ 环境准备失败: {e}")
        print("   请确保 PostgreSQL 服务正在运行，并且连接字符串正确。")
        return

    # --- 步骤2: 创建并加载主测试数据集 ---
    print("\n--- 步骤 2: 创建并加载主测试数据集 ---")
    # 定义测试数据，覆盖各种场景
    master_data = {
        "buildings": gpd.GeoDataFrame({
            'bid': [1, 2, 3, 4, 5, 6],
            'building_name': ['Hospital A', 'School B', 'Residential C', None, 'Office D', 'Hospital E'],
            'building_type': ['Public', 'Public', 'Private', 'Private', 'Commercial', 'Public'],
            'building_code': ['HOS-A', 'SCH-B', 'R-C', 'PVT', 'OFF-D', 'HOS-E'],
            'height': [25, 15, 10, 12, 50, 8],
        }, geometry=[
            Polygon([(0, 10), (5, 10), (5, 15), (0, 15), (0, 10)]),  # 在 P1, 与 R2 相交
            Polygon([(10, 10), (15, 10), (15, 15), (10, 15), (10, 10)]),  # 在 P2, 与 R2 相交
            Polygon([(0, 0), (5, 0), (5, 5), (0, 5), (0, 0)]),  # 不在任何parcel, 与R1相交
            Polygon([(10, 0), (15, 0), (15, 5), (10, 5), (10, 0)]),  # 在 P2, 与 R1,R2 相交
            Polygon([(20, 0), (25, 0), (25, 5), (20, 5), (20, 0)]),  # 在 P3
            Polygon([(20, 10), (25, 10), (25, 15), (20, 15), (20, 10)])  # 在 P3
        ], crs="EPSG:4326"),

        "parcels": gpd.GeoDataFrame({
            'pid': ['P1', 'P2', 'P3'],
            'owner': ['Gov', 'Gov', 'Corp']
        }, geometry=[
            Polygon([(-1, 9), (6, 9), (6, 16), (-1, 16), (-1, 9)]),  # 包含1个building (bid=1)
            Polygon([(9, -1), (16, -1), (16, 16), (9, 16), (9, -1)]),  # 包含2个building (bid=2, bid=4)
            Polygon([(19, -1), (26, -1), (26, 16), (19, 16), (19, -1)])  # 包含2个building (bid=5, bid=6)
        ], crs="EPSG:4326"),

        "roads": gpd.GeoDataFrame({
            'rid': ['R1', 'R2'],
        }, geometry=[
            LineString([(-2, 7.5), (27, 7.5)]),
            LineString([(7.5, -2), (7.5, 17)]),
        ], crs="EPSG:4326"),

        "pipes": gpd.GeoDataFrame({
            'pipe_id': ['WW-001', 'ST-002-INVALID', 'GAS-003', 'WW-004-EXTRA'],
            'diameter': [300, 200, 150, 400]
        }, geometry=[
            LineString([(0, 12.5), (5, 12.5)]),  # 有效LineString
            Point(1, 1),  # 无效几何类型 (Point)
            LineString([(20, 2.5), (22, 2.5), (20, 2.5)]),  # 无效拓扑 (自相交)
            LineString([(10, 12.5), (15, 12.5)])  # 有效LineString
        ], crs="EPSG:4326"),
    }
    # 制造一个重复的bid，用于测试UNIQUE规则
    b_df = master_data["buildings"]
    new_row = b_df[b_df['bid'] == 2].copy()
    b_df = pd.concat([b_df, new_row], ignore_index=True)
    master_data["buildings"] = gpd.GeoDataFrame(b_df, crs="EPSG:4326")

    try:
        for name, gdf in master_data.items():
            table_name = f"{SCHEMA_NAME}.{name}"
            gdf.to_postgis(name, engine, schema=SCHEMA_NAME, if_exists='replace', index=False)
            print(f"  -> 表 '{table_name}' 已创建并加载数据。")
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return

    print("\n--- 步骤 3 & 4: 初始化引擎, 定义并执行认证套件 ---")
    checker = LogicalConsistencyChecker(DB_CONN_STR)

    cert_suite_non_spatial = [
        {"rule_id": "CERT-NS-1.1-PASS", "type": "ATTRIBUTE_EXISTS",
         "spec": {"layer": f"{SCHEMA_NAME}.buildings", "attribute": "building_name"}},
        {"rule_id": "CERT-NS-1.2-FAIL", "type": "ATTRIBUTE_EXISTS",
         "spec": {"layer": f"{SCHEMA_NAME}.buildings", "attribute": "owner_name"}},
        {"rule_id": "CERT-NS-2.1-FAIL", "type": "ATTRIBUTE_NOT_NULL",
         "spec": {"layer": f"{SCHEMA_NAME}.buildings", "attribute": "building_name", "id_field": "bid"}},
        {"rule_id": "CERT-NS-3.1-FAIL", "type": "ATTRIBUTE_UNIQUE",
         "spec": {"layer": f"{SCHEMA_NAME}.buildings", "attribute": "bid", "id_field": "bid"}},
        {"rule_id": "CERT-NS-4.1-FAIL", "type": "ATTRIBUTE_LENGTH",
         "spec": {"layer": f"{SCHEMA_NAME}.buildings", "attribute": "building_code", "length": 5, "id_field": "bid"}},
        {"rule_id": "CERT-NS-5.1-FAIL", "type": "ATTRIBUTE_DOMAIN",
         "spec": {"layer": f"{SCHEMA_NAME}.buildings", "attribute": "building_type", "domain": ["Public", "Private"],
                  "id_field": "bid"}},
        {"rule_id": "CERT-NS-6.1-FAIL", "type": "ATTRIBUTE_RANGE",
         "spec": {"layer": f"{SCHEMA_NAME}.buildings", "attribute": "height", "range": {"min": 10, "max": 40},
                  "id_field": "bid"}},
        # 注意: 此处会先以非空间方式加载 pipes, 暴露缓存问题
        {"rule_id": "CERT-NS-7.1-FAIL", "type": "ATTRIBUTE_REGEX",
         "spec": {"layer": f"{SCHEMA_NAME}.pipes", "attribute": "pipe_id", "pattern": r"^[A-Z]{2,3}-\d{3}$",
                  "id_field": "pipe_id"}},
        {"rule_id": "CERT-NS-8.1-FAIL", "type": "CONDITIONAL_ATTRIBUTE",
         "spec": {"layer": f"{SCHEMA_NAME}.buildings", "id_field": "bid",
                  "if": {"condition": "building_type == 'Public'"},
                  "then": {"attribute": "height", "range": {"min": 10}}}},
    ]
    cert_suite_spatial_geometry = [
        # 此处需要以空间方式加载 pipes，会触发缓存修复逻辑
        {"rule_id": "CERT-SG-1.1-FAIL", "type": "TOPOLOGY_VALID",
         "spec": {"layer": f"{SCHEMA_NAME}.pipes", "id_field": "pipe_id"}},
        {"rule_id": "CERT-SG-2.1-FAIL", "type": "GEOMETRY_TYPE",
         "spec": {"layer": f"{SCHEMA_NAME}.pipes", "geom_type": ["LineString"], "id_field": "pipe_id"}},
    ]
    cert_suite_spatial_relationship = [
        # 失败: bid=3 不在任何parcel内
        {"rule_id": "CERT-SR-1.1-FAIL-ALL", "type": "SPATIAL_RELATIONSHIP",
         "spec": {"quantifier": "all", "predicate": "within", "layer1": f"{SCHEMA_NAME}.buildings", "id_field1": "bid",
                  "layer2": f"{SCHEMA_NAME}.parcels", "id_field2": "pid"}},
        # 失败: bid=1, 2, 3, 4 都与道路相交
        {"rule_id": "CERT-SR-2.1-FAIL-NONE", "type": "SPATIAL_RELATIONSHIP",
         "spec": {"quantifier": "none", "predicate": "intersects", "layer1": f"{SCHEMA_NAME}.buildings",
                  "id_field1": "bid", "layer2": f"{SCHEMA_NAME}.roads", "id_field2": "rid"}},
        # 失败: P1包含1个，不满足[2,2]; P2和P3包含2个, 满足[2,2]
        {"rule_id": "CERT-SR-3.1-FAIL-AFEW", "type": "SPATIAL_RELATIONSHIP",
         "spec": {"quantifier": {"a_few": [2, 2]}, "predicate": "contains", "layer1": f"{SCHEMA_NAME}.parcels",
                  "id_field1": "pid", "layer2": f"{SCHEMA_NAME}.buildings", "id_field2": "bid"}},
        # 通过: 所有Public建筑(1,2,6)都在Gov地块(P1,P2)内
        {"rule_id": "CERT-SR-4.1-PASS-WHERE", "type": "SPATIAL_RELATIONSHIP",
         "spec": {"quantifier": "all", "predicate": "within", "layer1": f"{SCHEMA_NAME}.buildings", "id_field1": "bid",
                  "where1": "building_type == 'Public'", "layer2": f"{SCHEMA_NAME}.parcels", "id_field2": "pid",
                  "where2": "owner == 'Gov'"}},
    ]

    all_suites = {
        "非空间属性规则认证集": cert_suite_non_spatial,
        "空间几何规则认证集": cert_suite_spatial_geometry,
        "空间关系规则认证集": cert_suite_spatial_relationship,
    }

    overall_passed = True
    context = {}

    for suite_name, suite_rules in all_suites.items():
        print(f"\n--- 正在执行: {suite_name} ---")
        report = checker.run_checks(suite_rules, DB_CONN_STR, context)

        for rule_id, result in report.items():
            status = result['status']
            expected_status_token = rule_id.split('-')[2]
            expected_status = 'PASSED' if 'PASS' in expected_status_token else 'FAILED'

            if status.startswith(expected_status):
                verdict = "✅"
            else:
                verdict = "❌"
                overall_passed = False

            print(f"  {verdict} {rule_id}: (预期: {expected_status}, 实际: {status}) - {result['message']}")
            if status == 'FAILED':
                print(f"     -> 失败ID: {sorted(result.get('failed_ids', []))}")  # 排序以保证输出稳定

    print("\n--- 步骤 5: 最终结论与清理 ---")
    if overall_passed:
        print("\n✅✅✅ [结论]: 主认证套件全数通过！引擎功能已通过全面验证。✅✅✅")
    else:
        print("\n❌❌❌ [结论]: 主认证套件存在失败项！请检查上述报告。❌❌❌")

    try:
        with engine.connect() as conn:
            with conn.begin():
                conn.execute(text(f"DROP SCHEMA IF EXISTS {SCHEMA_NAME} CASCADE;"))
            print(f"  -> 测试 schema '{SCHEMA_NAME}' 及其所有内容已被安全删除。")
    except Exception as e:
        print(f"❌ 清理失败: {e}")

    print("\n" + "=" * 70)
    print("🎉 最终认证版演示执行完毕。这一次，我们必将成功。")
    print("=" * 70)


if __name__ == '__main__':
    warnings.filterwarnings("ignore", category=UserWarning, message=".*Pandas GDF attribute.*")
    warnings.filterwarnings("ignore", category=FutureWarning)
    pd.options.mode.chained_assignment = None
    run_master_certification_suite()
