import io
import logging
import math
import os
from PIL import Image
from django.http import HttpResponse
from pyproj import Transformer
from rest_framework.response import Response
from rest_framework.views import APIView
from osgeo import gdal, osr
import numpy as np
from concurrent.futures import ThreadPoolExecutor

from glff.dao.geo_file.geo_file_detail import GeoFileDetail
from nank_geo import settings

# 启用异常模式
gdal.UseExceptions()

# 创建一个全局的线程池，设置较小的线程数
executor = ThreadPoolExecutor(max_workers=8)  # 根据实际情况调整


class TiffDyView(APIView):
    """
    处理 TIFF 文件的动态瓦片请求的视图类。

    该类负责接收请求，处理 TIFF 文件，生成瓦片，并返回给客户端。
    支持动态投影，可以根据请求中的 EPSG 代码进行坐标转换。
    目前支持 EPSG:3857（Web Mercator）、EPSG:4326（WGS 84）和 EPSG:4490（中国大地2000）等投影。
    通过动态计算瓦片的行列索引，确保生成的瓦片符合请求的投影坐标系。
    """

    def get(self, request, FILEID,EPSGCODE, TileMatrix, TileCol, TileRow):
        """
        处理 GET 请求，生成指定瓦片并返回。

        参数:
        - request: HTTP 请求对象
        - EPSGCODE: 目标坐标系的 EPSG 代码
        - TileMatrix: 瓦片矩阵级别
        - TileCol: 瓦片列号
        - TileRow: 瓦片行号

        返回:
        - HttpResponse: 返回生成的瓦片或错误信息
        """
        try:
            tile_matrix = int(TileMatrix)
            tile_col = int(TileCol)
            tile_row = int(TileRow)
            file_id = str(FILEID)
        except ValueError:
            return Response({"error": "TileMatrix, TileCol, TileRow 参数错误"}, status=400)

        jfs_path = settings.JFS_PATH
        file_detail = GeoFileDetail.get_by_id(file_id)
        tiff_path = os.path.join(jfs_path,file_detail.base_path,file_detail.path,file_detail.filename)

        # tiff_path = r"G:\testcode\ceshitiff\aaa\world.tif"
        if not os.path.exists(tiff_path):
            logging.warning({"error": f"指定的 {tiff_path} TIFF 文件不存在"});
            return Response({"error": "指定的 TIFF 文件不存在"}, status=404)

        # 打开 TIFF 文件
        dataset = gdal.Open(tiff_path, gdal.GA_ReadOnly)
        if not dataset:
            logging.warning({"error": "{tiff_path}文件未找到"});
            return Response({"error": "文件未找到"}, status=404)

        # 获取 TIFF 文件的有效范围和投影信息
        geo_transform = dataset.GetGeoTransform()
        min_x = geo_transform[0]
        max_y = geo_transform[3]
        max_x = min_x + geo_transform[1] * dataset.RasterXSize
        min_y = max_y + geo_transform[5] * dataset.RasterYSize

        # 获取 TIFF 文件的 EPSG 代码
        projection = dataset.GetProjection()
        srs = osr.SpatialReference()
        srs.ImportFromWkt(projection)
        source_epsg_code = srs.GetAttrValue('AUTHORITY', 1)  # 获取 EPSG 代码
        print(f"当前tif文件坐标系为：{source_epsg_code}")

        # 根据传入的 EPSG 代码选择目标坐标系
        dst_srs = osr.SpatialReference()
        try:
            epsg_code_int = int(EPSGCODE)  # 将传入的 EPSGCODE 转换为整数
            dst_srs.ImportFromEPSG(epsg_code_int)  # 使用整数形式的 EPSG 代码
            coordinate_system = EPSGCODE
        except Exception as e:
            return Response({"error": "不支持的投影类型"}, status=400)

        # 生成瓦片的缓存路径，按照 TMS 结构和 EPSG 代码
        tile_cache_dir = os.path.join(os.path.dirname(tiff_path), "tile_cache", str(EPSGCODE))  # 修改为 str(EPSGCODE)
        tile_cache_path = os.path.join(tile_cache_dir, str(tile_matrix), str(tile_col), f"{tile_row}.png")

        # 优先检查缓存
        if os.path.exists(tile_cache_path):
            print(f"从缓存中返回瓦片: {tile_cache_path}")
            with open(tile_cache_path, 'rb') as f:
                return HttpResponse(f.read(), content_type='image/png')

        # 计算瓦片边界
        tile_size = 256
        x_min, y_max, x_max, y_min = self.calculate_tile_bounds(tile_matrix, tile_col, tile_row, tile_size, epsg_code_int)

        # 将 TIFF 文件的有效范围转换为瓦片的坐标系
        min_x_transformed, min_y_transformed, max_x_transformed, max_y_transformed = self.transform_bounds(
            min_x, min_y, max_x, max_y, int(source_epsg_code), epsg_code_int
        )

        # 检查瓦片边界是否在 TIFF 文件的有效范围内
        if (x_min > max_x_transformed or x_max < min_x_transformed or
                y_min > max_y_transformed or y_max < min_y_transformed):
            print("瓦片超出有效范围，返回空瓦片。")
            return self.generate_empty_tile()

        # 直接重投影数据集
        reprojected_dataset = gdal.Warp('', dataset, format='MEM', dstSRS=dst_srs)

        if not reprojected_dataset:
            return Response({"error": "重投影失败"}, status=500)

        # 使用线程池生成瓦片
        future = executor.submit(self.create_tile, reprojected_dataset, x_min, y_max, x_max, y_min, tile_size)
        tile_image = future.result()

        if tile_image is None:
            return Response({"error": "提取瓦片失败"}, status=500)

        # 确保无效区域透明
        no_data_value = 0  # 根据您的数据设置无效值
        tile_image = self.ensure_transparency(tile_image, no_data_value)

        # 检查图像是否为空
        if self.is_image_empty(tile_image):
            print("生成的瓦片为空，不保存到缓存。")
            return self.generate_empty_tile()

        # 确保目录存在
        os.makedirs(os.path.dirname(tile_cache_path), exist_ok=True)

        # 将瓦片保存到文件系统
        executor.submit(self.save_tile_to_file, tile_cache_path, tile_image)

        # 返回生成的瓦片
        img_byte_arr = io.BytesIO()
        tile_image.save(img_byte_arr, format='PNG')
        img_byte_arr.seek(0)
        return HttpResponse(img_byte_arr.getvalue(), content_type='image/png')

    def transform_bounds(self,x_min, y_min, x_max, y_max, source_epsg_code, target_epsg_code):
        """
        将边界坐标从源坐标系转换到目标坐标系。

        参数:
        - x_min: 最小 x 坐标
        - y_min: 最小 y 坐标
        - x_max: 最大 x 坐标
        - y_max: 最大 y 坐标
        - source_epsg_code: 源坐标系的 EPSG 代码
        - target_epsg_code: 目标坐标系的 EPSG 代码

        返回:
        - (min_x_transformed, min_y_transformed, max_x_transformed, max_y_transformed): 转换后的边界坐标
        """
        try:
            # 创建坐标转换器
            transformer = Transformer.from_crs(source_epsg_code, target_epsg_code, always_xy=True)

            # 计算 Web 墨卡托投影的最大纬度限制
            WEB_MERCATOR_MAX_LAT = math.degrees(math.atan(math.sinh(math.pi)))
            logging.info(f"Web Mercator 最大纬度限制: {WEB_MERCATOR_MAX_LAT}")

            # 如果目标坐标系是 Web 墨卡托投影，则裁剪纬度值
            if target_epsg_code == 3857:
                logging.info("目标坐标系为 Web Mercator (EPSG:3857)，裁剪纬度范围。")
                y_min = max(y_min, -WEB_MERCATOR_MAX_LAT)
                y_max = min(y_max, WEB_MERCATOR_MAX_LAT)
                logging.info(f"裁剪后的纬度范围: y_min={y_min}, y_max={y_max}")

            # 转换四个边界点以确保完全覆盖
            points = [
                (x_min, y_min),
                (x_max, y_max),
                (x_min, y_max),
                (x_max, y_min)
            ]

            transformed_points = []
            for x, y in points:
                try:
                    tx, ty = transformer.transform(x, y)
                    transformed_points.append((tx, ty))
                except Exception as e:
                    if "latitude" in str(e):
                        # 强制限制到有效范围后重试
                        y_clipped = max(min(y, WEB_MERCATOR_MAX_LAT), -WEB_MERCATOR_MAX_LAT)
                        tx, ty = transformer.transform(x, y_clipped)
                        transformed_points.append((tx, ty))
                        logging.warning(f"纬度值 {y} 超出范围，已裁剪为 {y_clipped}")
                    else:
                        raise

            # 提取所有转换后的坐标极值
            min_x = min(p[0] for p in transformed_points)
            min_y = min(p[1] for p in transformed_points)
            max_x = max(p[0] for p in transformed_points)
            max_y = max(p[1] for p in transformed_points)

            logging.info(f"转换后的边界坐标: min_x={min_x}, min_y={min_y}, max_x={max_x}, max_y={max_y}")

            return min_x, min_y, max_x, max_y

        except Exception as e:
            logging.error(f"坐标转换失败: {e}")
            raise RuntimeError(f"坐标转换失败: {e}")

    def save_tile_to_file(self, tile_cache_path, tile_image):
        """
        将瓦片保存到文件系统。

        参数:
        - tile_cache_path: 瓦片缓存路径
        - tile_image: 要保存的瓦片图像
        """
        tile_image.save(tile_cache_path, format='PNG')
        print(f"瓦片已保存到: {tile_cache_path}")

    def is_image_empty(self, image):
        """
        检查图像是否为空（全透明）。

        参数:
        - image: 要检查的图像

        返回:
        - bool: 如果图像为空返回 True，否则返回 False
        """
        data = np.array(image)
        return np.all(data[:, :, 3] == 0)  # 检查 alpha 通道是否全为 0

    def calculate_tile_bounds(self, tile_matrix, tile_col, tile_row, tile_size, epsg_code):
        """
        计算给定级别、列和行的瓦片边界。

        参数:
        - tile_matrix: 瓦片矩阵级别
        - tile_col: 瓦片列号
        - tile_row: 瓦片行号
        - tile_size: 瓦片大小
        - epsg_code: EPSG 代码

        返回:
        - (x_min, y_max, x_max, y_min): 瓦片的边界坐标
        """
        if epsg_code == 3857:
            origin_x, origin_y = -20037508.342789244, 20037508.342789244
            initial_resolution = 2 * 20037508.342789244 / tile_size
            resolution = initial_resolution / (2 ** tile_matrix)
            x_min = origin_x + tile_col * tile_size * resolution
            y_max = origin_y - tile_row * tile_size * resolution
            x_max = x_min + tile_size * resolution
            y_min = y_max - tile_size * resolution
        elif epsg_code == 4326:
            initial_resolution = 360.0 / tile_size
            resolution = initial_resolution / (2 ** tile_matrix)
            x_min = -180 + tile_col * tile_size * resolution
            y_max = 90 - tile_row * tile_size * resolution
            x_max = x_min + tile_size * resolution
            y_min = y_max - tile_size * resolution
        elif epsg_code == 4490:
            initial_resolution = 360.0 / tile_size  # 这里假设与 WGS 84 相同
            resolution = initial_resolution / (2 ** tile_matrix)
            x_min = -180 + tile_col * tile_size * resolution
            y_max = 90 - tile_row * tile_size * resolution
            x_max = x_min + tile_size * resolution
            y_min = y_max - tile_size * resolution
        else:
            raise Exception(f"不支持的坐标系: {epsg_code}")

        return x_min, y_max, x_max, y_min

    def create_tile(self, reprojected_dataset, x_min, y_max, x_max, y_min, tile_size):
        """
        从重投影的数据集中创建瓦片。

        参数:
        - reprojected_dataset: 重投影后的数据集
        - x_min: 瓦片的最小 x 坐标
        - y_max: 瓦片的最大 y 坐标
        - x_max: 瓦片的最大 x 坐标
        - y_min: 瓦片的最小 y 坐标
        - tile_size: 瓦片大小

        返回:
        - Image: 生成的瓦片图像
        """
        tile_dataset = gdal.Translate(
            '',
            reprojected_dataset,
            format='MEM',
            projWin=[x_min, y_max, x_max, y_min],
            width=tile_size,
            height=tile_size,
            outputType=gdal.GDT_Byte
        )
        if not tile_dataset:
            return None

        band_count = tile_dataset.RasterCount
        bands_data = [tile_dataset.GetRasterBand(i + 1).ReadAsArray() for i in range(band_count)]

        if band_count == 3:
            image = Image.merge('RGB', [Image.fromarray(band) for band in bands_data])
            image = image.convert("RGBA")
        elif band_count == 4:
            image = Image.merge('RGBA', [Image.fromarray(band) for band in bands_data])
        elif band_count == 1:
            gray_band = Image.fromarray(bands_data[0])
            image = Image.merge('RGB', [gray_band, gray_band, gray_band])
            image = image.convert("RGBA")
        else:
            raise Exception(f"不支持的波段数: {band_count}")

        return image

    def generate_empty_tile(self):
        """
        生成一个空的透明瓦片。

        返回:
        - HttpResponse: 返回空的瓦片图像
        """
        tile_size = 256
        empty_tile = Image.new("RGBA", (tile_size, tile_size), (0, 0, 0, 0))  # 创建透明瓦片
        img_byte_arr = io.BytesIO()
        empty_tile.save(img_byte_arr, format='PNG')
        img_byte_arr.seek(0)
        return HttpResponse(img_byte_arr.getvalue(), content_type='image/png')

    def ensure_transparency(self, image, no_data_value):
        """
        确保图像中的无效区域是透明的。

        参数:
        - image: 要处理的图像
        - no_data_value: 无效值，用于确定哪些区域需要透明

        返回:
        - Image: 处理后的图像
        """
        image = image.convert("RGBA")
        data = np.array(image)
        if no_data_value is not None:
            mask = (data[:, :, 0] == no_data_value) & (data[:, :, 1] == no_data_value) & (
                        data[:, :, 2] == no_data_value)
            data[mask] = (0, 0, 0, 0)  # 设置为透明
        return Image.fromarray(data)

    def post(self, request):
        """
        处理 POST 请求，返回固定值 2。

        参数:
        - request: HTTP 请求对象

        返回:
        - Response: 返回固定值 2
        """
        return Response(2)

    def get_tile_count_for_level(self, level, min_x, max_x, min_y, max_y, epsg_code, tile_size):
        """
        根据 TIFF 范围和级别计算瓦片的最大最小行列值。

        参数:
        - level: 瓦片级别
        - min_x: TIFF 的最小 x 坐标
        - max_x: TIFF 的最大 x 坐标
        - min_y: TIFF 的最小 y 坐标
        - max_y: TIFF 的最大 y 坐标
        - epsg_code: EPSG 代码
        - tile_size: 瓦片大小

        返回:
        - (min_row, max_row, min_col, max_col): 瓦片的行列范围
        """
        # 计算当前级别的分辨率
        if epsg_code == 3857:
            initial_resolution = 2 * 20037508.342789244 / tile_size  # Web Mercator
            resolution = initial_resolution / (2 ** level)
        elif epsg_code in [4326, 4490]:
            initial_resolution = 360.0 / tile_size  # WGS 84
            resolution = initial_resolution / (2 ** level)
        else:
            raise Exception(f"不支持的坐标系: {epsg_code}")

        # 计算当前层级的最小和最大列
        if epsg_code == 3857:
            min_col = int((min_x + 20037508.342789244) / (tile_size * resolution))
            max_col = int((max_x + 20037508.342789244) / (tile_size * resolution))
        else:  # 对于 EPSG:4326 和 EPSG:4490
            min_col = int((min_x + 180) / (tile_size * resolution))
            max_col = int((max_x + 180) / (tile_size * resolution))

        # 计算当前层级的最小和最大行
        if epsg_code == 3857:
            min_row = int((20037508.342789244 - max_y) / (tile_size * resolution))
            max_row = int((20037508.342789244 - min_y) / (tile_size * resolution))
        else:  # 对于 EPSG:4326 和 EPSG:4490
            min_row = int((90 - max_y) / (tile_size * resolution))
            max_row = int((90 - min_y) / (tile_size * resolution))

        # 确保行列范围不小于0
        min_col = max(min_col, 0)
        min_row = max(min_row, 0)

        # 确保最大行列不小于最小行列
        max_col = max(max_col, min_col)  # 确保 max_col 至少等于 min_col
        max_row = max(max_row, min_row)  # 确保 max_row 至少等于 min_row

        # 计算可能的最大行列值
        if epsg_code == 3857:
            max_col = min(max_col, int((20037508.342789244 * 2) / (tile_size * resolution)))
            max_row = min(max_row, int((20037508.342789244 * 2) / (tile_size * resolution)))
        else:  # 对于 EPSG:4326 和 EPSG:4490
            max_col = min(max_col, int(360 / (tile_size * resolution)))
            max_row = min(max_row, int(180 / (tile_size * resolution)))

        return min_row, max_row, min_col, max_col
