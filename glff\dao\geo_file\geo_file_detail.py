# models.py
from django.db import models
from django.utils import timezone
import json

class GeoFileDetail(models.Model):
    id = models.CharField(
        verbose_name='文件ID',
        primary_key=True,
        max_length=32
    )
    url = models.CharField(
        verbose_name='文件访问地址',
        max_length=512
    )
    size = models.BigIntegerField(
        verbose_name='文件大小（字节）',
        null=True,
        blank=True
    )
    filename = models.CharField(
        verbose_name='文件名称',
        max_length=256,
        null=True,
        blank=True
    )
    original_filename = models.CharField(
        verbose_name='原始文件名',
        max_length=256,
        null=True,
        blank=True
    )
    base_path = models.CharField(
        verbose_name='基础存储路径',
        max_length=256,
        null=True,
        blank=True
    )
    path = models.CharField(
        verbose_name='存储路径',
        max_length=256,
        null=True,
        blank=True
    )
    ext = models.Char<PERSON>ield(
        verbose_name='文件扩展名',
        max_length=32,
        null=True,
        blank=True
    )
    content_type = models.CharField(
        verbose_name='MIME类型',
        max_length=128,
        null=True,
        blank=True
    )
    platform = models.CharField(
        verbose_name='存储平台',
        max_length=32,
        null=True,
        blank=True
    )
    th_url = models.CharField(
        verbose_name='缩略图访问路径',
        max_length=512,
        null=True,
        blank=True
    )
    th_filename = models.CharField(
        verbose_name='缩略图名称',
        max_length=256,
        null=True,
        blank=True
    )
    th_size = models.BigIntegerField(
        verbose_name='缩略图大小（字节）',
        null=True,
        blank=True
    )
    th_content_type = models.CharField(
        verbose_name='缩略图MIME类型',
        max_length=128,
        null=True,
        blank=True
    )
    object_id = models.CharField(
        verbose_name='所属对象ID',
        max_length=32,
        null=True,
        blank=True
    )
    object_type = models.CharField(
        verbose_name='所属对象类型',
        max_length=32,
        null=True,
        blank=True
    )
    metadata = models.TextField(
        verbose_name='文件元数据',
        null=True,
        blank=True
    )
    user_metadata = models.TextField(
        verbose_name='用户元数据',
        null=True,
        blank=True
    )
    th_metadata = models.TextField(
        verbose_name='缩略图元数据',
        null=True,
        blank=True
    )
    th_user_metadata = models.TextField(
        verbose_name='缩略图用户元数据',
        null=True,
        blank=True
    )
    attr = models.TextField(
        verbose_name='附加属性',
        null=True,
        blank=True
    )
    file_acl = models.CharField(
        verbose_name='文件ACL',
        max_length=32,
        null=True,
        blank=True
    )
    th_file_acl = models.CharField(
        verbose_name='缩略图ACL',
        max_length=32,
        null=True,
        blank=True
    )
    hash_info = models.TextField(
        verbose_name='哈希信息',
        null=True,
        blank=True
    )
    upload_id = models.CharField(
        verbose_name='上传ID',
        max_length=128,
        null=True,
        blank=True
    )
    upload_status = models.IntegerField(
        verbose_name='上传状态',
        null=True,
        blank=True,
        choices=[
            (1, '初始化完成'),
            (2, '上传完成')
        ]
    )
    create_time = models.DateTimeField(
        verbose_name='创建时间',
        auto_now_add=True
    )
    status = models.CharField(
        verbose_name='状态',
        max_length=32,
        null=True,
        blank=True
    )
    delete_date = models.DateTimeField(
        verbose_name='删除时间',
        null=True,
        blank=True
    )
    order_no = models.IntegerField(
        verbose_name='排序值',
        null=True,
        blank=True
    )
    remarks = models.CharField(
        verbose_name='描述',
        max_length=255,
        null=True,
        blank=True
    )

    class Meta:
        db_table = 'filestorage"."file_detail'  # PostgreSQL 模式指定方式
        verbose_name = '文件记录表'
        verbose_name_plural = verbose_name

    def __str__(self):
        return f'{self.filename} ({self.id})'

    # 自定义管理器方法
    @classmethod
    def create_file_detail(cls, **kwargs):
        """
        创建文件记录（自动处理JSON字段）
        """
        try:
            # 处理元数据字段
            for field in ['metadata', 'user_metadata', 'th_metadata', 'th_user_metadata']:
                if field in kwargs and isinstance(kwargs[field], dict):
                    kwargs[field] = json.dumps(kwargs[field], ensure_ascii=False)

            return cls.objects.create(**kwargs)
        except Exception as e:
            raise ValueError(f'创建文件记录失败: {str(e)}')

    @classmethod
    def update_file_detail(cls, file_id, **kwargs):
        """
        更新文件记录
        """
        try:
            obj = cls.objects.get(id=file_id)
            for key, value in kwargs.items():
                # 处理元数据字段
                if key in ['metadata', 'user_metadata', 'th_metadata', 'th_user_metadata'] and isinstance(value, dict):
                    value = json.dumps(value, ensure_ascii=False)
                setattr(obj, key, value)
            obj.save()
            return obj
        except cls.DoesNotExist:
            raise ValueError('文件记录不存在')
        except Exception as e:
            raise ValueError(f'更新文件记录失败: {str(e)}')

    @classmethod
    def delete_file_detail(cls, file_id):
        """
        删除文件记录（物理删除）
        """
        try:
            obj = cls.objects.get(id=file_id)
            obj.delete()
            return True
        except cls.DoesNotExist:
            raise ValueError('文件记录不存在')
        except Exception as e:
            raise ValueError(f'删除文件记录失败: {str(e)}')

    @classmethod
    def get_by_id(cls, file_id):
        """
        根据ID获取文件记录（自动解析JSON字段）
        """
        try:
            obj = cls.objects.get(id=file_id)
            # 反序列化元数据字段
            for field in ['metadata', 'user_metadata', 'th_metadata', 'th_user_metadata']:
                if getattr(obj, field):
                    setattr(obj, field, json.loads(getattr(obj, field)))
            return obj
        except cls.DoesNotExist:
            return None
        except json.JSONDecodeError:
            raise ValueError('元数据格式错误')
        except Exception as e:
            raise ValueError(f'查询文件记录失败: {str(e)}')

    @classmethod
    def get_by_upload_id(cls, upload_id):
        """
        根据上传ID获取记录
        """
        try:
            return cls.objects.get(upload_id=upload_id)
        except cls.DoesNotExist:
            return None

# 用法示例
if __name__ == "__main__":
    # 创建记录
    new_file = GeoFileDetail.create_file_detail(
        id="FILE_001",
        url="https://storage.example.com/files/001.pdf",
        metadata={"author": "张三", "department": "研发部"},
        upload_status=2
    )

    # 更新记录
    updated_file = GeoFileDetail.update_file_detail(
        "FILE_001",
        remarks="重要合同文件",
        user_metadata={"reviewer": "李四"}
    )

    # 查询记录
    file_detail = GeoFileDetail.get_by_id("FILE_001")
    print(file_detail.metadata)  # 输出: {'author': '张三', 'department': '研发部'}

    # 删除记录
    GeoFileDetail.delete_file_detail("FILE_001")
