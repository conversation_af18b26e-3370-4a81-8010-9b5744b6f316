"""
WSGI config for nank_geo project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/howto/deployment/wsgi/
"""

import os
from dotenv import load_dotenv
load_dotenv()  # 这会把 .env 里的变量注入 os.environ
os.environ['PROJ_LIB'] = os.environ.get('PROJ_LIB', '')
print('PROJ_LIB =', os.environ.get('PROJ_LIB'))
from django.core.wsgi import get_wsgi_application

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nank_geo.settings')

application = get_wsgi_application()
