import io
import logging
import math
import os
from concurrent.futures import ThreadPoolExecutor
from functools import lru_cache
from threading import Lock
from typing import Optional, Tuple
from xml.etree.ElementTree import Element, SubElement, tostring
from xml.dom import minidom

import numpy as np
from PIL import Image
from django.http import HttpResponse, HttpRequest
from django.conf import settings as django_settings
from django.urls import reverse
from pyproj import Transformer, CRS
from django.views import View
from osgeo import gdal, osr

# Import your project models
from glff.dao.geo_file.geo_file_detail import GeoFileDetail

# --- Global configuration & initialization ---

gdal.UseExceptions()
# Set GDAL to use multiple threads for operations if supported
try:
    gdal.SetConfigOption('GDAL_NUM_THREADS', str(getattr(django_settings, 'GDAL_NUM_THREADS', 'ALL_CPUS')))
except Exception:
    pass

# Thread pool for I/O intensive tasks (e.g., writing cache)
io_executor = ThreadPoolExecutor(
    max_workers=getattr(django_settings, 'TILE_SERVICE_IO_WORKERS', os.cpu_count() or 4),
    thread_name_prefix='tile_cache_writer'
)

# Lock for dataset cache management
dataset_cache_lock = Lock()

class DatasetCache:
    """Cache open GDAL datasets to reduce open/close overhead."""
    def __init__(self, max_size=10):
        self.max_size = max_size
        self.cache = lru_cache(maxsize=self.max_size)(self._open_dataset)

    def _open_dataset(self, path: str):
        # Always open readonly
        return gdal.Open(path, gdal.GA_ReadOnly)

    def get(self, path: str):
        try:
            ds = self.cache(path)
            return ds
        except RuntimeError as e:
            logging.error(f"Failed to open dataset {path}: {e}")
            return None

# Singleton dataset cache
dataset_cache = DatasetCache(max_size=getattr(django_settings, 'TILE_SERVICE_DATASET_CACHE_SIZE', 20))

# Cache for Transformer objects: key (src_epsg, dst_epsg)
transformer_cache = {}
transformer_cache_lock = Lock()

def get_transformer(src_wkt: str, dst_epsg: int):
    """Get or create a pyproj Transformer from source WKT to target EPSG."""
    key = (src_wkt, dst_epsg)
    with transformer_cache_lock:
        if key not in transformer_cache:
            try:
                transformer_cache[key] = Transformer.from_crs(CRS(src_wkt), CRS.from_epsg(dst_epsg), always_xy=True)
            except Exception as e:
                logging.error(f"Failed to create transformer: {e}")
                raise
        return transformer_cache[key]

class TileProcessor:
    """
    Encapsulates tile generation logic.
    """
    TILE_SIZE = 256

    def __init__(self):
        # Cache color lookup tables if needed
        self.color_lut_cache = {}

    def generate_tile(self, dataset: gdal.Dataset, tile_bounds: tuple, target_srs: osr.SpatialReference,
                      color_map: Optional[dict] = None) -> Optional[Image.Image]:
        source_srs = osr.SpatialReference(wkt=dataset.GetProjection())
        band1 = dataset.GetRasterBand(1)
        no_data_value = band1.GetNoDataValue() if band1 else None

        # Prepare warp options
        warp_options = {
            'format': 'MEM',
            'outputBounds': tile_bounds,
            'width': self.TILE_SIZE,
            'height': self.TILE_SIZE,
            'dstSRS': target_srs,
            'srcSRS': source_srs,
            'resampleAlg': gdal.GRIORA_Cubic if getattr(django_settings, 'USE_CUBIC_RESAMPLE', False) else gdal.GRIORA_NearestNeighbour,
            'outputType': gdal.GDT_Float32 if color_map else gdal.GDT_Byte,
        }
        if no_data_value is not None:
            warp_options['dstNodata'] = 0
            warp_options['srcNodata'] = no_data_value

        # Use multithreaded warp if supported
        # warp_options['NUM_THREADS'] = getattr(django_settings, 'GDAL_NUM_THREADS', 'ALL_CPUS')

        tile_dataset = gdal.Warp('', dataset, **warp_options)
        if not tile_dataset:
            return None
        return self._convert_gdal_to_pil(tile_dataset, color_map)

    def _convert_gdal_to_pil(self, tile_dataset: gdal.Dataset, color_map: Optional[dict]) -> Optional[Image.Image]:
        band_count = tile_dataset.RasterCount
        if band_count == 0:
            return None

        # Single-band with color mapping
        if band_count == 1 and color_map:
            band = tile_dataset.GetRasterBand(1)
            data = band.ReadAsArray().astype(np.float32)
            data = np.flipud(data)  # Flip Y axis
            no_data = band.GetNoDataValue()
            rgba = self.apply_color_map(data, color_map, no_data)
            return Image.fromarray(rgba, 'RGBA')

        # Multi-band: read and flip as needed
        bands_data = []
        for i in range(band_count):
            band = tile_dataset.GetRasterBand(i+1)
            arr = band.ReadAsArray()
            arr = np.flipud(arr)
            bands_data.append(arr)

        # Build PIL image
        if band_count == 1:
            image = Image.fromarray(bands_data[0]).convert("RGBA")
        elif band_count == 3:
            image = Image.merge('RGB', [Image.fromarray(b) for b in bands_data[:3]]).convert("RGBA")
        else:
            image = Image.merge('RGBA', [Image.fromarray(b) for b in bands_data[:4]])

        # Make black (0,0,0) transparent
        arr = np.array(image)
        mask = (arr[:, :, 0] == 0) & (arr[:, :, 1] == 0) & (arr[:, :, 2] == 0)
        arr[mask, 3] = 0
        return Image.fromarray(arr)

    def apply_color_map(self, data: np.ndarray, color_map: dict, no_data: Optional[float]) -> np.ndarray:
        # Create or retrieve LUT for color_map to accelerate interpolation
        stops = sorted(color_map.keys())
        key = tuple(stops)
        # Precompute RGB arrays
        red = np.array([color_map[s][0] for s in stops], dtype=np.uint8)
        green = np.array([color_map[s][1] for s in stops], dtype=np.uint8)
        blue = np.array([color_map[s][2] for s in stops], dtype=np.uint8)

        # Interpolate
        norm = data
        r = np.interp(norm, stops, red).astype(np.uint8)
        g = np.interp(norm, stops, green).astype(np.uint8)
        b = np.interp(norm, stops, blue).astype(np.uint8)
        a = np.full(r.shape, 255, dtype=np.uint8)
        if no_data is not None:
            a[data == no_data] = 0
        rgba = np.dstack((r, g, b, a))
        return rgba

class TiffDyViewPro(View):
    TILE_SIZE = 256
    DEM_COLOR_MAP = {
        0: [67, 103, 135], 500: [91, 145, 165], 1000: [157, 192, 163],
        2000: [224, 237, 168], 3000: [216, 187, 122], 4000: [181, 129, 80],
    }
    processor = TileProcessor()

    def get(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        params = {k.lower(): v for k, v in request.GET.items()}
        if params.get('service', '').upper() == 'WMTS':
            request_type = params.get('request', '').lower()
            if request_type == 'getcapabilities':
                return self._handle_kvp_get_capabilities(request, params)
            elif request_type == 'gettile':
                return self._handle_kvp_get_tile(request, params)
            else:
                return self._create_exception_report("InvalidRequest", f"Unsupported request type: {params.get('request')}")
        if 'capabilities.xml' in request.path.lower():
            return self.get_capabilities(request, **kwargs)
        return self.get_tile(request, **kwargs)

    def _handle_kvp_get_capabilities(self, request: HttpRequest, params: dict) -> HttpResponse:
        file_id = params.get('layer')
        epsg_code_str = params.get('tilematrixset')
        if not file_id or not epsg_code_str:
            return self._create_exception_report("MissingParameter", "Parameters 'layer' and 'tilematrixset' are required.")
        epsg_code = epsg_code_str.upper().replace('EPSG_', '')
        return self.get_capabilities(request, FILEID=file_id, EPSGCODE=epsg_code)

    def _handle_kvp_get_tile(self, request: HttpRequest, params: dict) -> HttpResponse:
        try:
            kwargs = {
                'FILEID': params['layer'],
                'EPSGCODE': params['tilematrixset'].upper().replace('EPSG_', ''),
                'TileMatrix': params['tilematrix'],
                'TileCol': params['tilecol'],
                'TileRow': params['tilerow'],
                'FORMAT': params.get('format', 'image/png')
            }
        except KeyError as e:
            return self._create_exception_report("MissingParameter", f"Missing KVP parameter: {e}")
        return self.get_tile(request, **kwargs)

    def get_tile(self, request: HttpRequest, FILEID: str, EPSGCODE: str, TileMatrix: str, TileCol: str,
                 TileRow: str, FORMAT: Optional[str] = None) -> HttpResponse:
        try:
            z, x, y = int(TileMatrix), int(TileCol), int(TileRow)
            target_epsg = int(EPSGCODE)
        except ValueError:
            return self._create_exception_report("InvalidParameterValue", "Invalid tile parameters.")
        try:
            tiff_path = self._get_tiff_path(FILEID)
            cache_path = self._get_cache_path(tiff_path, target_epsg, z, x, y)
            # Serve from cache if exists
            if os.path.exists(cache_path):
                return self._serve_cached_tile(cache_path)
            # Open dataset from cache
            with dataset_cache_lock:
                dataset = dataset_cache.get(tiff_path)
            if not dataset:
                raise FileNotFoundError(f"Cannot open dataset for FILEID {FILEID}.")
            source_srs = osr.SpatialReference(wkt=dataset.GetProjection())
            source_gt = dataset.GetGeoTransform()
            tile_bounds = self._calculate_tile_bounds(z, x, y, target_epsg)
            tiff_bounds = self._transform_tiff_bounds(source_gt, dataset.RasterXSize, dataset.RasterYSize, source_srs, target_epsg)
            if self._is_tile_out_of_bounds(tile_bounds, tiff_bounds):
                return self._create_image_response(None)
            color_map = self.DEM_COLOR_MAP if 'dem' in tiff_path.lower() else None
            # Prepare target SRS only once
            target_srs = osr.SpatialReference()
            target_srs.ImportFromEPSG(target_epsg)
            tile_image = self.processor.generate_tile(dataset, tile_bounds, target_srs, color_map)
            if not tile_image or self._is_image_empty(tile_image):
                return self._create_image_response(None)
            self._save_tile_to_cache_async(cache_path, tile_image)
            return self._create_image_response(tile_image)
        except FileNotFoundError as e:
            return self._create_exception_report("InvalidParameterValue", str(e), status=404)
        except Exception as e:
            logging.error(f"Error generating tile: {e}", exc_info=True)
            return self._create_exception_report("NoApplicableCode", "Internal server error.", status=500)

    def get_capabilities(self, request: HttpRequest, FILEID: str, EPSGCODE: str, **kwargs) -> HttpResponse:
        try:
            target_epsg = int(EPSGCODE)
            tiff_path = self._get_tiff_path(FILEID)
            with dataset_cache_lock:
                dataset = dataset_cache.get(tiff_path)
            if not dataset:
                raise FileNotFoundError(f"Cannot open dataset for FILEID {FILEID}.")
            source_srs = osr.SpatialReference(wkt=dataset.GetProjection())
            source_gt = dataset.GetGeoTransform()
            bounds = self._transform_tiff_bounds(source_gt, dataset.RasterXSize, dataset.RasterYSize, source_srs, target_epsg)
            xml_string = self._build_capabilities_xml(request, FILEID, f"Layer for {FILEID}", target_epsg, bounds)
            return HttpResponse(xml_string, content_type='application/xml')
        except FileNotFoundError as e:
            return self._create_exception_report("InvalidParameterValue", str(e), status=404)
        except Exception as e:
            logging.error(f"Error generating capabilities: {e}", exc_info=True)
            return self._create_exception_report("NoApplicableCode", "Cannot generate Capabilities.", status=500)

    def _build_capabilities_xml(self, request: HttpRequest, layer_id: str, title: str, epsg: int, bounds: tuple) -> str:
        restful_base_url = request.build_absolute_uri().split('/capabilities.xml')[0]
        kvp_endpoint_url = request.build_absolute_uri(reverse('tile_service:wmts_kvp_endpoint'))
        root = Element('Capabilities', {'version': '1.0.0', 'xmlns': 'http://www.opengis.net/wmts/1.0',
                                        'xmlns:ows': 'http://www.opengis.net/ows/1.1',
                                        'xmlns:xlink': 'http://www.w3.org/1999/xlink'})
        operations_metadata = SubElement(root, 'ows:OperationsMetadata')
        for op_name in ['GetCapabilities', 'GetTile']:
            op = SubElement(operations_metadata, 'ows:Operation', {'name': op_name})
            dcp = SubElement(op, 'ows:DCP')
            http = SubElement(dcp, 'ows:HTTP')
            SubElement(http, 'ows:Get', {'xlink:href': kvp_endpoint_url})
        contents = SubElement(root, 'Contents')
        layer = SubElement(contents, 'Layer')
        SubElement(layer, 'ows:Title').text = title
        SubElement(layer, 'ows:Identifier').text = layer_id
        bbox = SubElement(layer, 'ows:WGS84BoundingBox', {'crs': f'urn:ogc:def:crs:EPSG::{epsg}'})
        SubElement(bbox, 'ows:LowerCorner').text = f'{bounds[0]} {bounds[1]}'
        SubElement(bbox, 'ows:UpperCorner').text = f'{bounds[2]} {bounds[3]}'
        tms_link = SubElement(layer, 'TileMatrixSetLink')
        SubElement(tms_link, 'TileMatrixSet').text = f'EPSG_{epsg}'
        SubElement(layer, 'ResourceURL', {'format': 'image/png', 'resourceType': 'tile',
                                          'template': f'{restful_base_url}/{{TileMatrix}}/{{TileCol}}/{{TileRow}}.png'})
        tms = SubElement(contents, 'TileMatrixSet')
        SubElement(tms, 'ows:Identifier').text = f'EPSG_{epsg}'
        SubElement(tms, 'ows:SupportedCRS').text = f'urn:ogc:def:crs:EPSG::{epsg}'
        rough_string = tostring(root, 'utf-8')
        reparsed = minidom.parseString(rough_string)
        return reparsed.toprettyxml(indent="  ")

    def _create_exception_report(self, code: str, text: str, status: int = 400) -> HttpResponse:
        root = Element('ExceptionReport', {'xmlns': 'http://www.opengis.net/ows/1.1', 'version': '1.1.0'})
        exception = SubElement(root, 'Exception', {'exceptionCode': code})
        SubElement(exception, 'ExceptionText').text = text
        xml_string = tostring(root, 'utf-8')
        return HttpResponse(xml_string, content_type='application/xml', status=status)

    def _get_tiff_path(self, file_id: str) -> str:
        file_detail = GeoFileDetail.get_by_id(file_id)
        if not file_detail:
            raise FileNotFoundError(f"No record for ID {file_id}.")
        return os.path.join(django_settings.JFS_PATH, file_detail.base_path, file_detail.path, file_detail.filename)

    def _get_cache_path(self, tiff_path: str, epsg: int, z: int, x: int, y: int) -> str:
        tile_cache_dir = os.path.join(os.path.dirname(tiff_path), "tile_cache")
        return os.path.join(tile_cache_dir, str(epsg), str(z), str(x), f"{y}.png")

    def _serve_cached_tile(self, cache_path: str) -> HttpResponse:
        try:
            with open(cache_path, 'rb') as f:
                return HttpResponse(f.read(), content_type='image/png')
        except Exception as e:
            logging.error(f"Failed to serve cached tile {cache_path}: {e}")
            return self._create_image_response(None)

    def _is_image_empty(self, image: Image.Image) -> bool:
        if image.mode != 'RGBA':
            return False
        return not image.getbbox()

    def _save_tile_to_cache_async(self, cache_path: str, image: Image.Image):
        def save_task():
            try:
                os.makedirs(os.path.dirname(cache_path), exist_ok=True)
                image.save(cache_path, format='PNG')
            except Exception as e:
                logging.error(f"Async cache write failed: {e}", exc_info=True)
        io_executor.submit(save_task)

    def _create_image_response(self, image: Optional[Image.Image]) -> HttpResponse:
        if image is None:
            image = Image.new("RGBA", (self.TILE_SIZE, self.TILE_SIZE), (0, 0, 0, 0))
        buf = io.BytesIO()
        image.save(buf, format='PNG')
        buf.seek(0)
        return HttpResponse(buf.getvalue(), content_type='image/png')

    def _transform_tiff_bounds(self, gt: tuple, x_size: int, y_size: int, source_srs: osr.SpatialReference,
                               target_epsg: int) -> tuple:
        min_x, max_y = gt[0], gt[3]
        max_x = min_x + gt[1] * x_size
        min_y = max_y + gt[5] * y_size
        src_wkt = source_srs.ExportToWkt()
        transformer = get_transformer(src_wkt, target_epsg)
        points = [(min_x, min_y), (min_x, max_y), (max_x, min_y), (max_x, max_y)]
        trans = [transformer.transform(x, y) for x, y in points]
        xs = [p[0] for p in trans]
        ys = [p[1] for p in trans]
        return min(xs), min(ys), max(xs), max(ys)

    def _is_tile_out_of_bounds(self, tile_bounds: tuple, tiff_bounds: tuple) -> bool:
        tile_min_x, tile_max_y, tile_max_x, tile_min_y = tile_bounds
        tiff_min_x, tiff_min_y, tiff_max_x, tiff_max_y = tiff_bounds
        if tile_min_x >= tiff_max_x or tile_max_x <= tiff_min_x:
            return True
        if tile_min_y >= tiff_max_y or tile_max_y <= tiff_min_y:
            return True
        return False

    def _calculate_tile_bounds(self, z: int, x: int, y: int, epsg: int) -> tuple:
        if epsg == 3857:
            origin_x, origin_y = -20037508.342789244, 20037508.342789244
            initial_resolution = 2 * origin_y / self.TILE_SIZE
        elif epsg in [4326, 4490]:
            origin_x, origin_y = -180.0, 90.0
            initial_resolution = 360.0 / self.TILE_SIZE
        else:
            raise ValueError(f"Unsupported EPSG:{epsg}")
        resolution = initial_resolution / (2 ** z)
        x_min = origin_x + x * self.TILE_SIZE * resolution
        y_max = origin_y - y * self.TILE_SIZE * resolution
        x_max = x_min + self.TILE_SIZE * resolution
        y_min = y_max - self.TILE_SIZE * resolution
        return x_min, y_max, x_max, y_min
