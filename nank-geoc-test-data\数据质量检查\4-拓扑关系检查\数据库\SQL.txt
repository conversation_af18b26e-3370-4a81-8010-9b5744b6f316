1、建模式
geodata

2、建表，导入数据
CREATE TABLE "geodata"."spatial_table" (
  "id" int4 NOT NULL DEFAULT nextval('"geodata".spatial_table_id_seq'::regclass),
  "name" varchar(50) COLLATE "pg_catalog"."default",
  "geometry" geometry(GEOMETRY),
  CONSTRAINT "spatial_table_pkey" PRIMARY KEY ("id")
)
;

ALTER TABLE "geodata"."spatial_table" 
  OWNER TO "postgres";


INSERT INTO "geodata"."spatial_table" ("id", "name", "geometry") VALUES (1, 'overlap_A', ST_GeomFromText('POLYGON((0 0, 0 2, 2 2, 2 0, 0 0))', 3857));
INSERT INTO "geodata"."spatial_table" ("id", "name", "geometry") VALUES (2, 'overlap_B', ST_GeomFromText('POLYGON((1 1, 1 3, 3 3, 3 1, 1 1))', 3857));
INSERT INTO "geodata"."spatial_table" ("id", "name", "geometry") VALUES (3, 'valid_poly', ST_GeomFromText('POLYGON((10 10, 10 12, 12 12, 12 10, 10 10))', 3857));
INSERT INTO "geodata"."spatial_table" ("id", "name", "geometry") VALUES (4, 'invalid_bowtie', ST_GeomFromText('POLYGON((20 20, 22 22, 20 22, 22 20, 20 20))', 3857));
INSERT INTO "geodata"."spatial_table" ("id", "name", "geometry") VALUES (5, 'gap_former_A', ST_GeomFromText('POLYGON((30 31, 30 32, 31 32, 31 31, 30 31))', 3857));
INSERT INTO "geodata"."spatial_table" ("id", "name", "geometry") VALUES (6, 'gap_former_B', ST_GeomFromText('POLYGON((32 31, 32 32, 33 32, 33 31, 32 31))', 3857));
INSERT INTO "geodata"."spatial_table" ("id", "name", "geometry") VALUES (7, 'island_poly', ST_GeomFromText('POLYGON((40 40, 40 41, 41 41, 41 40, 40 40))', 3857));
INSERT INTO "geodata"."spatial_table" ("id", "name", "geometry") VALUES (8, 'gap_bridge', ST_GeomFromText('POLYGON((30 30, 33 30, 33 31, 30 31, 30 30))', 3857));
INSERT INTO "geodata"."spatial_table" ("id", "name", "geometry") VALUES (9, 'self_intersect', ST_GeomFromText('LINESTRING(0 0, 2 2, 0 2, 2 0)', 3857));
INSERT INTO "geodata"."spatial_table" ("id", "name", "geometry") VALUES (10, 'intersect_A', ST_GeomFromText('LINESTRING(5 5, 7 7)', 3857));
INSERT INTO "geodata"."spatial_table" ("id", "name", "geometry") VALUES (11, 'intersect_B', ST_GeomFromText('LINESTRING(5 7, 7 5)', 3857));
INSERT INTO "geodata"."spatial_table" ("id", "name", "geometry") VALUES (12, 'dangle_line_1', ST_GeomFromText('LINESTRING(10 10, 11 11)', 3857));
INSERT INTO "geodata"."spatial_table" ("id", "name", "geometry") VALUES (13, 'connected_A', ST_GeomFromText('LINESTRING(15 15, 16 16)', 3857));
INSERT INTO "geodata"."spatial_table" ("id", "name", "geometry") VALUES (14, 'connected_B', ST_GeomFromText('LINESTRING(16 16, 17 15)', 3857));
INSERT INTO "geodata"."spatial_table" ("id", "name", "geometry") VALUES (15, 'dangle_line_2', ST_GeomFromText('LINESTRING(20 20, 21 21)', 3857));
INSERT INTO "geodata"."spatial_table" ("id", "name", "geometry") VALUES (16, 'duplicate_A', ST_GeomFromText('POINT(0 0)', 3857));
INSERT INTO "geodata"."spatial_table" ("id", "name", "geometry") VALUES (17, 'duplicate_B', ST_GeomFromText('POINT(0 0)', 3857));
INSERT INTO "geodata"."spatial_table" ("id", "name", "geometry") VALUES (18, 'unique_point', ST_GeomFromText('POINT(5 5)', 3857));
INSERT INTO "geodata"."spatial_table" ("id", "name", "geometry") VALUES (19, 'duplicate_C', ST_GeomFromText('POINT(1 1)', 3857));
INSERT INTO "geodata"."spatial_table" ("id", "name", "geometry") VALUES (20, 'duplicate_D', ST_GeomFromText('POINT(1 1)', 3857));
