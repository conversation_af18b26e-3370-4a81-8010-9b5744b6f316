"""
地理数据重投影模块

本模块提供统一的地理数据坐标转换功能，支持栅格和矢量数据的重投影转换。
主要功能包括：
1. 单文件坐标转换：支持TIF/GeoTIFF、GeoJSON、Shapefile等格式
2. 批量文件转换：支持同类型文件的批量处理
3. 多种转换方式：支持直接转换、七参数转换等

特性：
- 内存控制：可设置最大内存使用量
- 进度回调：支持处理进度监控
- 精度控制：支持单精度/双精度输出
- 异常处理：完善的错误处理和日志记录

使用示例：
>>> from glff.normalization.reproject import Reprojector
>>> reprojector = Reprojector(max_memory_mb=2048)  # 初始化2GB内存限制
>>> # 单文件转换
>>> reprojector.transform_coordinates("input.tif", "output.tif", dst_crs="EPSG:4326")
>>> # 批量转换
>>> reprojector.batch_transform_coordinates(["file1.tif", "file2.tif"], "output_dir")

注意事项：
1. 批量处理要求所有文件类型必须一致
2. 七参数转换仅支持栅格数据
3. 大文件处理时注意内存限制
"""

from typing import Union, List, Dict, Optional, Callable
from pathlib import Path
import logging
from glff.normalization.data_pre_processing.reproject.tif_reproject import CompatibleGeoTiffTransformer
from glff.normalization.data_pre_processing.reproject.vector_reproject import (
    convert_geojson_file,
    convert_geojson_shp,
    convert_geojson_text
)

logger = logging.getLogger(__name__)

class Reprojector:
    """地理数据重投影处理器
    
    提供统一的地理数据坐标转换功能，支持栅格和矢量数据的重投影转换。
    
    主要功能：
    - 单文件坐标转换：支持TIF/GeoTIFF、GeoJSON、Shapefile等格式
    - 批量文件转换：支持同类型文件的批量处理
    - 多种转换方式：支持直接转换、七参数转换等
    
    使用示例：
    >>> from glff.normalization.reproject import Reprojector
    >>> reprojector = Reprojector(max_memory_mb=2048)  # 初始化2GB内存限制
    >>> # 单文件转换
    >>> success = reprojector.transform_coordinates("input.tif", "output.tif", dst_crs="EPSG:4326")
    >>> # 批量转换
    >>> results = reprojector.batch_transform_coordinates(["file1.tif", "file2.tif"], "output_dir")
    
    注意事项：
    1. 批量处理要求所有文件类型必须一致
    2. 七参数转换仅支持栅格数据
    3. 大文件处理时注意内存限制
    """

    def __init__(self, max_memory_mb: int = 1024, logger: Optional[logging.Logger] = None):
        """
        初始化重投影处理器
        
        参数:
            max_memory_mb (int): 最大内存使用量(MB)，默认1024MB(1GB)
                建议值：
                - 小文件(＜100MB): 512MB
                - 中等文件(100MB-1GB): 1024MB
                - 大文件(＞1GB): 2048MB或更高
            logger (Optional[logging.Logger]): 自定义日志记录器
                如果为None，则创建默认日志记录器
                
        示例:
            >>> reprojector = Reprojector(max_memory_mb=2048)  # 2GB内存限制
            >>> custom_logger = logging.getLogger("custom")
            >>> reprojector = Reprojector(logger=custom_logger)  # 使用自定义日志
        """
        self.max_memory_mb = max_memory_mb
        self.logger = logger or logging.getLogger(__name__)
        self.tif_transformer = CompatibleGeoTiffTransformer(
            max_memory_mb=max_memory_mb,
            logger=self.logger.getChild("Transformer")
        )

    # 批量定义投影
    def batch_transform_coordinates(
        self,
        input_files: List[Union[str, bytes]],
        output_dir: Union[str, bytes],
        src_crs: Union[str, List[str]] = "EPSG:32648",
        dst_crs: Union[str, List[str]] = "EPSG:4326",
        resample_method: str = "bilinear",
        precision: Optional[str] = None,
        seven_params: Optional[Dict[str, float]] = None,
        progress_callback: Optional[Callable] = None,
        **kwargs
    ) -> Dict[str, Union[bool, str]]:
        """批量地理数据坐标转换接口
        
        功能概述:
        提供批量地理数据坐标转换功能，支持同类型文件的批量处理。
        支持多种输入格式和转换方式，包括七参数转换等高级功能。
        
        参数说明:
        ----------
        input_files (List[Union[str, bytes]]): 输入文件列表
            - 要求所有文件类型必须一致
            - 支持格式: TIF/GeoTIFF(.tif/.tiff), GeoJSON(.geojson/.json), Shapefile(.shp)
            示例: ["/data/file1.tif", "/data/file2.tif"]
            
        output_dir (Union[str, bytes]): 输出目录
            - 必须已存在
            - 输出文件名将与输入文件名相同
            示例: "/output/batch_results"
            
        src_crs (Union[str, List[str]]): 源坐标参考系统，默认"EPSG:32648"(WGS84/UTM zone 48N)
            - 字符串: 所有文件使用相同的源坐标系
            - 列表: 为每个文件指定不同的源坐标系(长度必须与input_files相同)
            示例: 
            - "EPSG:32648" (所有文件使用相同坐标系)
            - ["EPSG:32648", "EPSG:32649"] (为不同文件指定不同坐标系)
            
        dst_crs (Union[str, List[str]]): 目标坐标参考系统，默认"EPSG:4326"(WGS84)
            - 格式同src_crs
            示例: "EPSG:3857" (Web墨卡托投影)
            
        resample_method (str): 重采样方法(仅栅格数据)，默认"bilinear"
            可选值: 
            - "nearest": 最近邻(适用于分类数据)
            - "bilinear": 双线性(适用于连续数据)
            - "cubic": 三次卷积(高质量重采样)
            - "average": 平均值(适用于降采样)
            
        precision (Optional[str]): 输出数据精度控制(仅栅格数据)
            可选值:
            - None/"auto": 保持输入精度(默认)
            - "single": 强制输出Float32
            - "double": 强制输出Float64
            
        seven_params (Optional[Dict[str, float]]): 七参数坐标转换参数(仅栅格数据)
            格式要求:
            {
                "dx": x平移量(m),
                "dy": y平移量(m),
                "dz": z平移量(m),
                "rx": x旋转量(秒),
                "ry": y旋转量(秒),
                "rz": z旋转量(秒),
                "scale": 比例因子(ppm)
            }
            
        progress_callback (Optional[Callable]): 进度回调函数
            接收两个参数: (当前处理数, 总数)
            示例: lambda current, total: print(f"进度: {current}/{total}")
            
        返回值:
        Dict[str, Union[bool, str]]: 
            - 键: 输入文件路径
            - 值: 
              * True: 转换成功
              * False: 转换失败
              * str: 错误信息
        
        支持的数据格式:
        - 栅格数据: TIF/GeoTIFF(.tif/.tiff)
        - 矢量数据: 
          * GeoJSON文件(.geojson/.json)
          * Shapefile(.shp及相关文件)
        
        使用示例:
        1. 栅格文件批量转换:
        >>> results = reprojector.batch_transform_coordinates(
        ...     ["file1.tif", "file2.tif"],
        ...     "output_dir",
        ...     dst_crs="EPSG:4326"
        ... )
        
        2. 七参数批量转换:
        >>> seven_params = {
        ...     "dx": 100, "dy": 200, "dz": 50,
        ...     "rx": 0.1, "ry": 0.2, "rz": 0.3,
        ...     "scale": 1.0001
        ... }
        >>> results = reprojector.batch_transform_coordinates(
        ...     ["file1.tif", "file2.tif"],
        ...     "output_7param",
        ...     seven_params=seven_params,
        ...     precision="double"
        ... )
        
        注意事项:
        1. 批量处理要求所有文件类型必须一致
        2. 七参数转换和精度控制仅支持栅格数据
        3. 输出目录必须已存在
        4. 处理大文件时注意内存使用限制
        5. 进度回调函数接收的是文件计数而非百分比
        """
        from os.path import join, basename, splitext
        from typing import List, Union, Dict
        
        # 检查文件类型是否一致
        extensions = set()
        for f in input_files:
            ext = splitext(str(f))[1].lower()
            extensions.add(ext)
            if len(extensions) > 1:
                raise ValueError(f"批量处理要求所有文件类型必须一致，发现多种类型: {extensions}")
        
        results = {}
        total = len(input_files)
        
        # 处理坐标系参数(支持单个值或列表)
        src_crs_list = [src_crs] * total if isinstance(src_crs, str) else src_crs
        dst_crs_list = [dst_crs] * total if isinstance(dst_crs, str) else dst_crs
        
        if len(src_crs_list) != total or len(dst_crs_list) != total:
            raise ValueError("src_crs和dst_crs列表长度必须与input_files相同")
        
        for i, (input_file, src, dst) in enumerate(zip(input_files, src_crs_list, dst_crs_list)):
            try:
                output_path = join(output_dir, basename(str(input_file)))
                result = self.transform_coordinates(
                    input_path=input_file,
                    output_path=output_path,
                    src_crs=src,
                    dst_crs=dst,
                    resample_method=resample_method,
                    precision=precision,
                    seven_params=seven_params,
                    progress_callback=progress_callback,
                    **kwargs
                )
                results[str(input_file)] = result
                
                if progress_callback:
                    progress_callback(i+1, total)
                    
            except Exception as e:
                results[str(input_file)] = str(e)
                
        return results

    # 坐标转换
    def transform_coordinates(
        self,
        input_path: Union[str, bytes],
        output_path: Union[str, bytes, None],
        src_crs: str = "EPSG:32648",
        dst_crs: str = "EPSG:4326",
        resample_method: str = "bilinear",
        precision: Optional[str] = None,
        seven_params: Optional[Dict[str, float]] = None,
        progress_callback: Optional[Callable] = None,
        **kwargs
    ) -> Union[bool, str]:
        """
        地理数据坐标转换主接口
        
        功能概述:
        提供统一的地理数据坐标转换功能，支持栅格和矢量数据的重投影转换。
        支持多种输入格式和转换方式，包括七参数转换等高级功能。
        
        参数说明:
        ----------
        input_path (Union[str, bytes]): 输入数据路径或GeoJSON文本内容
            - 文件路径: 支持绝对/相对路径
            - GeoJSON文本: 必须以'{'开头
            示例: 
            - "/data/input.tif"
            - b'/data/input.tif' (bytes路径)
            - '{"type": "Feature"...}' (GeoJSON文本)
            
        output_path (Union[str, bytes, None]): 输出路径
            - 文件转换: 指定输出文件路径
            - 文本转换: 必须为None
            示例:
            - "/output/result.tif"
            - None (当input_path是GeoJSON文本时)
            
        src_crs (str): 源坐标参考系统，默认"EPSG:32648"(WGS84/UTM zone 48N)
            支持格式:
            - EPSG代码(如"EPSG:4326")
            - WKT字符串
            - PROJ字符串
            示例: "EPSG:3857" (Web墨卡托投影)
            
        dst_crs (str): 目标坐标参考系统，默认"EPSG:4326"(WGS84)
            格式同src_crs
            
        resample_method (str): 重采样方法(仅栅格数据)，默认"bilinear"
            可选值: 
            - "nearest": 最近邻(适用于分类数据)
            - "bilinear": 双线性(适用于连续数据)
            - "cubic": 三次卷积(高质量重采样)
            - "average": 平均值(适用于降采样)
            
        precision (Optional[str]): 输出数据精度控制(仅栅格数据)
            可选值:
            - None/"auto": 保持输入精度(默认)
            - "single": 强制输出Float32
            - "double": 强制输出Float64
            
        seven_params (Optional[Dict[str, float]]): 七参数坐标转换参数(仅栅格数据)
            格式要求:
            {
                "dx": x平移量(m),
                "dy": y平移量(m),
                "dz": z平移量(m),
                "rx": x旋转量(秒),
                "ry": y旋转量(秒),
                "rz": z旋转量(秒),
                "scale": 比例因子(ppm)
            }
            
        progress_callback (Optional[Callable]): 进度回调函数(仅栅格数据)
            接收0.0-1.0之间的进度值
            示例: lambda p: print(f"进度: {p*100:.1f}%")
            
        返回值:
        - bool: 文件转换成功/失败
        - str: GeoJSON文本转换结果
        
        支持的数据格式:
        - 栅格数据: TIF/GeoTIFF(.tif/.tiff)
        - 矢量数据: 
          * GeoJSON文件(.geojson/.json)
          * Shapefile(.shp及相关文件)
          * 直接传入GeoJSON文本
        
        使用示例:
        1. 栅格文件转换:
        >>> success = reprojector.transform_coordinates(
        ...     "input.tif", 
        ...     "output.tif",
        ...     dst_crs="EPSG:4326",
        ...     resample_method="bilinear"
        ... )
        
        2. GeoJSON文本转换:
        >>> geojson_result = reprojector.transform_coordinates(
        ...     '{"type": "Feature"...}', 
        ...     None,
        ...     src_crs="EPSG:32648",
        ...     dst_crs="EPSG:4326"
        ... )
        
        注意事项:
        1. 七参数转换和精度控制仅支持栅格数据
        2. 进度回调仅栅格数据处理时有效
        3. 处理大文件时注意内存使用限制
        4. 批量处理请使用batch_transform_coordinates方法
        """
        try:
            # 处理GeoJSON文本输入
            if isinstance(input_path, str) and input_path.strip().startswith('{'):
                self.logger.info("Processing GeoJSON text input")
                result = convert_geojson_text(input_path, src_crs, dst_crs)
                return result

            input_path = str(Path(input_path)) if input_path else None
            output_path = str(Path(output_path)) if output_path else None

            # 根据文件类型选择处理方式
            if input_path.lower().endswith(('.tif', '.tiff', '.geotiff')):
                self.logger.info(f"Processing raster file: {input_path}")
                transform_args = {
                    'input_path': input_path,
                    'output_path': output_path,
                    'target_crs': dst_crs,
                    'resample_method': resample_method,
                    'progress_callback': progress_callback,
                    **kwargs
                }

                # 处理精度设置
                if precision == "single":
                    transform_args['force_single_precision'] = True
                elif precision == "double":
                    transform_args['force_double_precision'] = True

                # 处理7参数转换
                if seven_params:
                    transform_args['seven_params'] = seven_params

                # 重置转换器状态
                self.tif_transformer.close()
                self.tif_transformer.input_path = input_path
                self.tif_transformer.output_path = output_path
                self.tif_transformer.src_ds = None
                
                self.tif_transformer.transform(**transform_args)

            elif input_path.lower().endswith(('.geojson', '.json')):
                self.logger.info(f"Processing GeoJSON file: {input_path}")
                convert_geojson_file(input_path, output_path, src_crs, dst_crs)

            elif input_path.lower().endswith('.shp'):
                self.logger.info(f"Processing Shapefile: {input_path}")
                convert_geojson_shp(input_path, output_path, src_crs, dst_crs)

            else:
                raise ValueError(f"Unsupported file format: {input_path}")

            self.logger.info(f"Successfully transformed {input_path} to {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to transform {input_path}: {str(e)}")
            return False


if __name__ == "__main__":
    import logging
    from osgeo import gdal

    # 配置日志
    main_logger = logging.getLogger()
    main_logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    main_logger.addHandler(handler)

    # 初始化重投影器
    reprojector = Reprojector(
        max_memory_mb=1024,  # 1GB
        logger=main_logger.getChild("Reprojector")
    )

    # 示例1: 自动精度检测, 目标 WGS84 (EPSG:4326)
    print("\n示例1: 自动精度检测")
    reprojector.transform_coordinates(
        input_path="G:\\testcode\\ceshitiff\\ffff.tif",
        output_path="G:\\testcode\\ceshitiff\\ffff0506output_auto.tif",
        dst_crs="EPSG:4326",
        progress_callback=gdal.TermProgress_nocb
    )

    # 示例2: 强制单精度
    print("\n示例2: 强制单精度")
    reprojector.transform_coordinates(
        input_path="G:\\testcode\\ceshitiff\\ffff.tif",
        output_path="G:\\testcode\\ceshitiff\\ffff05062output_single.tif",
        precision="single",
        target_crs="EPSG:32650",  # UTM Zone 50N
        resample_method="bilinear",
        progress_callback=gdal.TermProgress_nocb
    )

    # 示例3: 7参数转换
    print("\n示例3: 7参数转换")
    seven_params = {
        "dx": 100, "dy": 200, "dz": 50,
        "rx": 0.1, "ry": 0.2, "rz": 0.3,
        "scale": 1.0001
    }
    reprojector.transform_coordinates(
        input_path="G:\\testcode\\ceshitiff\\world.tif",
        output_path="G:\\testcode\\ceshitiff\\ffff05063output_7param.tif",
        seven_params=seven_params,
        progress_callback=gdal.TermProgress_nocb
    )

    # 示例4: GeoJSON文件转换
    print("\n示例4: GeoJSON文件转换")
    reprojector.transform_coordinates(
        input_path="G:\\testcode\\ceshitiff\\output_actual_polygon.geojson",
        output_path="G:\\testcode\\ceshitiff\\output.geojson",
        src_crs="EPSG:32648",
        dst_crs="EPSG:4326",
        progress_callback=gdal.TermProgress_nocb
    )

    # 示例5: GeoJSON文本转换
    print("\n示例5: GeoJSON文本转换")
    sample_geojson_text = '''{
        "type": "FeatureCollection",
        "name": "output_bounds",
        "crs": { "type": "name", "properties": { "name": "urn:ogc:def:crs:EPSG::32648" } },
        "features": [{
            "type": "Feature",
            "properties": { },
            "geometry": {
                "type": "Polygon",
                "coordinates": [[
                    [566353.962730000028387, 2899559.663550000172108],
                    [566353.962730000028387, 2899939.746550000272691],
                    [565658.46473, 2899939.746550000272691],
                    [565658.46473, 2899559.663550000172108],
                    [566353.962730000028387, 2899559.663550000172108]
                ]]
            }
        }]
    }'''
    result_text = reprojector.transform_coordinates(
        input_path=sample_geojson_text,
        output_path=None,
        src_crs="EPSG:32648",
        dst_crs="EPSG:4326"
    )
    print("转换结果:", result_text)

    # 示例6: Shapefile转换
    print("\n示例6: Shapefile转换")
    reprojector.transform_coordinates(
        input_path="G:\\testcode\\ceshitiff\\aaaa.shp",
        output_path="G:\\testcode\\ceshitiff\\output_shp.shp",
        src_crs="EPSG:32648",
        dst_crs="EPSG:4326",
        progress_callback=gdal.TermProgress_nocb
    )

    # 示例7: 错误处理案例
    print("\n示例7: 错误处理案例")
    try:
        # 不存在的文件
        reprojector.transform_coordinates(
            input_path="G:\\testcode\\ceshitiff\\nonexistent.geojson",
            output_path="G:\\testcode\\ceshitiff\\output.geojson"
        )
    except Exception as e:
        print(f"捕获到预期错误: {str(e)}")

    # 不支持的格式
    try:
        reprojector.transform_coordinates(
            input_path="G:\\testcode\\ceshitiff\\unsupported.txt",
            output_path="G:\\testcode\\ceshitiff\\output.txt"
        )
    except Exception as e:
        print(f"捕获到预期错误: {str(e)}")

    # ========== 新增批量处理示例 ==========
    print("\n=== 新增批量处理示例 ===")

    # 示例8: 同类型文件批量转换(TIF)
    print("\n示例8: 同类型文件批量转换(TIF)")
    try:
        batch_results = reprojector.batch_transform_coordinates(
            input_files=["G:\\testcode\\ceshitiff\\ffff.tif", "G:\\testcode\\ceshitiff\\world.tif"],
            output_dir="G:\\testcode\\ceshitiff\\output",
            src_crs="EPSG:32648",
            dst_crs="EPSG:4326"
        )
        print("批量处理结果:", batch_results)
    except Exception as e:
        print(f"批量处理失败: {str(e)}")

    # 示例9: 七参数批量转换(TIF)
    print("\n示例9: 七参数批量转换(TIF)")
    try:
        # 七参数示例值 [ΔX(m), ΔY(m), ΔZ(m), rx(秒), ry(秒), rz(秒), scale(ppm)]
        seven_params = {
            "dx": 100, "dy": 200, "dz": 50,
            "rx": 0.1, "ry": 0.2, "rz": 0.3,
            "scale": 1.0001
        }
        batch_results = reprojector.batch_transform_coordinates(
            input_files=["G:\\testcode\\ceshitiff\\ffff.tif", "G:\\testcode\\ceshitiff\\world.tif"],
            output_dir="G:\\testcode\\ceshitiff\\output_7param",
            src_crs="EPSG:32648",  # 源坐标系
            dst_crs="EPSG:4326",    # 目标坐标系
            seven_params=seven_params,
            precision="double"      # 建议使用双精度进行七参数转换
        )
        print("七参数批量处理结果:", batch_results)
    except Exception as e:
        print(f"七参数批量处理失败: {str(e)}")

    # 示例9: 同类型文件批量转换(GeoJSON)
    print("\n示例9: 同类型文件批量转换(GeoJSON)")
    try:
        batch_results = reprojector.batch_transform_coordinates(
            input_files=["G:\\testcode\\ceshitiff\\output_actual_polygon.geojson", "G:\\testcode\\ceshitiff\\output_bounds.geojson"],
            output_dir="G:\\testcode\\ceshitiff\\output",
            src_crs="EPSG:32648",
            dst_crs="EPSG:4326"
        )
        print("批量处理结果:", batch_results)
    except Exception as e:
        print(f"批量处理失败: {str(e)}")

    # 示例10: 混合类型文件批量转换(会失败)
    print("\n示例10: 混合类型文件批量转换(会失败)")
    try:
        batch_results = reprojector.batch_transform_coordinates(
            input_files=["G:\\testcode\\ceshitiff\\ffff.tif", "G:\\testcode\\ceshitiff\\output_actual_polygon.geojson"],
            output_dir="batch_output"
        )
        print("批量处理结果:", batch_results)
    except Exception as e:
        print(f"预期中的批量处理失败: {str(e)}")


