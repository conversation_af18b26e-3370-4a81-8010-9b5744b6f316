# 使用 camrand/pydal:latest 作为基础镜像
FROM skfc-python-gdal-app:latest

# 设置时区（可选）
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置工作目录
WORKDIR /app

# 复制项目代码到工作目录
COPY . /app/

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 运行数据库迁移
RUN python manage.py migrate

# 暴露端口（根据您的项目配置，通常为8000）
EXPOSE 8000

# 启动 Django 开发服务器
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
