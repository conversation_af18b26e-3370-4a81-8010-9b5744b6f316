import os
import tempfile
import logging
import threading
import time
from collections import defaultdict
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from osgeo import gdal, osr
from PIL import Image
import numpy as np

# 启用 GDAL 异常模式
gdal.UseExceptions()

# 全局 GDAL 配置函数
def configure_gdal(cache_max=None, num_threads=None):
    """
    配置 GDAL 全局参数：
    - cache_max: 内存缓存大小（字节），例如 512*1024*1024
    - num_threads: GDAL 内部线程数，如 'ALL_CPUS' 或整数
    """
    if cache_max is not None:
        try:
            gdal.SetCacheMax(cache_max)
            logging.getLogger(__name__).info(f"GDAL cache max 设置为 {cache_max} bytes")
        except Exception as e:
            logging.getLogger(__name__).warning(f"设置 GDAL cache max 失败: {e}")
    if num_threads is not None:
        try:
            os.environ['GDAL_NUM_THREADS'] = str(num_threads)
            logging.getLogger(__name__).info(f"GDAL_NUM_THREADS 设置为 {num_threads}")
        except Exception as e:
            logging.getLogger(__name__).warning(f"设置 GDAL_NUM_THREADS 失败: {e}")

# 源 TIFF overviews 管理
def ensure_overviews(src_path, overview_levels):
    """
    检查并构建源 TIFF overviews（如果用户允许）。
    overview_levels: list of int, e.g. [2,4,8,16]
    注意：此操作会修改源 TIFF 或生成 .ovr，需确保用户可接受。
    """
    try:
        ds = gdal.Open(src_path, gdal.GA_Update)
    except Exception as e:
        logging.getLogger(__name__).warning(f"无法以写模式打开源 TIFF 构建 overviews: {e}")
        return
    if ds is None:
        logging.getLogger(__name__).warning("无法打开源 TIFF 构建 overviews")
        return
    try:
        band = ds.GetRasterBand(1)
        count = band.GetOverviewCount()
        if count > 0:
            logging.getLogger(__name__).info(f"已有 {count} 个 overview，跳过构建")
        else:
            logging.getLogger(__name__).info(f"开始构建 overviews: levels={overview_levels}")
            ds.BuildOverviews('NEAREST', overview_levels)
            logging.getLogger(__name__).info("overviews 构建完成")
    except Exception as e:
        logging.getLogger(__name__).warning(f"构建 overviews 失败: {e}")
    finally:
        ds = None

class TiffDyProcessor:
    """
    处理 TIFF 文件的动态瓦片生成类，保持外部接口 __init__ 和 process_tiles() 不变，
    内部集成 VRT 虚拟重投影、GDAL 配置、overview 支持、跨经度 wrap 处理、
    线程本地 Dataset 缓存、任务分块调度、性能统计与 Summary、重试机制等优化。
    """

    def __init__(self, epsg_code, tiff_path, min_level, max_level):
        """
        初始化，接口保持不变：
        - epsg_code: 目标 EPSG 代码（字符串或整数）
        - tiff_path: 源 TIFF 文件路径
        - min_level: 最小瓦片级别
        - max_level: 最大瓦片级别
        """
        # 日志配置
        logging.basicConfig(
            level=logging.INFO,
            format='[%(asctime)s] [%(levelname)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        self.logger = logging.getLogger(__name__)

        # 解析参数
        try:
            self.epsg_code = int(epsg_code)
        except Exception:
            raise ValueError(f"无效的 EPSG 代码: {epsg_code}")
        self.tiff_path = tiff_path
        self.min_level = int(min_level)
        self.max_level = int(max_level)
        if self.min_level < 0 or self.max_level < self.min_level:
            raise ValueError("级别范围错误：min_level 应 >=0，且 max_level >= min_level")

        # 常量
        self.tile_size = 256

        # 可配置属性（用户可在实例化后修改）
        self.num_workers = 16                 # 并发线程数
        self.use_overviews = True            # 是否使用/构建 overviews
        self.overview_levels = [2, 4, 8, 16] # 构建 overviews 的采样因子
        self.build_overviews_auto = False    # 是否自动构建 overviews（若 True，会修改源 TIFF）
        self.gdal_cache_max = 256 * 1024 * 1024  # GDAL 缓存大小，字节
        self.gdal_num_threads = 'ALL_CPUS'       # GDAL 内部线程数
        self.wrap_longitude = True           # 对地理 CRS 是否处理跨经度 wrap
        self.retry_count = 1                 # 单瓦片重试次数
        self.downsample_vrt = False          # 是否对低 zoom 级别创建下采样 VRT
        self.downsample_threshold = 2        # zoom <= threshold 使用下采样 VRT
        # VRT 管理和 metrics
        self._vrt_path = None
        self._thread_local = threading.local()
        self._metrics_lock = threading.Lock()
        self._metrics = defaultdict(lambda: {'total':0, 'success':0, 'fail':0, 'skip':0, 'time':0.0})

        # 目标 SRS
        self.dst_srs = osr.SpatialReference()
        if self.dst_srs.ImportFromEPSG(self.epsg_code) != 0:
            # 部分 GDAL 版本返回 None 或 0，忽略此返回值，但若 EPSG 不存在，后续 Warp 会失败
            pass

    def _validate_epsg(self):
        """
        验证 EPSG 是否有效。若无效，抛出异常。
        """
        tmp_srs = osr.SpatialReference()
        if tmp_srs.ImportFromEPSG(self.epsg_code) != 0:
            # 某些 GDAL 版本 ImportFromEPSG 返回 None 或 0，视具体情况而定
            # 这里尝试检测投影 WKT 是否为空
            try:
                wkt = tmp_srs.ExportToWkt()
                if not wkt:
                    raise ValueError
            except Exception:
                raise ValueError(f"无效或不支持的 EPSG 代码: {self.epsg_code}")

    def process_tiles(self):
        """
        主流程：保持接口不变。集成 GDAL 配置、overview 管理、VRT 创建、边界获取、
        任务调度并发裁剪、性能统计与 Summary、资源清理等。
        """
        start_all = time.time()
        # 配置 GDAL
        configure_gdal(self.gdal_cache_max, self.gdal_num_threads)

        # 验证 EPSG
        try:
            self._validate_epsg()
        except Exception as e:
            raise RuntimeError(f"EPSG 校验失败: {e}")

        # 检查源 TIFF 存在并打开
        if not os.path.exists(self.tiff_path):
            raise FileNotFoundError(f"指定的 TIFF 文件不存在: {self.tiff_path}")
        try:
            src_ds = gdal.Open(self.tiff_path, gdal.GA_ReadOnly)
        except Exception as e:
            raise RuntimeError(f"无法打开源 TIFF: {e}")
        if src_ds is None:
            raise RuntimeError(f"无法打开源 TIFF: {self.tiff_path}")

        # 检查投影信息
        proj_wkt = src_ds.GetProjection()
        if not proj_wkt:
            src_ds = None
            raise RuntimeError("源 TIFF 无投影信息，无法生成瓦片")
        # GeoTransform 旋转检测
        try:
            gt = src_ds.GetGeoTransform()
            if abs(gt[2]) > 1e-6 or abs(gt[4]) > 1e-6:
                self.logger.warning(
                    "源 TIFF 存在旋转/仿射倾斜 (GeoTransform[2] or [4] 非0)，"
                    "裁剪范围可能不精确，建议使用无旋转的正射纠正影像以获得最佳效果。"
                )
        except Exception:
            pass

        # 构建或使用 overviews
        if self.use_overviews and self.build_overviews_auto:
            ensure_overviews(self.tiff_path, self.overview_levels)

        # 创建 VRT 管理
        try:
            self._create_vrt(src_ds, proj_wkt)
        except Exception as e:
            src_ds = None
            raise RuntimeError(f"创建重投影 VRT 失败: {e}")
        # 关闭 src_ds
        src_ds = None

        # 打开 VRT Dataset 获取边界
        try:
            vrt_ds = gdal.Open(self._vrt_path, gdal.GA_ReadOnly)
        except Exception as e:
            self._cleanup_vrt()
            raise RuntimeError(f"无法打开 VRT 数据集: {e}")
        if vrt_ds is None:
            self._cleanup_vrt()
            raise RuntimeError("无法打开 VRT 数据集")
        xmin, xmax, ymin, ymax = self._get_dataset_bounds(vrt_ds)
        self.logger.info(f"VRT 重投影后边界 (EPSG {self.epsg_code}): xmin={xmin}, xmax={xmax}, ymin={ymin}, ymax={ymax}")
        vrt_ds = None

        # 计算任务列表并并发处理
        total_tiles = 0
        # ThreadPoolExecutor
        with ThreadPoolExecutor(max_workers=self.num_workers) as executor:
            future_to_tile = {}
            # 按 zoom 分批提交，提高局部性
            for zoom in range(self.min_level, self.max_level + 1):
                # 计算索引范围，考虑 wrap
                try:
                    ranges = self._compute_tile_ranges_for_zoom(xmin, xmax, ymin, ymax, zoom)
                except Exception as e:
                    self.logger.warning(f"计算级别 {zoom} 瓦片索引失败: {e}")
                    continue
                # 对于每个段，生成 tile 列表
                zoom_tile_count = 0
                for (min_row, max_row, min_col, max_col) in ranges:
                    if min_row > max_row or min_col > max_col:
                        continue
                    zoom_tile_count += (max_row - min_row + 1) * (max_col - min_col + 1)
                    # 按行分批：确保同一线程更可能处理相邻行
                    for row in range(min_row, max_row + 1):
                        for col in range(min_col, max_col + 1):
                            future = executor.submit(self._process_single_tile_with_retry, zoom, col, row)
                            future_to_tile[future] = (zoom, col, row)
                if zoom_tile_count > 0:
                    self.logger.info(f"Level {zoom}: 计划生成瓦片 {zoom_tile_count} 个")
                total_tiles += zoom_tile_count

            if total_tiles == 0:
                self._cleanup_vrt()
                self.logger.info("无瓦片需要生成，退出")
                return

            self.logger.info(f"待生成瓦片总数: {total_tiles}")

            # 等待并收集结果
            for future in as_completed(future_to_tile):
                zoom, col, row = future_to_tile[future]
                try:
                    status = future.result()
                    # status 已在 _process_single_tile_with_retry 内记录 metrics
                except Exception as e:
                    # 如果未来抛异常，记录失败
                    self._record_metric(zoom, 'fail', 0.0)
                    self.logger.error(f"瓦片 {zoom}/{col}/{row} 生成时抛异常: {e}")

        # 完成所有任务后 Summary
        self._report_metrics()
        end_all = time.time()
        self.logger.info(f"全部瓦片生成完成，总耗时: {end_all - start_all:.2f}s")

        # 清理 VRT 及线程本地 Dataset
        self._cleanup_vrt()

    def _create_vrt(self, src_ds, proj_wkt):
        """
        创建或使用 VRT：
        - 若源 CRS 与目标 EPSG 相同，直接使用源 TIFF 路径
        - 否则使用 gdal.Warp(format='VRT') 生成虚拟重投影 VRT Dataset，并写入临时 .vrt 文件
        """
        # 判断源 CRS
        need_vrt = True
        try:
            src_srs = osr.SpatialReference()
            src_srs.ImportFromWkt(proj_wkt)
            src_auth = src_srs.GetAttrValue('AUTHORITY', 1)
            if src_auth is not None and int(src_auth) == self.epsg_code:
                need_vrt = False
        except Exception:
            need_vrt = True

        if not need_vrt:
            # 直接使用源 TIFF
            self._vrt_path = self.tiff_path
            self.logger.info("源 TIFF CRS 与目标相同，直接使用源数据，不创建 VRT")
            return

        # 生成 VRT 重投影：使用 gdal.Warp format='VRT'
        warp_opts = gdal.WarpOptions(
            format='VRT',
            dstSRS=self.dst_srs.ExportToWkt(),
            resampleAlg='bilinear',
            # 可根据需求添加 NUM_THREADS 等参数
        )
        vrt_ds = gdal.Warp('', src_ds, options=warp_opts)
        if vrt_ds is None:
            raise RuntimeError("gdal.Warp 生成 VRT 失败，返回 None")
        # 写入临时 .vrt
        tmp_vrt = tempfile.NamedTemporaryFile(delete=False, suffix=".vrt")
        vrt_path = tmp_vrt.name
        tmp_vrt.close()
        vrt_driver = gdal.GetDriverByName('VRT')
        out_vrt_ds = vrt_driver.CreateCopy(vrt_path, vrt_ds)
        if out_vrt_ds is None:
            raise RuntimeError("写入 VRT 文件失败")
        # 释放内存 VRT Dataset
        vrt_ds = None
        out_vrt_ds = None
        self._vrt_path = vrt_path
        self.logger.info(f"生成临时重投影 VRT: {vrt_path}")

    def _get_dataset_bounds(self, ds):
        """
        获取 Dataset 边界 (xmin, xmax, ymin, ymax)：
        - 若无旋转 GeoTransform，直接计算
        - 否则用四角近似，并警告
        - 对 Web Mercator (EPSG:3857) 做合法范围裁剪
        """
        gt = ds.GetGeoTransform()
        xsize = ds.RasterXSize
        ysize = ds.RasterYSize
        if abs(gt[2]) < 1e-6 and abs(gt[4]) < 1e-6:
            xmin = gt[0]
            ymax = gt[3]
            xmax = gt[0] + gt[1] * xsize
            ymin = gt[3] + gt[5] * ysize
        else:
            xs = []
            ys = []
            corners = [(0, 0), (xsize, 0), (0, ysize), (xsize, ysize)]
            for i, j in corners:
                x = gt[0] + i * gt[1] + j * gt[2]
                y = gt[3] + i * gt[4] + j * gt[5]
                xs.append(x); ys.append(y)
            xmin = min(xs); xmax = max(xs)
            ymin = min(ys); ymax = max(ys)
            self.logger.warning("VRT Dataset 存在旋转 GeoTransform，仅使用四角近似边界")
        # Web Mercator 裁剪
        if self.epsg_code == 3857:
            maxm = 20037508.342789244
            xmin = max(xmin, -maxm); xmax = min(xmax, maxm)
            ymin = max(ymin, -maxm); ymax = min(ymax, maxm)
        return xmin, xmax, ymin, ymax

    def _compute_tile_ranges_for_zoom(self, xmin, xmax, ymin, ymax, zoom):
        """
        计算给定 zoom 级别下的瓦片行列范围列表，考虑 wrap_longitude:
        返回列表，每项为 (min_row, max_row, min_col, max_col)。
        """
        # 若地理 CRS (4326/4490) 且 wrap_longitude:
        if self.wrap_longitude and self.epsg_code in (4326, 4490):
            # 可能跨 -180/180，需要拆分
            segments = self._split_longitude_ranges(xmin, xmax)
        else:
            segments = [(xmin, xmax)]
        ranges = []
        for seg in segments:
            seg_min_x, seg_max_x = seg
            # 计算行列
            min_row, max_row, min_col, max_col = self._compute_range_segment(
                seg_min_x, seg_max_x, ymin, ymax, zoom
            )
            if min_row <= max_row and min_col <= max_col:
                ranges.append((min_row, max_row, min_col, max_col))
        return ranges

    def _split_longitude_ranges(self, min_x, max_x):
        """
        针对地理 CRS 跨越 -180/180 情形，拆分为一或两个区间：
        返回 list of (seg_min_x, seg_max_x)，值在 [-180,180] 区间内或相对处理跨域。
        """
        # 全覆盖
        if max_x - min_x >= 360.0:
            return [(-180.0, 180.0)]
        segs = []
        # 归一化到 [-180,180] 便于判断
        norm_min = min_x
        norm_max = max_x
        # 若 min_x < -180 或 max_x > 180，拆分
        if min_x < -180.0 or max_x > 180.0:
            # 跨界两段
            # 例如 min_x < -180: segment1: [min_x+360, 180], segment2: [-180, max_x]
            if min_x < -180.0:
                segs.append((min_x + 360.0, 180.0))
                segs.append((-180.0, max_x))
            elif max_x > 180.0:
                segs.append((min_x, 180.0))
                segs.append((-180.0, max_x - 360.0))
        else:
            segs.append((min_x, max_x))
        return segs

    def _compute_range_segment(self, min_x, max_x, min_y, max_y, zoom):
        """
        对单个经度段计算 tile 行列范围，不做 wrap。
        返回 (min_row, max_row, min_col, max_col)，并截断到 [0, 2^zoom -1]。
        """
        tile_size = self.tile_size
        if self.epsg_code == 3857:
            initial_resolution = 2 * 20037508.342789244 / tile_size
            res = initial_resolution / (2 ** zoom)
            min_col = int((min_x + 20037508.342789244) / (tile_size * res))
            max_col = int((max_x + 20037508.342789244) / (tile_size * res))
            min_row = int((20037508.342789244 - max_y) / (tile_size * res))
            max_row = int((20037508.342789244 - min_y) / (tile_size * res))
            max_index = (2 ** zoom) - 1
            min_col = max(min_col, 0); max_col = min(max_col, max_index)
            min_row = max(min_row, 0); max_row = min(max_row, max_index)
        elif self.epsg_code in (4326, 4490):
            initial_resolution = 360.0 / tile_size
            res = initial_resolution / (2 ** zoom)
            min_col = int((min_x + 180.0) / (tile_size * res))
            max_col = int((max_x + 180.0) / (tile_size * res))
            min_row = int((90.0 - max_y) / (tile_size * res))
            max_row = int((90.0 - min_y) / (tile_size * res))
            max_index = (2 ** zoom) - 1
            min_col = max(min_col, 0); max_col = min(max_col, max_index)
            min_row = max(min_row, 0); max_row = min(max_row, max_index)
        else:
            raise Exception(f"不支持的坐标系: {self.epsg_code}")
        return min_row, max_row, min_col, max_col

    def _process_single_tile_with_retry(self, zoom, tile_col, tile_row):
        """
        对 _process_single_tile 包装重试逻辑，记录 metrics。
        返回状态字符串。
        """
        attempt = 0
        while attempt <= self.retry_count:
            t0 = time.time()
            try:
                status = self._process_single_tile(zoom, tile_col, tile_row)
                # status: 'success' / 'skip' / 'fail'
                elapsed = time.time() - t0
                self._record_metric(zoom, status, elapsed)
                return status
            except Exception as e:
                attempt += 1
                if attempt > self.retry_count:
                    elapsed = time.time() - t0
                    self._record_metric(zoom, 'fail', elapsed)
                    self.logger.error(f"瓦片 {zoom}/{tile_col}/{tile_row} 重试后失败: {e}")
                    return 'fail'
                else:
                    self.logger.warning(f"瓦片 {zoom}/{tile_col}/{tile_row} 处理异常，重试 {attempt}/{self.retry_count}: {e}")
        # 不应到这里
        return 'fail'

    def _process_single_tile(self, zoom, tile_col, tile_row):
        """
        单瓦片生成逻辑，线程中执行。利用线程本地缓存打开 VRT Dataset。
        返回 'success', 'skip' 或 'fail'。
        """
        # 1. 获取线程本地 VRT Dataset
        ds = getattr(self._thread_local, 'vrt_ds', None)
        if ds is None:
            try:
                ds = gdal.Open(self._vrt_path, gdal.GA_ReadOnly)
            except Exception as e:
                self.logger.warning(f"线程无法打开 VRT: {e}")
                return 'fail'
            if ds is None:
                self.logger.warning("线程无法打开 VRT Dataset，跳过瓦片")
                return 'fail'
            self._thread_local.vrt_ds = ds

        # 2. 计算瓦片边界 (x_min, y_max, x_max, y_min)
        try:
            x_min, y_max, x_max, y_min = self._calculate_tile_bounds(zoom, tile_col, tile_row)
        except Exception as e:
            self.logger.warning(f"计算瓦片边界失败 {zoom}/{tile_col}/{tile_row}: {e}")
            return 'fail'

        # 3. 构造输出路径，与原逻辑类似
        base_dir = os.path.dirname(os.path.abspath(self.tiff_path))
        tile_cache_dir = os.path.join(base_dir, "tile_cache", str(self.epsg_code),
                                      str(zoom), str(tile_col))
        os.makedirs(tile_cache_dir, exist_ok=True)
        tile_cache_path = os.path.join(tile_cache_dir, f"{tile_row}.png")
        # 已存在且大小>0，跳过
        if os.path.exists(tile_cache_path) and os.path.getsize(tile_cache_path) > 0:
            return 'skip'

        # 4. 裁剪到内存
        try:
            tile_ds = gdal.Translate(
                '',
                ds,
                format='MEM',
                projWin=[x_min, y_max, x_max, y_min],
                width=self.tile_size,
                height=self.tile_size,
                outputType=ds.GetRasterBand(1).DataType
                # , bandList=[1,2,3]  # 可选：仅读取前三个波段以减少 I/O
            )
        except Exception as e:
            self.logger.warning(f"Translate 裁剪失败 {zoom}/{tile_col}/{tile_row}: {e}")
            return 'fail'
        if tile_ds is None:
            # 可能超出范围或无数据
            return 'skip'

        # 5. 转为 PIL Image 并透明化
        try:
            img = self._tile_ds_to_image(tile_ds)
        except Exception as e:
            self.logger.error(f"PIL 转换异常 {zoom}/{tile_col}/{tile_row}: {e}")
            tile_ds = None
            return 'fail'
        tile_ds = None
        if img is None:
            # 空瓦
            return 'skip'

        # 6. 保存 PNG
        try:
            img.save(tile_cache_path, format='PNG')
            # self.logger.info(f"瓦片已保存: {tile_cache_path}")
            return 'success'
        except Exception as e:
            self.logger.error(f"保存瓦片失败 {zoom}/{tile_col}/{tile_row}: {e}")
            return 'fail'

    def _calculate_tile_bounds(self, zoom, tile_col, tile_row):
        """
        计算瓦片边界 (x_min, y_max, x_max, y_min)：
        与常见 tile scheme 保持一致。
        """
        ts = self.tile_size
        if self.epsg_code == 3857:
            initial_resolution = 2 * 20037508.342789244 / ts
            res = initial_resolution / (2 ** zoom)
            x_min = -20037508.342789244 + tile_col * ts * res
            y_max = 20037508.342789244 - tile_row * ts * res
            x_max = x_min + ts * res
            y_min = y_max - ts * res
        elif self.epsg_code == 4326:
            initial_resolution = 360.0 / ts
            res = initial_resolution / (2 ** zoom)
            x_min = -180.0 + tile_col * ts * res
            y_max = 90.0 - tile_row * ts * res
            x_max = x_min + ts * res
            y_min = y_max - ts * res
        elif self.epsg_code == 4490:
            initial_resolution = 360.0 / ts
            res = initial_resolution / (2 ** zoom)
            x_min = -180.0 + tile_col * ts * res
            y_max = 90.0 - tile_row * ts * res
            x_max = x_min + ts * res
            y_min = y_max - ts * res
        else:
            raise Exception(f"不支持的坐标系: {self.epsg_code}")
        return x_min, y_max, x_max, y_min

    def _tile_ds_to_image(self, tile_ds):
        """
        将裁剪后的 MEM Dataset 转为 PIL Image 并透明化 NoData 区域；若空瓦返回 None。
        """
        band_count = tile_ds.RasterCount
        if band_count < 1:
            return None
        arrays = []
        ndvs = []
        for i in range(band_count):
            band = tile_ds.GetRasterBand(i+1)
            try:
                arr = band.ReadAsArray()
            except Exception:
                return None
            arrays.append(arr)
            try:
                ndv = band.GetNoDataValue()
            except Exception:
                ndv = None
            ndvs.append(ndv)
        # 构建 RGBA
        if band_count == 1:
            gray = arrays[0]
            img = Image.fromarray(gray).convert("RGBA")
            data = np.array(img)
            ndv = ndvs[0]
            if ndv is not None:
                mask = (gray == ndv)
                data[mask] = (0, 0, 0, 0)
            if np.all(data[:, :, 3] == 0):
                return None
            return Image.fromarray(data)
        elif band_count == 3:
            r, g, b = arrays
            img = Image.merge('RGB', [Image.fromarray(ch) for ch in (r, g, b)]).convert("RGBA")
            data = np.array(img)
            if all(ndv is not None for ndv in ndvs[:3]):
                mask = (r == ndvs[0]) & (g == ndvs[1]) & (b == ndvs[2])
                data[mask] = (0, 0, 0, 0)
            if np.all(data[:, :, 3] == 0):
                return None
            return Image.fromarray(data)
        elif band_count == 4:
            r, g, b, a = arrays
            img = Image.merge('RGBA', [Image.fromarray(ch) for ch in (r, g, b, a)])
            if np.all(a == 0):
                return None
            return img
        else:
            # 不支持其他波段数
            return None

    def _record_metric(self, zoom, status, elapsed):
        """
        记录每瓦片执行状态和耗时，供 Summary 使用。
        status: 'success', 'fail', 'skip'
        """
        with self._metrics_lock:
            m = self._metrics[zoom]
            m['total'] += 1
            if status == 'success':
                m['success'] += 1
            elif status == 'fail':
                m['fail'] += 1
            elif status == 'skip':
                m['skip'] += 1
            m['time'] += elapsed

    def _report_metrics(self):
        """
        打印 Summary 日志，包括每级别总数、成功/失败/跳过数、平均耗时。
        """
        self.logger.info("瓦片生成 Summary:")
        for zoom in sorted(self._metrics.keys()):
            info = self._metrics[zoom]
            total = info['total']
            success = info['success']
            skip = info['skip']
            fail = info['fail']
            total_time = info['time']
            avg = total_time / total if total > 0 else 0.0
            self.logger.info(f" Level {zoom}: total={total}, success={success}, skip={skip}, fail={fail}, avgTime={avg:.2f}s")
        # 可输出总体耗时已在 process_tiles 中打印

    def _cleanup_vrt(self):
        """
        清理临时 VRT 文件及线程本地 Dataset。
        """
        # 关闭线程本地 ds
        ds = getattr(self._thread_local, 'vrt_ds', None)
        if ds:
            try:
                # 关闭 Dataset：释放引用
                ds = None
                del self._thread_local.vrt_ds
            except Exception:
                pass
        # 删除临时 VRT 文件
        if self._vrt_path and self._vrt_path != self.tiff_path:
            try:
                os.remove(self._vrt_path)
                self.logger.info(f"已删除临时 VRT 文件: {self._vrt_path}")
            except Exception as e:
                self.logger.warning(f"删除临时 VRT 文件失败: {e}")
        self._vrt_path = None




# os.environ['PROJ_LIB'] = r'H:\conda\miniforge3\envs\py31013\Library\share\proj'
if __name__ == "__main__":
    epsg_code = "4490"
    tiff_path = r"G:\testcode\ceshitiff\aaa\world.tif"
    min_level = 0
    max_level = 5

    processor = TiffDyProcessor(epsg_code, tiff_path, min_level, max_level)
    processor.process_tiles()



# 如果作为脚本运行，示例调用方式保持不变
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="优化后的 TiffDyProcessor 调用示例")
    parser.add_argument('--epsg', '-e', required=True, help="目标 EPSG 代码，例如 3857")
    parser.add_argument('--tiff', '-i', required=True, help="源 TIFF 文件路径")
    parser.add_argument('--min', '-min', required=True, type=int, help="最小瓦片级别")
    parser.add_argument('--max', '-max', required=True, type=int, help="最大瓦片级别")
    # 可选配置参数（示例，可根据需要扩展）
    parser.add_argument('--workers', '-w', type=int, default=None, help="并发线程数，默认 8")
    parser.add_argument('--no-overviews', action='store_true', help="关闭 overview 支持")
    parser.add_argument('--build-overviews', action='store_true', help="自动构建 overviews（会修改源 TIFF）")
    parser.add_argument('--cache-max', type=int, default=None, help="GDAL cache max (bytes)，例如 268435456")
    parser.add_argument('--gdal-threads', default=None, help="GDAL_NUM_THREADS，例如 'ALL_CPUS' 或整数")
    parser.add_argument('--no-wrap', action='store_true', help="关闭跨经度 wrap 处理（仅地理 CRS）")
    parser.add_argument('--retry', type=int, default=None, help="单瓦片重试次数")
    args = parser.parse_args()

    processor = TiffDyProcessor(args.epsg, args.tiff, args.min, args.max)
    # 应用可选配置
    if args.workers is not None:
        processor.num_workers = args.workers
    if args.no_overviews:
        processor.use_overviews = False
    if args.build_overviews:
        processor.build_overviews_auto = True
    if args.cache_max is not None:
        processor.gdal_cache_max = args.cache_max
    if args.gdal_threads is not None:
        processor.gdal_num_threads = args.gdal_threads
    if args.no_wrap:
        processor.wrap_longitude = False
    if args.retry is not None:
        processor.retry_count = args.retry

    processor.process_tiles()
