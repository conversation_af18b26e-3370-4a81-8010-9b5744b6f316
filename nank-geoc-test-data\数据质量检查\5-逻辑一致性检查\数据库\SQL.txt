1、建模式
attr_check_demo_v12

2、建表，导入数据

CREATE TABLE "attr_check_demo_v12"."parks" (
  "park_id" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "park_name" varchar(50) COLLATE "pg_catalog"."default",
  "has_playground" bool,
  "geometry" geometry(POLYGON, 4326),
  CONSTRAINT "parks_pkey" PRIMARY KEY ("park_id")
)
;

ALTER TABLE "attr_check_demo_v12"."parks" 
  OWNER TO "postgres";


CREATE TABLE "attr_check_demo_v12"."facilities" (
  "facility_id" int4 NOT NULL DEFAULT nextval('"attr_check_demo_v12".facilities_facility_id_seq'::regclass),
  "facility_type" varchar(20) COLLATE "pg_catalog"."default",
  "geometry" geometry(POINT, 4326),
  CONSTRAINT "facilities_pkey" PRIMARY KEY ("facility_id")
)
;

ALTER TABLE "attr_check_demo_v12"."facilities" 
  OWNER TO "postgres";



INSERT INTO "attr_check_demo_v12"."facilities" ("facility_id", "facility_type", "geometry") VALUES (1, 'Playground', ST_GeomFromText('POINT(5 5)', 4326));
INSERT INTO "attr_check_demo_v12"."facilities" ("facility_id", "facility_type", "geometry") VALUES (2, 'Restroom', ST_GeomFromText('POINT(25 5)', 4326));
INSERT INTO "attr_check_demo_v12"."facilities" ("facility_id", "facility_type", "geometry") VALUES (3, 'Playground', ST_GeomFromText('POINT(100 100)', 4326));


INSERT INTO "attr_check_demo_v12"."parks" ("park_id", "park_name", "has_playground", "geometry") VALUES ('P-01', 'Central Park', 't', ST_GeomFromText('POLYGON((0 0, 10 0, 10 10, 0 10, 0 0))', 4326));
INSERT INTO "attr_check_demo_v12"."parks" ("park_id", "park_name", "has_playground", "geometry") VALUES ('P-02', 'Riverfront Park', 't', ST_GeomFromText('POLYGON((20 0, 30 0, 30 10, 20 10, 20 0))', 4326));
INSERT INTO "attr_check_demo_v12"."parks" ("park_id", "park_name", "has_playground", "geometry") VALUES ('P-03', NULL, 'f', ST_GeomFromText('POLYGON((0 20, 10 20, 10 30, 0 30, 0 20))', 4326));

