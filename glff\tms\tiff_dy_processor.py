# views.py
import os
import json
import uuid

from PIL import Image
from osgeo import gdal, osr
import numpy as np
from concurrent.futures import ThreadPoolExecutor

from django.http import JsonResponse
from django.views import View

from glff.dao.geo_api.geo_api import GeoApi

# 启用 GDAL 异常模式
gdal.UseExceptions()

# 全局线程池，根据实际情况调整最大线程数
executor = ThreadPoolExecutor(max_workers=20)

class TiffDyProcessor:
    """
    处理 TIFF 文件动态切片生成的类。
    支持根据传入的 EPSG 代码进行重投影（支持 EPSG:3857, EPSG:4326, EPSG:4490 等）。
    """
    def __init__(self, epsg_code, tiff_path, min_level, max_level,file_id,file_name):
        self.epsg_code = epsg_code
        self.tiff_path = tiff_path
        self.min_level = min_level
        self.max_level = max_level
        self.file_id = file_id
        self.file_name = file_name

    def process_tiles(self):
        if not os.path.exists(self.tiff_path):
            raise FileNotFoundError(f"指定的 TIFF 文件不存在: {self.tiff_path}")

        # 打开 TIFF 文件（假设原始坐标系为4326）
        dataset = gdal.Open(self.tiff_path, gdal.GA_ReadOnly)
        if not dataset:
            raise Exception(f"无法打开 TIFF 文件: {self.tiff_path}")

        # 获取原始投影信息，仅用于提示
        orig_geo = dataset.GetGeoTransform()
        orig_min_x = orig_geo[0]
        orig_max_y = orig_geo[3]
        orig_max_x = orig_min_x + orig_geo[1] * dataset.RasterXSize
        orig_min_y = orig_max_y + orig_geo[5] * dataset.RasterYSize
        print(f"原始 TIFF 范围（4326）：min_x={orig_min_x}, max_x={orig_max_x}, min_y={orig_min_y}, max_y={orig_max_y}")

        # 设置目标投影
        dst_srs = osr.SpatialReference()
        try:
            epsg_code_int = int(self.epsg_code)
            dst_srs.ImportFromEPSG(epsg_code_int)
        except Exception as e:
            raise ValueError(f"不支持的投影类型: {self.epsg_code}")

        # 重投影数据集到内存（目标坐标系）
        reprojected_dataset = gdal.Warp('', dataset, format='MEM', dstSRS=dst_srs)
        if not reprojected_dataset:
            raise Exception("重投影失败")

        # 使用重投影后的地理范围来计算瓦片范围
        reproj_geo = reprojected_dataset.GetGeoTransform()
        min_x = reproj_geo[0]
        max_y = reproj_geo[3]
        max_x = min_x + reproj_geo[1] * reprojected_dataset.RasterXSize
        min_y = max_y + reproj_geo[5] * reprojected_dataset.RasterYSize
        print(f"重投影后 TIFF 范围：min_x={min_x}, max_x={max_x}, min_y={min_y}, max_y={max_y}")

        # 遍历所有级别，动态生成切片。此处计算基于重投影后的范围，确保在目标坐标系下每级别完整覆盖
        for tile_matrix in range(self.min_level, self.max_level + 1):
            min_row, max_row, min_col, max_col = self.get_tile_count_for_level(
                tile_matrix, min_x, max_x, min_y, max_y, epsg_code_int, 256
            )
            print(f"Level {tile_matrix}: min_row={min_row}, max_row={max_row}, min_col={min_col}, max_col={max_col}")
            for tile_row in range(min_row, max_row + 1):
                for tile_col in range(min_col, max_col + 1):
                    self.generate_and_save_tile(reprojected_dataset, tile_matrix, tile_col, tile_row)

        # 保存切片信息和切片服务到数据库中
        try:
            id = uuid.uuid4().hex
            apiUrl = f"glff/tif/tiles/dy-view/"+self.file_id+"/{epsgcode}/{z}/{x}/{y}";
            GeoApi.create_geo_api(
                id=id,
                url= apiUrl,
                api_type= "TMS",
                file_id= self.file_id,
                remarks= self.file_name,
                params= (
                        f'{{"apiName": "{self.file_name}", '
                        f'"epsg_code": "指定的投影的编码", '
                        f'"z": "瓦片z序列号", '
                        f'"x": "瓦片x序列号", '
                        f'"y": "瓦片y序列号", '
                        f'"demo": "{apiUrl}"}}'
                    )
            )
        except Exception as e:
            raise ValueError(f"保存服务信息错误: {e.message}")

    def generate_and_save_tile(self, reprojected_dataset, tile_matrix, tile_col, tile_row):
        tile_size = 256
        x_min, y_max, x_max, y_min = self.calculate_tile_bounds(tile_matrix, tile_col, tile_row, tile_size, int(self.epsg_code))

        # 构造瓦片缓存路径（按照 TMS 结构）
        tile_cache_dir = os.path.join(os.path.dirname(self.tiff_path), "tile_cache", str(self.epsg_code))
        tile_cache_path = os.path.join(tile_cache_dir, str(tile_matrix), str(tile_col), f"{tile_row}.png")

        if os.path.exists(tile_cache_path):
            print(f"瓦片已存在: {tile_cache_path}")
            return

        future = executor.submit(self.create_tile, reprojected_dataset, x_min, y_max, x_max, y_min, tile_size)
        tile_image = future.result()
        if tile_image is None:
            print(f"提取瓦片失败: Level {tile_matrix} tile_col={tile_col} tile_row={tile_row}")
            return

        no_data_value = 0
        tile_image = self.ensure_transparency(tile_image, no_data_value)

        if self.is_image_empty(tile_image):
            print(f"生成的瓦片为空: Level {tile_matrix} tile_col={tile_col} tile_row={tile_row}")
            return

        os.makedirs(os.path.dirname(tile_cache_path), exist_ok=True)
        executor.submit(self.save_tile_to_file, tile_cache_path, tile_image)

    def calculate_tile_bounds(self, tile_matrix, tile_col, tile_row, tile_size, epsg_code):
        # 根据目标 EPSG 坐标系计算全局瓦片边界
        if epsg_code == 3857:
            initial_resolution = 2 * 20037508.342789244 / tile_size
            resolution = initial_resolution / (2 ** tile_matrix)
            x_min = -20037508.342789244 + tile_col * tile_size * resolution
            y_max = 20037508.342789244 - tile_row * tile_size * resolution
            x_max = x_min + tile_size * resolution
            y_min = y_max - tile_size * resolution
        elif epsg_code == 4326 or epsg_code == 4490:
            initial_resolution = 360.0 / tile_size
            resolution = initial_resolution / (2 ** tile_matrix)
            x_min = -180 + tile_col * tile_size * resolution
            y_max = 90 - tile_row * tile_size * resolution
            x_max = x_min + tile_size * resolution
            y_min = y_max - tile_size * resolution
        else:
            raise Exception(f"不支持的坐标系: {epsg_code}")
        return x_min, y_max, x_max, y_min

    def create_tile(self, reprojected_dataset, x_min, y_max, x_max, y_min, tile_size):
        tile_dataset = gdal.Translate(
            '',
            reprojected_dataset,
            format='MEM',
            projWin=[x_min, y_max, x_max, y_min],
            width=tile_size,
            height=tile_size,
            outputType=gdal.GDT_Byte
        )
        if not tile_dataset:
            return None

        band_count = tile_dataset.RasterCount
        bands_data = [tile_dataset.GetRasterBand(i + 1).ReadAsArray() for i in range(band_count)]
        if band_count == 3:
            image = Image.merge('RGB', [Image.fromarray(band) for band in bands_data])
            image = image.convert("RGBA")
        elif band_count == 4:
            image = Image.merge('RGBA', [Image.fromarray(band) for band in bands_data])
        elif band_count == 1:
            gray_band = Image.fromarray(bands_data[0])
            image = Image.merge('RGB', [gray_band, gray_band, gray_band])
            image = image.convert("RGBA")
        else:
            raise Exception(f"不支持的波段数: {band_count}")
        return image

    def ensure_transparency(self, image, no_data_value):
        image = image.convert("RGBA")
        data = np.array(image)
        if no_data_value is not None:
            mask = (data[:, :, 0] == no_data_value) & (data[:, :, 1] == no_data_value) & (data[:, :, 2] == no_data_value)
            data[mask] = (0, 0, 0, 0)
        return Image.fromarray(data)

    def is_image_empty(self, image):
        data = np.array(image)
        return np.all(data[:, :, 3] == 0)

    def save_tile_to_file(self, tile_cache_path, tile_image):
        tile_image.save(tile_cache_path, format='PNG')
        print(f"瓦片已保存: {tile_cache_path}")

    def get_tile_count_for_level(self, level, min_x, max_x, min_y, max_y, epsg_code, tile_size):
        # 根据目标 EPSG 坐标系计算重投影后数据的覆盖范围对应的瓦片行列
        if epsg_code == 3857:
            initial_resolution = 2 * 20037508.342789244 / tile_size
            resolution = initial_resolution / (2 ** level)
        elif epsg_code in [4326, 4490]:
            initial_resolution = 360.0 / tile_size
            resolution = initial_resolution / (2 ** level)
        else:
            raise Exception(f"不支持的坐标系: {epsg_code}")

        if epsg_code == 3857:
            # 这里直接使用全局 Web Mercator 坐标计算公式
            min_col = int((min_x + 20037508.342789244) / (tile_size * resolution))
            max_col = int((max_x + 20037508.342789244) / (tile_size * resolution))
        else:
            min_col = int((min_x + 180) / (tile_size * resolution))
            max_col = int((max_x + 180) / (tile_size * resolution))

        if epsg_code == 3857:
            min_row = int((20037508.342789244 - max_y) / (tile_size * resolution))
            max_row = int((20037508.342789244 - min_y) / (tile_size * resolution))
        else:
            min_row = int((90 - max_y) / (tile_size * resolution))
            max_row = int((90 - min_y) / (tile_size * resolution))

        # 为保证计算结果正确，补充边界检查
        min_col = max(min_col, 0)
        min_row = max(min_row, 0)
        max_col = max(max_col, min_col)
        max_row = max(max_row, min_row)

        if epsg_code == 3857:
            max_col = min(max_col, int((20037508.342789244 * 2) / (tile_size * resolution)) - 1)
            max_row = min(max_row, int((20037508.342789244 * 2) / (tile_size * resolution)) - 1)
        else:
            max_col = min(max_col, int(360 / (tile_size * resolution)) - 1)
            max_row = min(max_row, int(180 / (tile_size * resolution)) - 1)

        return min_row, max_row, min_col, max_col


class GenerateDyTilesView(View):
    """
    通过 POST 请求接收 JSON 参数：
    {
      "epsg_code": "3857",
      "tiff_path": "G:\\testcode\\ceshitiff\\aaa\\world.tif",
      "min_level": "0",
      "max_level": "5"
    }
    然后动态生成切片。
    """
    def post(self, request, *args, **kwargs):
        try:
            # 尝试从 JSON 中解析参数
            try:
                data = json.loads(request.body)
            except Exception:
                data = request.POST

            epsg_code = data.get('epsg_code')
            tiff_path = data.get('tiff_path')
            min_level = data.get('min_level')
            max_level = data.get('max_level')
            file_id = data.get('file_id')
            file_name = data.get('file_name')

            if not all([epsg_code, tiff_path, min_level, max_level,file_id,file_name]):
                return JsonResponse({'error': '缺少必要参数'}, status=400)

            try:
                min_level = int(min_level)
                max_level = int(max_level)
            except Exception:
                return JsonResponse({'error': 'min_level 和 max_level 必须为整数'}, status=400)

            processor = TiffDyProcessor(epsg_code, tiff_path, min_level, max_level,file_id,file_name)
            processor.process_tiles()

            return JsonResponse({'message': '切片生成成功'})
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
