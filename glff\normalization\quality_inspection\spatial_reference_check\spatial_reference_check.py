# -*- coding: utf-8 -*-
"""
空间参考检查模块 (GCS/PCS)

本模块提供了一套用于检查地理空间数据空间参考系统 (CRS) 的工具。
此版本允许用户分别传入地理坐标系(GCS)和投影坐标系(PCS)的标准，
提供了更灵活和精细的检查能力。
"""

import geopandas as gpd
import rasterio
import simplejson as json
from pyproj import CRS
from pyproj.exceptions import CRSError
from typing import Dict, Any, Optional, Tuple
import os
import shutil
import warnings
import numpy as np


# --- 核心实现部分 ---

class CheckResult:
    """封装单项检查结果的标准化数据结构。"""

    def __init__(self, status: str, message: str, details: Optional[Dict[str, Any]] = None):
        if status not in ['PASSED', 'FAILED', 'ERROR']:
            raise ValueError("状态必须是 'PASSED', 'FAILED', 或 'ERROR' 之一")
        self.status = status
        self.message = message
        self.details = details or {}

    def to_dict(self) -> Dict[str, Any]:
        return {"status": self.status, "message": self.message, "details": self.details}

    def __repr__(self) -> str:
        return f"CheckResult(status='{self.status}', message='{self.message}')"


class SpatialReferenceChecker:
    """
    空间参考检查器 v3.0。
    初始化时分别接收地理坐标系(GCS)和可选的投影坐标系(PCS)标准。
    """

    def __init__(self, standard_gcs_str: str, standard_pcs_str: Optional[str] = None):
        """
        初始化检查器。这是使用该工具的第一步。

        调用说明:
            创建实例时，必须提供一个地理坐标系(GCS)的标准，并可选择性地提供一个
            投影坐标系(PCS)的标准。

        示例:
            # 案例1: 标准为CGCS2000地理坐标系，无投影要求
            # (例如，只要求数据是基于2000国家大地坐标系的经纬度数据)
            checker_gcs_only = SpatialReferenceChecker(standard_gcs_str="EPSG:4490")

            # 案例2: 标准为CGCS2000地理坐标系，且投影为高斯-克吕格投影 (CM 111E)
            checker_gcs_and_pcs = SpatialReferenceChecker(
                standard_gcs_str="EPSG:4490",       # 地理坐标系标准
                standard_pcs_str="EPSG:4547"        # 投影坐标系标准
            )

        Args:
            standard_gcs_str (str): 项目标准的地理坐标系(GCS)定义。
                                    必须是一个有效的地理坐标系。
            standard_pcs_str (Optional[str]): 项目标准的可选投影坐标系(PCS)定义。
                                              如果提供，必须是一个有效的投影坐标系。
                                              如果为 None，则不检查投影。

        Raises:
            CRSError: 如果提供的字符串无效或类型不匹配 (例如，给GCS传入了一个投影坐标系)。
        """
        try:
            self.standard_gcs = CRS.from_string(standard_gcs_str)
            if self.standard_gcs.is_projected:
                raise CRSError(f"提供的标准地理坐标系(GCS) '{standard_gcs_str}' 实际上是一个投影坐标系。")
        except CRSError as e:
            raise CRSError(f"无效的标准GCS定义 '{standard_gcs_str}': {e}") from e

        self.standard_pcs = None
        if standard_pcs_str:
            try:
                self.standard_pcs = CRS.from_string(standard_pcs_str)
                if not self.standard_pcs.is_projected:
                    raise CRSError(f"提供的标准投影坐标系(PCS) '{standard_pcs_str}' 实际上是一个地理坐标系。")
            except CRSError as e:
                raise CRSError(f"无效的标准PCS定义 '{standard_pcs_str}': {e}") from e

    # --- check 及其他辅助方法保持不变 ---
    def check(self, filepath: str) -> Dict[str, Any]:
        """
        对给定的空间数据文件执行全面的空间参考检查。

        调用说明:
            与v2版本完全相同。初始化检查器后，调用此方法对文件进行检查。
        """
        if not os.path.exists(filepath):
            report = self._create_initial_report()
            result = CheckResult('ERROR', f"文件不存在: {filepath}")
            report["checks"]["file_readability"] = result.to_dict()
            report["overall_status"] = "错误"
            return report
        file_ext = os.path.splitext(filepath)[1].lower()
        if file_ext == '.shp':
            return self._check_vector(filepath)
        elif file_ext in ['.tif', '.tiff']:
            return self._check_raster(filepath)
        elif file_ext == '.geojson':
            return self._check_geojson(filepath)
        else:
            report = self._create_initial_report()
            result = CheckResult('ERROR', f"不支持的文件类型: {file_ext}")
            report["checks"]["file_type_check"] = result.to_dict()
            report["overall_status"] = "错误"
            return report

    def _create_initial_report(self) -> Dict[str, Any]:
        return {"overall_status": "失败", "checks": {}}

    def _get_crs_from_file(self, filepath: str) -> Tuple[Optional[CRS], Optional[CheckResult]]:
        file_ext = os.path.splitext(filepath)[1].lower()
        try:
            if file_ext == '.shp':
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    gdf = gpd.read_file(filepath, rows=0)
                return gdf.crs, None
            elif file_ext in ['.tif', '.tiff']:
                with rasterio.open(filepath) as src:
                    rasterio_crs = src.crs
                    if rasterio_crs is None: return None, None
                    pyproj_crs = CRS.from_wkt(rasterio_crs.to_wkt())
                    return pyproj_crs, None
        except Exception as e:
            return None, CheckResult('ERROR', f"读取或转换文件CRS时出错: {e}")
        return None, CheckResult('ERROR', "内部错误: 未知的CRS提取路径")

    def _run_standard_checks(self, checked_crs: Optional[CRS]) -> Dict[str, Any]:
        report = self._create_initial_report()
        crs_existence_result = self._check_crs_existence(checked_crs)
        report["checks"]["crs_existence"] = crs_existence_result.to_dict()
        if crs_existence_result.status != 'PASSED':
            return report

        gcs_result = self._check_gcs(checked_crs)
        report["checks"]["geodetic_crs_check"] = gcs_result.to_dict()

        pcs_result = self._check_pcs(checked_crs)
        report["checks"]["projected_crs_check"] = pcs_result.to_dict()

        if all(r['status'] == 'PASSED' for r in report["checks"].values()):
            report["overall_status"] = "通过"
        return report

    def _check_vector(self, filepath: str) -> Dict[str, Any]:
        checked_crs, error_result = self._get_crs_from_file(filepath)
        if error_result:
            report = self._create_initial_report()
            report['checks']['file_readability'] = error_result.to_dict()
            report['overall_status'] = '错误'
            return report
        return self._run_standard_checks(checked_crs)

    def _check_raster(self, filepath: str) -> Dict[str, Any]:
        checked_crs, error_result = self._get_crs_from_file(filepath)
        if error_result:
            report = self._create_initial_report()
            report['checks']['file_readability'] = error_result.to_dict()
            report['overall_status'] = '错误'
            return report
        return self._run_standard_checks(checked_crs)

    def _check_geojson(self, filepath: str) -> Dict[str, Any]:
        report = self._create_initial_report()
        # ... GeoJSON 解析逻辑保持不变 ...
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except (json.JSONDecodeError, UnicodeDecodeError) as e:
            result = CheckResult('ERROR', f"GeoJSON文件解析失败: {e}");
            report["checks"]["geojson_parsing"] = result.to_dict();
            report["overall_status"] = "错误";
            return report
        if data.get("type") != "FeatureCollection":
            result = CheckResult('FAILED', 'GeoJSON文件类型不是 "FeatureCollection"。',
                                 {"found_type": data.get("type")});
            report["checks"]["geojson_type_check"] = result.to_dict();
            return report
        else:
            result = CheckResult('PASSED', 'GeoJSON文件类型为 "FeatureCollection"。');
            report["checks"]["geojson_type_check"] = result.to_dict()
        crs_member = data.get("crs")
        if not crs_member:
            result = CheckResult('FAILED', 'GeoJSON文件缺少必需的 "crs" 成员。');
            report["checks"]["geojson_crs_member_existence"] = result.to_dict();
            return report
        if not (isinstance(crs_member, dict) and crs_member.get("type") == "name" and isinstance(
                crs_member.get("properties"), dict) and "name" in crs_member.get("properties", {})):
            result = CheckResult('FAILED', 'GeoJSON的 "crs" 成员结构不符合 "urn:ogc:def:crs" 规范。',
                                 {"found_crs_member": crs_member});
            report["checks"]["geojson_crs_member_structure"] = result.to_dict();
            return report
        result = CheckResult('PASSED', 'GeoJSON的 "crs" 成员存在且结构正确。');
        report["checks"]["geojson_crs_member_check"] = result.to_dict()
        try:
            urn_string = crs_member["properties"]["name"];
            checked_crs = CRS.from_string(urn_string)
        except (KeyError, CRSError) as e:
            result = CheckResult('ERROR', f'无法从GeoJSON的 "crs" 成员解析CRS: {e}', {"found_crs_member": crs_member});
            report["checks"]["crs_parsing"] = result.to_dict();
            report["overall_status"] = "错误";
            return report
        standard_checks_report = self._run_standard_checks(checked_crs)
        report["checks"].update(standard_checks_report["checks"])
        report["overall_status"] = standard_checks_report["overall_status"]
        return report

    def _check_crs_existence(self, checked_crs: Optional[CRS]) -> CheckResult:
        if checked_crs is None: return CheckResult('FAILED', '数据缺少空间参考 (CRS) 信息。')
        return CheckResult('PASSED', '数据包含空间参考信息。')

    # --- [已修改] 检查 GCS 的逻辑 ---
    def _check_gcs(self, checked_crs: CRS) -> CheckResult:
        """检查数据的地理坐标系是否与标准 GCS 一致。"""
        checked_gcs = checked_crs.geodetic_crs
        if checked_gcs is None:
            return CheckResult('FAILED', '数据文件中未能识别出地理坐标系(GCS)。')

        if self.standard_gcs.equals(checked_gcs):
            return CheckResult('PASSED', '地理坐标系 (GCS) 与标准一致。', {"gcs_name": self.standard_gcs.name})
        else:
            return CheckResult('FAILED', '地理坐标系 (GCS) 与标准不一致。', {
                "标准GCS": self.standard_gcs.name,
                "数据GCS": checked_gcs.name
            })

    # --- [已修改] 检查 PCS 的逻辑 ---
    def _check_pcs(self, checked_crs: CRS) -> CheckResult:
        """检查数据的投影坐标系是否与标准 PCS 一致。"""
        # 场景1: 项目标准不要求投影
        if self.standard_pcs is None:
            if not checked_crs.is_projected:
                return CheckResult('PASSED', '数据为地理坐标系，符合无投影标准的要求。')
            else:
                return CheckResult('FAILED', '标准不要求投影，但数据是投影坐标系。', {
                    "数据PCS": checked_crs.name
                })

        # 场景2: 项目标准要求投影
        else:
            if not checked_crs.is_projected:
                return CheckResult('FAILED', '标准要求投影，但数据是地理坐标系。')

            # 直接比较整个投影坐标系
            if self.standard_pcs.equals(checked_crs):
                return CheckResult('PASSED', '投影坐标系 (PCS) 与标准完全一致。', {"pcs_name": self.standard_pcs.name})
            else:
                return CheckResult('FAILED', '投影坐标系 (PCS) 与标准不一致。', {
                    "标准PCS": self.standard_pcs.name,
                    "数据PCS": checked_crs.name
                })

    def check_db(self, db_conn_str, db_schema, table_name, geom_column='geometry'):
        """
        检查数据库空间表的空间参考（仅查一行，避免大表全读）。
        Args:
            db_conn_str (str): 数据库连接字符串
            db_schema (str): 数据库schema
            table_name (str): 表名
            geom_column (str): 空间字段名，默认 'geometry'
        Returns:
            dict: 检查报告，与文件检查一致
        """
        import sqlalchemy
        try:
            engine = sqlalchemy.create_engine(db_conn_str)
            sql = f'SELECT * FROM "{db_schema}"."{table_name}" LIMIT 1'
            gdf = gpd.read_postgis(sql, engine, geom_col=geom_column)
            if gdf.empty:
                report = self._create_initial_report()
                report['checks']['db_readability'] = CheckResult('FAILED', '数据库表为空，无法获取空间参考信息。').to_dict()
                report['overall_status'] = '失败'
                return report
            checked_crs = gdf.crs
        except Exception as e:
            report = self._create_initial_report()
            report['checks']['db_readability'] = CheckResult('ERROR', f'数据库读取失败: {e}').to_dict()
            report['overall_status'] = '错误'
            return report
        return self._run_standard_checks(checked_crs)


# --- 测试与演示部分 (已更新以适应新的初始化方式) ---

def _create_test_dir():
    TEST_DIR = "test_data_temp_v3_0"
    if os.path.exists(TEST_DIR): shutil.rmtree(TEST_DIR)
    os.makedirs(TEST_DIR)
    return TEST_DIR


def _create_test_shp(filepath: str, crs_str: Optional[str]):
    gdf = gpd.GeoDataFrame({'id': [1], 'geometry': [gpd.points_from_xy([1], [1])[0]]})
    if crs_str: gdf.crs = CRS.from_string(crs_str)
    gdf.to_file(filepath, driver='ESRI Shapefile', encoding='utf-8')


def _create_test_tif(filepath: str, crs_str: Optional[str]):
    profile = {
        'driver': 'GTiff', 'height': 1, 'width': 1, 'count': 1, 'dtype': 'uint8',
        'crs': CRS.from_string(crs_str) if crs_str else None,
        'transform': rasterio.transform.from_origin(110, 40, 1, 1)
    }
    data_array = np.array([[1]], dtype=np.uint8)
    with rasterio.open(filepath, 'w', **profile) as dst:
        dst.write(data_array, 1)


def run_demonstration_and_tests():
    print("=" * 40)
    print("  空间参考检查器 v3.0 - 使用演示与自动化测试")
    print("=" * 40)

    # --- 准备环境 ---
    print("\n[准备] 创建临时测试目录和文件...")
    TEST_DIR = _create_test_dir()

    # 定义标准和各种测试数据
    STANDARD_GCS_STR = "EPSG:4490"  # CGCS2000
    STANDARD_PCS_STR = "EPSG:4547"  # CGCS2000 / 3-degree GK CM 111E

    # 1. 完全符合标准的数据 (GCS正确, PCS正确)
    perfect_shp = os.path.join(TEST_DIR, "perfect.shp")
    _create_test_shp(perfect_shp, STANDARD_PCS_STR)

    # 2. GCS正确，但无投影的数据
    gcs_only_shp = os.path.join(TEST_DIR, "gcs_only.shp")
    _create_test_shp(gcs_only_shp, STANDARD_GCS_STR)

    # 3. GCS错误，PCS也错误的数据 (WGS 84 / UTM zone 50N)
    wrong_gcs_pcs_tif = os.path.join(TEST_DIR, "wrong_gcs_pcs.tif")
    _create_test_tif(wrong_gcs_pcs_tif, "EPSG:32650")

    # 4. GCS正确，但PCS错误的数据 (CGCS2000 / 3-degree GK CM 114E)
    wrong_pcs_shp = os.path.join(TEST_DIR, "wrong_pcs.shp")
    _create_test_shp(wrong_pcs_shp, "EPSG:4548")

    print("准备完成。")

    # --- 演示1: 严格检查 (要求 GCS 和 PCS 都匹配) ---
    print("\n" + "#" * 20 + " 演示1: 严格检查 (要求GCS和PCS) " + "#" * 20)
    try:
        strict_checker = SpatialReferenceChecker(
            standard_gcs_str=STANDARD_GCS_STR,
            standard_pcs_str=STANDARD_PCS_STR
        )
        print(f"严格检查器初始化成功。标准GCS: {STANDARD_GCS_STR}, 标准PCS: {STANDARD_PCS_STR}")
    except CRSError as e:
        print(f"初始化失败: {e}");
        return

    print("\n--- 案例 1.1: 检查完全符合标准的数据 ---")
    report = strict_checker.check(perfect_shp)
    print(json.dumps(report, indent=2, ensure_ascii=False))
    assert report['overall_status'] == '通过'
    print("结论: ✔ 检查通过，符合预期。")

    print("\n--- 案例 1.2: 检查只有GCS正确的数据 (应失败) ---")
    report = strict_checker.check(gcs_only_shp)
    print(json.dumps(report, indent=2, ensure_ascii=False))
    assert report['overall_status'] == '失败'
    assert report['checks']['projected_crs_check']['status'] == 'FAILED'
    print("结论: ✔ 检查失败，因缺少投影，符合预期。")

    print("\n--- 案例 1.3: 检查GCS和PCS都错误的数据 (应失败) ---")
    report = strict_checker.check(wrong_gcs_pcs_tif)
    print(json.dumps(report, indent=2, ensure_ascii=False))
    assert report['overall_status'] == '失败'
    assert report['checks']['geodetic_crs_check']['status'] == 'FAILED'
    assert report['checks']['projected_crs_check']['status'] == 'FAILED'
    print("结论: ✔ 检查失败，因GCS和PCS都不匹配，符合预期。")

    # --- 演示2: 宽松检查 (只要求 GCS 匹配) ---
    print("\n" + "#" * 20 + " 演示2: 宽松检查 (只要求GCS) " + "#" * 20)
    try:
        loose_checker = SpatialReferenceChecker(standard_gcs_str=STANDARD_GCS_STR)
        print(f"宽松检查器初始化成功。标准GCS: {STANDARD_GCS_STR}, 无投影要求。")
    except CRSError as e:
        print(f"初始化失败: {e}");
        return

    print("\n--- 案例 2.1: 检查只有GCS正确的数据 (应通过) ---")
    report = loose_checker.check(gcs_only_shp)
    print(json.dumps(report, indent=2, ensure_ascii=False))
    assert report['overall_status'] == '通过'
    print("结论: ✔ 检查通过，符合预期。")

    print("\n--- 案例 2.2: 检查GCS正确但PCS错误的数据 (应失败) ---")
    report = loose_checker.check(wrong_pcs_shp)
    print(json.dumps(report, indent=2, ensure_ascii=False))
    assert report['overall_status'] == '失败'
    assert report['checks']['projected_crs_check']['status'] == 'FAILED'
    print("结论: ✔ 检查失败，因数据包含不被要求的投影，符合预期。")

    # --- 清理 ---
    print("\n[清理] 删除临时测试文件和目录...")
    shutil.rmtree(TEST_DIR)
    print("清理完成。")
    print("\n" + "=" * 40)
    print("🎉 演示与自动化测试全部通过！")
    print("=" * 40)


if __name__ == '__main__':
    run_demonstration_and_tests()
