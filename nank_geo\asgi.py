"""
ASGI config for nank_geo project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/howto/deployment/asgi/
"""

import os
from dotenv import load_dotenv
load_dotenv()  # 这会把 .env 里的变量注入 os.environ
os.environ['PROJ_LIB'] = os.environ.get('PROJ_LIB', '')

from django.core.asgi import get_asgi_application

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nank_geo.settings')

application = get_asgi_application()
