# 文本处理工具脚本
# 功能：从多种文件格式中提取文本，进行清洗、关键词提取和摘要生成

# 导入必要的库
import os  # 操作系统接口
import subprocess
import sys
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
import re  # 正则表达式
import easyocr  # 光学字符识别(OCR)
import jieba  # 中文分词
import jieba.analyse  # 关键词提取
from snownlp import SnowNLP  # 中文文本处理
from collections import Counter  # 计数器
from docx import Document  # Word文档处理
from pptx import Presentation  # PPT处理
import pandas as pd  # 数据处理
from pdfminer.high_level import extract_text  # PDF文本提取
from pathlib import Path # 路径处理

# 尝试导入gensim的摘要功能（可选）
try:
    from gensim.summarization import summarize
    gensim_available = True  # 标记gensim是否可用
except ImportError:
    gensim_available = False

try:
    import docx2txt
except ImportError:
    docx2txt = None
try:
    import textract
except ImportError:
    textract = None

class LibreOfficeConverter:
    def __init__(self, libreoffice_path: str = None):
        """
        :param libreoffice_path: LibreOffice 安装路径（默认自动检测）
        """
        self.libreoffice_path = self._detect_libreoffice_path(libreoffice_path)
    def get_soffice_paths(self):
        platform = sys.platform
        if platform.startswith('win'):
            key = 'SOFFICE_PATHS_WIN32'
        elif platform == 'darwin':
            key = 'SOFFICE_PATHS_DARWIN'
        elif platform.startswith('linux'):
            key = 'SOFFICE_PATHS_LINUX'
        else:
            return []
        value = os.environ.get(key, '')
        return [p.strip() for p in value.split(';') if p.strip()]
    def _detect_libreoffice_path(self, custom_path: str) -> str:
        """自动检测 LibreOffice 可执行文件路径"""
        if custom_path:
            return custom_path

        # 常见系统默认路径
        paths = self.get_soffice_paths()
        for path in paths:
            if os.path.exists(path):
                return path
        raise FileNotFoundError("LibreOffice 未找到，请手动指定安装路径")

    def convert_doc_to_docx(self, doc_path):
        docx_path = os.path.splitext(doc_path)[0] + ".docx"
        if os.path.exists(docx_path):
            return docx_path
        cmd = [
            str(self.libreoffice_path),
            '--headless',
            '--convert-to', 'docx',
            '--outdir', str(os.path.dirname(doc_path)),
            str(doc_path)
        ]
        try:
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=30)
            print("STDOUT:", result.stdout.decode())
            print("STDERR:", result.stderr.decode())
            if os.path.exists(docx_path):
                return docx_path
            else:
                print("LibreOffice转换失败:", result.stderr.decode())
                return None
        except Exception as e:
            print("调用LibreOffice失败:", e)
            return None

    def convert(self, input_path: str, output_dir: str, output_format: str = "docx") -> Path:
        """
        使用 LibreOffice 将文档转换为指定格式。
        :param input_path: 输入文档路径
        :param output_dir: 输出目录
        :param output_format: 输出格式 (e.g., "docx", "pdf", "txt")
        :return: 转换后的文件路径
        """
        output_path = Path(output_dir) / f"{Path(input_path).stem}.{output_format}"
        if output_path.exists():
            # 如果文件已存在，可以选择直接返回或先删除再转换
            print(f"文件 {output_path} 已存在，直接返回。")
            return output_path

        # 1. 修正 cmd 列表，删除错误的参数
        cmd = [
            self.libreoffice_path,
            "--headless",
            "--convert-to", output_format,
            "--outdir", output_dir,
            input_path  # 将 input_path 放在最后
        ]

        try:
            # 2. 修改 subprocess.run 调用方式
            #    - 使用 capture_output=True 替代 stdout=PIPE, stderr=PIPE
            #    - 使用 text=True 让 Python 自动使用系统默认编码解码输出
            #    - 或者明确指定 encoding='gbk' 或 locale.getpreferredencoding(False)
            result = subprocess.run(
                cmd,
                capture_output=True,  # 更现代的写法，等同于 stdout=PIPE, stderr=PIPE
                text=True,  # 自动解码 stdout 和 stderr，使用系统默认编码
                # encoding=locale.getpreferredencoding(False), # 或者更明确地指定编码
                timeout=60,
                check=False  # 我们手动检查返回码，所以设为 False
            )

            # 因为设置了 text=True，result.stdout 和 result.stderr 已经是字符串，无需 .decode()
            print(f"LibreOffice 转换命令: {' '.join(cmd)}")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)

            # LibreOffice 成功时有时也会在 stderr 输出一些信息，所以主要判断返回码和文件是否存在
            if result.returncode == 0 and output_path.exists():
                print(f"文件成功转换为: {output_path}")
                return output_path
            else:
                # 即使返回码不是0，如果文件生成了，有时也可以认为成功
                if output_path.exists():
                    print(f"警告: LibreOffice 返回码为 {result.returncode}，但文件已生成。")
                    return output_path

                raise Exception(
                    f"LibreOffice 转换失败。返回码: {result.returncode}\n"
                    f"STDOUT: {result.stdout}\n"
                    f"STDERR: {result.stderr}"
                )
        except subprocess.TimeoutExpired:
            raise Exception(f"LibreOffice 转换超时。命令: {' '.join(cmd)}")
        except Exception as e:
            # 现在的异常会是更底层的错误，而不是解码错误
            raise Exception(f"调用 LibreOffice 失败: {e}")

# 第一步：文件文本提取函数
def extract_text_from_file(file_path):
    """
    从多种文件格式中提取文本内容
    参数:
        file_path: 文件路径
    返回:
        提取的文本内容
    """
    # 获取文件扩展名
    ext = file_path.lower().split('.')[-1]

    # 根据文件类型选择提取方法
    if ext == 'txt':  # 纯文本文件
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    elif ext == 'csv':  # CSV文件
        df = pd.read_csv(file_path, encoding='utf-8')
        return '\n'.join(df.astype(str).apply(' '.join, axis=1))
    elif ext in ['xls', 'xlsx']:  # Excel文件
        df = pd.read_excel(file_path)
        return '\n'.join(df.astype(str).apply(' '.join, axis=1))
    elif ext == 'docx':  # Word文档
        doc = Document(file_path)
        return '\n'.join([para.text for para in doc.paragraphs])
    elif ext == 'pptx':  # PPT文件
        prs = Presentation(file_path)
        text = []
        for slide in prs.slides:
            for shape in slide.shapes:
                if hasattr(shape, "text"):
                    text.append(shape.text)
        return '\n'.join(text)
    elif ext == 'pdf':  # PDF文件
        return extract_text(file_path)
    elif ext == 'png':  # 图片文件
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        result = reader.readtext(file_path, detail=0)
        return '\n'.join(result)
    else:  # 不支持的文件格式
        return ""

def convert_doc_to_docx(input_path, libreoffice_path=None):
    converter = LibreOfficeConverter(libreoffice_path)
    input_path = Path(input_path).resolve()
    output_dir = input_path.parent
    # 只需改输出格式为 docx
    try:
        input_path = str(input_path).encode('utf-8').decode('utf-8', errors='ignore')
        output_dir = str(output_dir).encode('utf-8').decode('utf-8', errors='ignore')
        docx_path = converter.convert(input_path, output_dir, output_format="docx")
        return str(docx_path)
    except Exception as e:
        print(f"doc转docx失败: {e}")
        return None

def extract_text_from_doc(file_path, libreoffice_path=None):
    docx_path = convert_doc_to_docx(file_path, libreoffice_path)
    if docx_path and os.path.exists(docx_path):
        from docx import Document
        try:
            doc = Document(docx_path)
            return '\n'.join([p.text for p in doc.paragraphs])
        except Exception:
            return ""
    return ""

def extract_text_from_docx(file_path):
    """
    提取docx文件文本内容。
    """
    if docx2txt:
        try:
            return docx2txt.process(file_path)
        except Exception:
            pass
    try:
        doc = Document(file_path)
        return '\n'.join([p.text for p in doc.paragraphs])
    except Exception:
        return ""

# 第二步：文本清洗函数
def clean_text(text):
    """
    清洗和规范化文本
    参数:
        text: 原始文本
    返回:
        清洗后的文本
    """
    # 合并多个空白字符为单个空格
    text = re.sub(r'\s+', ' ', text)
    # 移除非文字字符，保留中英文和数字
    text = re.sub(r'[^\w\s\u4e00-\u9fa5]', '', text)
    return text.strip()  # 去除首尾空格

# 第三步：关键词提取函数
def extract_keywords(text, topk=20):
    """
    使用TF-IDF和TextRank算法提取关键词
    参数:
        text: 输入文本
        topk: 返回关键词数量
    返回:
        关键词列表
    """
    # TF-IDF算法提取关键词
    tfidf_keywords = jieba.analyse.extract_tags(text, topK=topk)
    # TextRank算法提取关键词
    textrank_keywords = jieba.analyse.textrank(text, topK=topk)
    # 合并并去重
    keywords = list(set(tfidf_keywords + textrank_keywords))
    # 过滤单字关键词
    return [kw for kw in keywords if len(kw) > 1]

# 第四步：文本摘要生成函数
def extract_summary(text):
    if not text or not text.strip():
        return ""
    try:
        s = SnowNLP(text)
        return s.summary(3)
    except Exception:
        return ""

# 第五步：综合处理函数
def process_file(file_path):
    """
    处理文件，支持txt, doc, docx, pdf等格式。
    """
    ext = os.path.splitext(file_path)[-1].lower()
    text = ""
    if ext == '.txt':
        with open(file_path, encoding='utf-8', errors='ignore') as f:
            text = f.read()
    elif ext == '.docx':
        text = extract_text_from_docx(file_path)
    elif ext == '.doc':
        text = extract_text_from_doc(file_path)
    elif ext == 'pptx':  # PPT文件
        prs = Presentation(file_path)
        text = []
        for slide in prs.slides:
            for shape in slide.shapes:
                if hasattr(shape, "text"):
                    text.append(shape.text)
        text = '\n'.join(text)
    elif ext == 'pdf':  # PDF文件
        text = extract_text(file_path)
    elif ext == 'png':  # 图片文件
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        result = reader.readtext(file_path, detail=0)
        text = '\n'.join(result)
    else:  # 不支持的文件格式
        text = extract_text_from_file(file_path) # 回退到通用提取

    cleaned_text = text.strip() if text else ""
    if not cleaned_text:
        return {
            'text': '',
            'keywords': [],
            'summary': ''
        }

    # 清洗文本
    cleaned_text = clean_text(cleaned_text)
    # 提取关键词
    keywords = extract_keywords(cleaned_text)
    # 生成摘要
    summary = extract_summary(cleaned_text)

    return {
        "text": text,  # 原始文本
        "keywords": keywords,  # 关键词列表
        "summary": summary  # 文本摘要
    }

# 第六步：主程序入口
if __name__ == '__main__':
    # 示例文件路径（实际使用时需要替换）
    file_path = r"H:\code\nank-geo-dev\nank_geo-v2.0\data\file-test\鲁迅简介.doc"

    # 处理文件
    result = process_file(file_path)

    # 输出结果
    print("【文本内容前500字】：\n", result['text'][:500])
    print("\n【关键词】：\n", result['keywords'])
    print("\n【摘要】：\n", result['summary'])
