import os
import platform
import stat
import subprocess

from django.core.files.base import ContentFile
from django.http import HttpResponse

from glff.s3torage.S3Boto3Storage import CustomS3Storage


def demo_res(request):
    # 一、测试上传本地文件到S3中
    local_file_path = r'G:\testcode\ceshitiff\yiwudem.tif'

    try:
        # 创建 OVR 文件
        ovr_file_path = local_file_path.replace('.tif', '.ovr')  # OVR 文件路径

        # 使用 gdaladdo 创建 内部 OVR 文件
        subprocess.run(['gdaladdo', '-r', 'average', local_file_path, '2', '4', '8', '16', '32'], check=True)
        print(f"OVR file created for: {local_file_path}")

        # 上传 TIFF 文件到 S3
        with open(local_file_path, 'rb') as local_file:
            file_content = local_file.read()
            file_name = os.path.basename(local_file_path)

            # 使用 CustomS3Storage 进行文件上传
            path = f'uploads/{file_name}'  # S3 中的路径
            storage = CustomS3Storage(bucket_name='system44')
            storage.save(path, ContentFile(file_content))

        return HttpResponse("Files uploaded successfully to S3!")

    except Exception as e:
        return HttpResponse(f"An error occurred: {str(e)}")

def create_directory_and_set_permissions(directory_path):
    # 创建目录
    try:
        os.makedirs(directory_path, exist_ok=True)
        print(f"Directory '{directory_path}' created successfully.")
    except Exception as e:
        print(f"Error creating directory: {e}")
        return

    # 设置权限
    try:
        if platform.system() == "Windows":
            # 使用 icacls 命令设置权限
            command = f'icacls "{directory_path}" /grant Everyone:(OI)(CI)F'
            subprocess.run(command, shell=True, check=True)
            print(f"Permissions for '{directory_path}' set to allow Everyone read/write.")
        else:
            # Linux 系统，使用 os.chmod 设置权限
            os.chmod(directory_path, stat.S_IRWXU | stat.S_IRWXG | stat.S_IRWXO)  # rwxrwxrwx
            print(f"Permissions for '{directory_path}' set to allow Everyone read/write.")
    except Exception as e:
        print(f"Error setting permissions: {e}")

