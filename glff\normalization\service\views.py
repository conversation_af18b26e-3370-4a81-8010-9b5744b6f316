import logging
import traceback

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
import json
import os

from pyproj import Transformer
from shapely.geometry import mapping
import glob

# --- data_pre_processing ---
from glff.normalization.data_pre_processing.file_processing import file_extract, file_preprocess
from glff.normalization.data_pre_processing.reproject import Reprojector, transform_single_coord, convert_geojson_data, \
    convert_geojson_file, convert_geojson_text, convert_geojson_shp
from glff.normalization.data_pre_processing.geo_info import shp_info_extract, tif_info_extract
from glff.normalization.data_pre_processing.image_processing import preprocess_image
from glff.normalization.data_pre_processing.mosaic import geo_mosaic_tool

# --- quality_inspection ---
from glff.normalization.quality_inspection.attribute_checker import attribute_checker
from glff.normalization.quality_inspection.logical_consistency_checker import logical_consistency_checker
from glff.normalization.quality_inspection.topology_check import vector_quality
from glff.normalization.quality_inspection.geo_integrity_checker import geo_integrity_checker
from glff.normalization.quality_inspection.spatial_reference_check import spatial_reference_check

# --- 工具函数 ---
def get_json(request):
    """
    从Django request对象中解析JSON数据。
    :param request: Django HttpRequest对象
    :return: dict，解析后的JSON数据，解析失败返回空字典
    """
    try:
        return json.loads(request.body)
    except Exception:
        return {}

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from rest_framework.decorators import permission_classes

from django.shortcuts import render

def index_view(request):
    return render(request, 'index.html')

def preprocess_view(request):
    return render(request, 'preprocess.html')

def quality_view(request):
    return render(request, 'quality.html')

# ===================== file_processing =====================
@permission_classes([AllowAny])
class FileExtractTextView(APIView):
    """
    文本提取API：从多种文件格式中提取文本、关键词、摘要。
    POST参数：
        file_path (str): 文件路径
    返回：
        status: 'success' or 'error'
        result: 提取结果（dict）
        message: 错误信息（如有）
    """
    def post(self, request):
        data = request.data
        file_path = data.get('file_path')
        try:
            result = file_extract.process_file(file_path)
            return Response({'status': 'success', 'result': result})
        except Exception as e:
            return Response({'status': 'error', 'message': str(e)}, status=500)

@permission_classes([AllowAny])
class FilePreprocessView(APIView):
    """
    文档格式转换API：将文档统一转换为PDF。
    POST参数：
        file_path (str): 输入文件路径
        output_path (str): 输出PDF路径
    返回：
        status: 'success' or 'error'
        result: 输出文件路径
        message: 错误信息（如有）
    """
    def post(self, request):
        data = request.data
        file_path = data.get('file_path')
        output_path = data.get('output_path')
        try:
            converter = file_preprocess.LibreOfficeConverter()
            result = str(converter.convert_to_pdf(file_path, output_path))
            return Response({'status': 'success', 'result': result})
        except Exception as e:
            return Response({'status': 'error', 'message': str(e)}, status=500)

# ===================== reproject =====================
@permission_classes([AllowAny])
class ReprojectTransformView(APIView):
    """
    单文件重投影API。
    POST参数：
        input_path (str): 输入文件路径
        output_path (str): 输出文件路径
        src_crs (str): 源坐标系（可选，默认EPSG:32648）
        dst_crs (str): 目标坐标系（可选，默认EPSG:4326）
    返回：
        status: 'success' or 'error'
        result: True/False 或错误信息
    """
    def post(self, request):
        data = request.data
        input_path = data.get('input_path')
        output_path = data.get('output_path')
        src_crs = data.get('src_crs', 'EPSG:32648')
        dst_crs = data.get('dst_crs', 'EPSG:4326')
        try:
            reprojector = Reprojector()
            result = reprojector.transform_coordinates(input_path, output_path, src_crs=src_crs, dst_crs=dst_crs)
            return Response({'status': 'success', 'result': json.dumps(result,ensure_ascii=False)})
        except Exception as e:
            return Response({'status': 'error', 'message': str(e)}, status=500)

@permission_classes([AllowAny])
class ReprojectBatchTransformView(APIView):
    """
    批量重投影API。
    POST参数：
        input_files (list): 输入文件路径列表
        output_dir (str): 输出目录
        dst_crs (str): 目标坐标系（可选，默认EPSG:4326）
    返回：
        status: 'success' or 'error'
        result: 批量处理结果（dict）
    """
    def post(self, request):
        data = request.data
        input_files = data.get('input_files', [])
        output_dir = data.get('output_dir')
        dst_crs = data.get('dst_crs', 'EPSG:4326')
        try:
            reprojector = Reprojector()
            result = reprojector.batch_transform_coordinates(input_files, output_dir, dst_crs=dst_crs)
            return Response({'status': 'success', 'result': result})
        except Exception as e:
            return Response({'status': 'error', 'message': str(e)}, status=500)

@permission_classes([AllowAny])
class ReprojectSingleCoordView(APIView):
    """
    单点坐标转换API。
    POST参数：
        coord (list): [x, y] 或 [x, y, z]
        src_crs (str): 源坐标系（可选，默认EPSG:32648）
        dst_crs (str): 目标坐标系（可选，默认EPSG:4326）
    返回：
        status: 'success' or 'error'
        result: 转换后坐标
    """
    def post(self, request):
        data = request.data
        coord = data.get('coord')
        src_crs = data.get('src_crs')
        dst_crs = data.get('dst_crs')
        try:
            transformer = Transformer.from_crs(src_crs, dst_crs, always_xy=True)
            result = transform_single_coord(coord, transformer)
            return Response({'status': 'success', 'result': result})
        except Exception as e:
            return Response({'status': 'error', 'message': str(e)}, status=500)

@permission_classes([AllowAny])
class ReprojectConvertGeojsonView(APIView):
    """
    GeoJSON坐标转换API。
    POST参数：
        geojson (dict): GeoJSON数据  text = '{"type":"FeatureCollection","features":[{"type":"Feature","geometry":{"type":"Point","coordinates":[566353.96,2899559.66]}}]}'
        src_crs (str): 源坐标系（可选，默认EPSG:32648）
        dst_crs (str): 目标坐标系（可选，默认EPSG:4326）
    返回：
        status: 'success' or 'error'
        result: 转换后GeoJSON
    """
    def post(self, request):
        data = request.data
        geojson = data.get('geojson')
        src_crs = data.get('src_crs')
        dst_crs = data.get('dst_crs')
        try:
            result = convert_geojson_data(geojson, src_crs, dst_crs)
            return Response({'status': 'success', 'result': result})
        except Exception as e:
            return Response({'status': 'error', 'message': str(e)}, status=500)

@permission_classes([AllowAny])
class ReprojectConvertGeojsonShpView(APIView):
    """
    Shapefile坐标转换API。
    POST参数：
        input_path (str): 输入Shapefile路径
        output_path (str): 输出Shapefile路径
        src_crs (str): 源坐标系（可选，默认EPSG:32648）
        dst_crs (str): 目标坐标系（可选，默认EPSG:4326）
    返回：
        status: 'success' or 'error'
        result: None（成功）或错误信息
    """
    def post(self, request):
        data = request.data
        input_path = data.get('input_path')
        output_path = data.get('output_path')
        src_crs = data.get('src_crs')
        dst_crs = data.get('dst_crs')
        try:
            result = convert_geojson_shp(input_path, output_path, src_crs, dst_crs)
            return Response({'status': 'success', 'result': result})
        except Exception as e:
            return Response({'status': 'error', 'message': str(e)}, status=500)

# ===================== geo_info =====================
@permission_classes([AllowAny])
class GeoInfoShpView(APIView):
    """
    Shapefile空间信息提取API。
    POST参数：
        shp_path (str): Shapefile文件路径
    返回：
        status: 'success' or 'error'
        result: 提取结果（dict）
    """
    def post(self, request):
        data = request.data
        shp_path = data.get('shp_path')
        return_all_features = data.get('return_all_features', False)
        if isinstance(return_all_features, str):
            return_all_features = return_all_features.lower() in ['true', '1', 'yes']
        
        try:
            result = shp_info_extract.extract_shp_info(shp_path, return_all_features=return_all_features)
            return Response({'status': 'success', 'result': result})
        except Exception as e:
            return Response({'status': 'error', 'message': str(e)}, status=500)

@permission_classes([AllowAny])
class GeoInfoTifView(APIView):
    """
    TIF空间信息提取API。
    POST参数：
        tif_path (str): TIF文件路径
    返回：
        status: 'success' or 'error'
        result: 提取结果（dict）
    """
    def post(self, request):
        data = request.data
        tif_path = data.get('tif_path')
        try:
            directory_path = os.path.dirname(tif_path)
            output_bounds_geojson_path = os.path.join(directory_path,'output_bounds.geojson')
            output_actual_geojson_path = os.path.join(directory_path,'output_actual_polygon.geojson')
            result = tif_info_extract.read_tif_info(tif_path,output_bounds_geojson_path,output_actual_geojson_path,2048,0.001)

            if result["error"]:
                resultJson = {
                    "info":"error"
                }
            else:
                metadata = result['metadata'];
                metadata['crs'] = metadata['crs'].to_wkt()
                affine_params_as_strings = map(str, metadata['transform'])
                metadata['transform'] = ', '.join(affine_params_as_strings)
                resultJson = {
                    "info": "sucess",
                    "metadata": metadata,
                    "rpc_info": result['rpc_info'],
                    "bounds": result['bounds'],
                    "crs": result['crs'],
                    "actual_polygons": {
                        "type": "Feature",
                        "geometry": mapping(result['actual_polygons']),
                        "properties": {"description": "tif bound"}
                    }
                }
            return Response({'status': 'success', 'result': resultJson})
        except Exception as e:
            return Response({'status': 'error', 'message': str(e)}, status=500)

# ===================== image_processing =====================
@permission_classes([AllowAny])
class ImageProcessView(APIView):
    """
    影像预处理API（最复杂模式，支持参数覆盖）。
    POST参数：
        input_path (str): 输入影像路径
        denoise (bool): 是否去噪，默认True
        resample_resolution (tuple): 重采样分辨率，默认(0.1, 0.1)
        build_pyramids (bool): 是否构建金字塔，默认True
        create_thumbnail (bool): 是否生成缩略图，默认True
    返回：
        status: 'success' or 'error'
        result: 处理结果（dict）
    """
    def post(self, request):
        data = request.data
        input_path = data.get('input_path')
        denoise = data.get('denoise', False)
        resample_resolution = None
        if data.get('resample_resolution') is not None:
            resample_resolution = tuple((data.get('resample_resolution')[0],data.get('resample_resolution')[1]))
        build_pyramids = data.get('build_pyramids', True)
        create_thumbnail = data.get('create_thumbnail', True)
        try:
            processor = preprocess_image.GeospatialProcessor(
                denoise=denoise,
                resample_resolution=resample_resolution,
                build_pyramids=build_pyramids
            )
            result = processor.process_file(input_path, create_thumbnail=create_thumbnail)
            return Response({'status': 'success', 'result': result})
        except Exception as e:
            return Response({'status': 'error', 'message': str(e)}, status=500)

# ===================== mosaic =====================
@permission_classes([AllowAny])
class MosaicView(APIView):
    """
    影像接边拼接API。
    POST参数：
        input_paths (list): 输入影像路径列表
        output_path (str): 输出影像路径
        options (dict): GDAL参数（可选），支持：
            - target_crs (str): 目标坐标系，默认None（自动推断）
            - blend_distance (float): 融合带宽，默认0
            - nodata_value (float or 'auto'): nodata值，默认'auto'
            - num_threads (str): 线程数，默认'ALL_CPUS'
            - build_overviews (bool): 是否构建金字塔，默认True
            - overview_resampling (str): 金字塔重采样方法，默认'AVERAGE'
            - overview_levels (list): 金字塔层级，默认[2,4,8,16,32]
            - resample_alg (str): 重采样算法，默认'cubic'
            - output_format (str): 输出格式，默认'GTiff'
            - creationOptions (list): GDAL创建选项，默认['COMPRESS=LZW', 'TILED=YES', 'BIGTIFF=YES']
            - warp_memory_mb (int): GDAL内存限制，默认None
    返回：
        status: 'success' or 'error'
        result: 输出文件路径
    """
    def post(self, request):
        data = request.data
        input_paths = data.get('input_paths')
        output_path = data.get('output_path')
        options = data.get('options', {
            "target_crs": "EPSG:3857", "blend_distance": 25, "nodata_value": "auto",
            "num_threads": "ALL_CPUS", "build_overviews": True, "show_progress": True
        })
        try:

            input_files = glob.glob(os.path.join(input_paths, '*.tif'))
            processor = geo_mosaic_tool.MosaicProcessor(input_files, output_path, **options)
            result = processor.process()
            return Response({'status': 'success', 'result': result})
        except Exception as e:
            return Response({'status': 'error', 'message': str(e)}, status=500)

# ===================== quality_inspection =====================
@permission_classes([AllowAny])
class AttributeCheckView(APIView):
    """
    属性结构与几何检查API。
    POST参数：
        shp_path (str): Shapefile路径
        schema (dict): 字段检查规则
        id_field (str): 唯一标识字段名（可选，默认'id'）
    返回：
        status: 'success' or 'error'
        result: 检查报告（dict）
    """
    def post(self, request):
        data = request.data
        shp_path = data.get('shp_path')
        schema = data.get('schema')
        id_field = data.get('id_field', 'id')
        try:
            checker = attribute_checker.AttributeChecker.from_shapefile(shp_path, schema, id_field)
            result = checker.run()
            return Response({'status': 'success', 'result': result})
        except Exception as e:
            return Response({'status': 'error', 'message': str(e)}, status=500)

@permission_classes([AllowAny])
class AttributeCheckDbView(APIView):
    """
    数据库属性结构与几何检查API。
    POST参数：
        db_conn_str (str): 数据库连接字符串
        schema (dict): 字段检查规则
        table_name (str): 表名
        db_schema (str): 数据库schema名（可选）
        id_field (str): 唯一标识字段名（可选）
    返回：
        status: 'success' or 'error'
        result: 检查报告（dict）
    """
    def post(self, request):
        data = request.data
        db_conn_str = data.get('db_conn_str')
        schema = data.get('schema')
        table_name = data.get('table_name')
        db_schema = data.get('db_schema')
        id_field = data.get('id_field')
        try:
            checker = attribute_checker.AttributeChecker.from_database(db_conn_str, schema, table_name, db_schema, id_field)
            result = checker.run()
            return Response({'status': 'success', 'result': json.dumps(result,ensure_ascii=False)})
        except Exception as e:
            return Response({'status': 'error', 'message': str(e)}, status=500)

@permission_classes([AllowAny])
class LogicalConsistencyView(APIView):
    """
    逻辑一致性检查API（数据库规则）。
    POST参数：
        suite_rules (list): 规则集（列表）
        db_conn_str (str): 数据库连接字符串
        context (dict): 缓存上下文（可选）
    返回：
        status: 'success' or 'error'
        result: 检查报告（dict）
    """
    def post(self, request):
        data = request.data
        suite_rules = data.get('suite_rules', [])
        db_conn_str = data.get('db_conn_str')
        context = data.get('context', None)
        try:
            checker = logical_consistency_checker.LogicalConsistencyChecker(db_conn_str)
            result = checker.run_checks(suite_rules, db_conn_str, context)
            return Response({'status': 'success', 'result': result})
        except Exception as e:
            print(e)
            traceback.print_exc()  # 输出完整堆栈跟踪
            print("ERROR:", e, flush=True)  # 强制立即输出
            logging.exception("发生异常:")  # 自动附带堆栈跟踪
            return Response({'status': 'error', 'message': str(e)}, status=500)

@permission_classes([AllowAny])
class TopologyCheckView(APIView):
    """
    拓扑关系检查API。
    POST参数：
        file_path (str): 矢量文件路径（shp/geojson）
        id_field (str): 唯一标识字段名（可选，默认'id'）
    返回：
        status: 'success' or 'error'
        result: 检查报告（dict）
    """
    def post(self, request):
        data = request.data
        file_path = data.get('file_path')
        id_field = data.get('id_field', 'id')
        try:
            checker = vector_quality.TopologyChecker.from_file(file_path, id_field=id_field)
            report, failed_gdfs = checker.run_checks()
            return Response({'status': 'success', 'result': report})
        except Exception as e:
            return Response({'status': 'error', 'message': str(e)}, status=500)

@permission_classes([AllowAny])
class GeoIntegrityCheckView(APIView):
    """
    Shapefile数据完整性检查API。
    POST参数：
        shp_path (str): Shapefile路径
        required_fields (list): 必填字段列表
    返回：
        status: 'success' or 'error'
        result: 检查报告（dict）
    """
    def post(self, request):
        data = request.data
        shp_path = data.get('shp_path')
        required_fields = data.get('required_fields', [])
        try:
            checker = geo_integrity_checker.ShpIntegrityChecker()
            result = checker.check(shp_path, required_fields)
            return Response({'status': 'success', 'result': result})
        except Exception as e:
            return Response({'status': 'error', 'message': str(e)}, status=500)

@permission_classes([AllowAny])
class SpatialReferenceCheckView(APIView):
    """
    空间参考检查API。
    POST参数：
        shp_path (str): 文件路径（shp/tif/geojson）
        gcs (str): 标准地理坐标系（如EPSG:4326）
        pcs (str): 标准投影坐标系（可选）
    返回：
        status: 'success' or 'error'
        result: 检查报告（dict）
    """
    def post(self, request):
        data = request.data
        shp_path = data.get('shp_path')
        gcs = data.get('gcs')
        pcs = data.get('pcs')
        try:
            checker = spatial_reference_check.SpatialReferenceChecker(gcs, pcs)
            result = checker.check(shp_path)
            return Response({'status': 'success', 'result': result})
        except Exception as e:
            return Response({'status': 'error', 'message': str(e)}, status=500) 