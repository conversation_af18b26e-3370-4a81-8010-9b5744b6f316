<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据预处理 - 数据规范化处理子系统</title>
    <link href="../lib/css/googleapis.css" rel="stylesheet">
    <link rel="stylesheet" href="lib/css/all.min.css">
    <style>
        /* 设计系统 - 现代化科技蓝主题 */
        :root {
            /* 主色调 */
            --primary-50: #e6f7ff;
            --primary-100: #b3e0ff;
            --primary-200: #80c9ff;
            --primary-300: #4db3ff;
            --primary-400: #1a9cff;
            --primary-500: #0085ff;
            --primary-600: #0066cc;
            --primary-700: #004799;
            --primary-800: #002866;
            --primary-900: #000933;
            
            /* 中性色 */
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            
            /* 背景色 */
            --bg-primary: #0a0f1c;
            --bg-secondary: #111827;
            --bg-tertiary: #1f2937;
            --bg-card: rgba(31, 41, 55, 0.8);
            --bg-overlay: rgba(10, 15, 28, 0.95);
            --bg-sidebar: rgba(15, 20, 25, 0.95);
            
            /* 文字色 */
            --text-primary: #ffffff;
            --text-secondary: #94a3b8;
            --text-tertiary: #64748b;
            
            /* 边框色 */
            --border-primary: #374151;
            --border-secondary: #4b5563;
            --border-accent: #3b82f6;
            
            /* 状态色 */
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --info: #3b82f6;
            
            /* 阴影 */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            
            /* 动画 */
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
            
            /* 间距 */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
            --space-24: 6rem;
            
            /* 圆角 */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
            --radius-3xl: 2rem;
        }

        /* 重置样式 */
        *,
        *::before,
        *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            color: var(--text-primary);
            line-height: 1.6;
            font-size: 16px;
            font-weight: 400;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* 工具类 */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 var(--space-6);
        }

        /* 侧边栏 */
        .sidebar {
            position: fixed;
            top: 80px;
            left: 0;
            width: 280px;
            height: calc(100vh - 80px);
            background: var(--bg-sidebar);
            backdrop-filter: blur(20px);
            border-right: 1px solid var(--border-primary);
            overflow-y: auto;
            z-index: 100;
        }

        .sidebar-header {
            padding: var(--space-6);
            border-bottom: 1px solid var(--border-primary);
        }

        .sidebar-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--space-2);
        }

        .sidebar-subtitle {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .sidebar-menu {
            padding: var(--space-4);
        }

        .menu-category {
            margin-bottom: var(--space-6);
        }

        .menu-category-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-tertiary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: var(--space-3);
            padding: 0 var(--space-3);
        }

        .menu-item {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-3) var(--space-4);
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--radius-md);
            transition: var(--transition-fast);
            margin-bottom: var(--space-1);
            cursor: pointer;
        }

        .menu-item:hover {
            background: rgba(55, 65, 81, 0.3);
            color: var(--text-primary);
        }

        .menu-item.active {
            background: rgba(59, 130, 246, 0.1);
            color: var(--primary-400);
            border-left: 3px solid var(--primary-400);
        }

        .menu-item-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: inherit;
        }

        .menu-item-text {
            flex: 1;
            font-size: 0.875rem;
            font-weight: 500;
        }

        /* 主内容区 */
        .main-content {
            margin-left: 280px;
            padding-top: 80px;
            min-height: 100vh;
        }

        .flex {
            display: flex;
        }

        .flex-col {
            flex-direction: column;
        }

        .items-center {
            align-items: center;
        }

        .justify-between {
            justify-content: space-between;
        }

        .justify-center {
            justify-content: center;
        }

        .text-center {
            text-align: center;
        }

        .gap-4 {
            gap: var(--space-4);
        }

        .gap-6 {
            gap: var(--space-6);
        }

        .gap-8 {
            gap: var(--space-8);
        }

        .mb-4 {
            margin-bottom: var(--space-4);
        }

        .mb-6 {
            margin-bottom: var(--space-6);
        }

        .mb-8 {
            margin-bottom: var(--space-8);
        }

        .mb-12 {
            margin-bottom: var(--space-12);
        }

        .mt-8 {
            margin-top: var(--space-8);
        }

        .mt-12 {
            margin-top: var(--space-12);
        }

        .pt-20 {
            padding-top: var(--space-20);
        }

        .pb-20 {
            padding-bottom: var(--space-20);
        }

        .px-6 {
            padding-left: var(--space-6);
            padding-right: var(--space-6);
        }

        .py-8 {
            padding-top: var(--space-8);
            padding-bottom: var(--space-8);
        }

        .py-12 {
            padding-top: var(--space-12);
            padding-bottom: var(--space-12);
        }

        .py-16 {
            padding-top: var(--space-16);
            padding-bottom: var(--space-16);
        }

        /* 顶部导航栏 */
        .top-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(10, 15, 28, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-primary);
            transition: var(--transition-normal);
        }

        .top-navbar.scrolled {
            background: rgba(10, 15, 28, 0.98);
            box-shadow: var(--shadow-lg);
        }

        .top-navbar-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-4) 0;
        }

        .top-navbar-brand {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
            text-decoration: none;
        }

        .top-navbar-brand-icon {
            width: 28px;
            height: 28px;
            background: linear-gradient(135deg, var(--primary-400), var(--primary-600));
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.875rem;
        }

        .top-navbar-actions {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        /* 按钮组件 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
            padding: var(--space-3) var(--space-6);
            border: none;
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: var(--transition-fast);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: var(--transition-normal);
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: rgba(55, 65, 81, 0.8);
            color: var(--text-primary);
            border: 1px solid var(--border-primary);
        }

        .btn-secondary:hover {
            background: rgba(55, 65, 81, 0.9);
            border-color: var(--primary-400);
            color: var(--primary-400);
        }

        .btn-ghost {
            background: transparent;
            color: var(--text-secondary);
            border: 1px solid transparent;
        }

        .btn-ghost:hover {
            background: rgba(55, 65, 81, 0.5);
            color: var(--text-primary);
        }

        /* 主布局 */
        .main-layout {
            display: flex;
            min-height: 100vh;
            padding-top: 80px;
        }

        /* 侧边栏 */
        .sidebar {
            width: 280px;
            background: var(--bg-sidebar);
            backdrop-filter: blur(20px);
            border-right: 1px solid var(--border-primary);
            padding: var(--space-8) 0;
            position: fixed;
            top: 80px;
            left: 0;
            bottom: 0;
            overflow-y: auto;
        }

        .sidebar-nav {
            padding: 0 var(--space-6);
        }

        .sidebar-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: var(--space-4);
            padding: 0 var(--space-4);
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-3) var(--space-4);
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--radius-md);
            margin-bottom: var(--space-1);
            transition: var(--transition-fast);
            cursor: pointer;
        }

        .sidebar-item:hover {
            background: rgba(59, 130, 246, 0.1);
            color: var(--primary-400);
        }

        .sidebar-item.active {
            background: rgba(59, 130, 246, 0.15);
            color: var(--primary-400);
            border-left: 3px solid var(--primary-400);
        }

        .sidebar-item i {
            width: 20px;
            text-align: center;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: var(--space-8);
        }

        .content-header {
            margin-bottom: var(--space-8);
        }

        .content-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--space-2);
        }

        .content-subtitle {
            color: var(--text-secondary);
            font-size: 1.125rem;
        }

        /* 表单样式 */
        .form-container {
            background: var(--bg-card);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            margin-bottom: var(--space-8);
        }

        .form-group {
            margin-bottom: var(--space-6);
        }

        .form-label {
            display: block;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--space-2);
        }

        .form-input {
            width: 100%;
            padding: var(--space-3) var(--space-4);
            background: rgba(15, 20, 25, 0.8);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-md);
            color: var(--text-primary);
            font-size: 0.875rem;
            transition: var(--transition-fast);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-400);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-select {
            width: 100%;
            padding: var(--space-3) var(--space-4);
            background: rgba(15, 20, 25, 0.8);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-md);
            color: var(--text-primary);
            font-size: 0.875rem;
            transition: var(--transition-fast);
        }

        .form-select:focus {
            outline: none;
            border-color: var(--primary-400);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-textarea {
            width: 100%;
            padding: var(--space-3) var(--space-4);
            background: rgba(15, 20, 25, 0.8);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-md);
            color: var(--text-primary);
            font-size: 0.875rem;
            transition: var(--transition-fast);
            resize: vertical;
            min-height: 100px;
        }

        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-400);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 进度条 */
        .progress-container {
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
            padding: var(--space-4);
            margin: var(--space-4) 0;
            display: none;
        }
        
        .progress-bar {
            background: var(--bg-tertiary);
            border-radius: var(--border-radius);
            height: 20px;
            overflow: hidden;
            position: relative;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, var(--primary-500), var(--primary-400));
            height: 100%;
            width: 0%;
            transition: width 0.5s ease; /* 增加过渡时间，使进度更平滑 */
            border-radius: var(--border-radius);
        }
        
        .progress-text {
            margin-top: var(--space-2);
            text-align: center;
            color: var(--text-secondary);
            font-size: 0.9em;
        }
        
        /* 闪烁动画效果 */
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.7; }
        }
        
        /* mosaic功能特殊样式 */
        .mosaic-processing {
            background: linear-gradient(90deg, var(--primary-500), var(--primary-400));
            transition: width 0.8s ease; /* mosaic功能使用更长的过渡时间 */
        }

        /* 结果区域 */
        .result-container {
            display: none;
            background: var(--bg-card);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            margin-top: var(--space-8);
        }

        .result-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: var(--space-6);
            padding-bottom: var(--space-4);
            border-bottom: 1px solid var(--border-primary);
        }

        .result-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .result-status {
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-md);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }

        .status-error {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error);
        }

        .status-warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning);
        }

        /* 模态框 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            z-index: 2000;
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: var(--bg-overlay);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-xl);
            padding: var(--space-8);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-6);
            padding-bottom: var(--space-4);
            border-bottom: 1px solid var(--border-primary);
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .modal-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.5rem;
            cursor: pointer;
            padding: var(--space-2);
            border-radius: var(--radius-md);
            transition: var(--transition-fast);
        }

        .modal-close:hover {
            background: rgba(55, 65, 81, 0.5);
            color: var(--text-primary);
        }

        .modal-body {
            color: var(--text-secondary);
        }

        .modal-body h4 {
            color: var(--text-primary);
            margin-bottom: var(--space-3);
            font-weight: 600;
        }

        .modal-body ul {
            margin-left: var(--space-4);
            margin-bottom: var(--space-6);
        }

        .modal-body li {
            margin-bottom: var(--space-1);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .sidebar {
                transform: translateX(-100%);
                transition: var(--transition-normal);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 var(--space-4);
            }

            .main-content {
                padding: var(--space-4);
            }

            .top-navbar-actions {
                gap: var(--space-2);
            }

            .btn {
                padding: var(--space-2) var(--space-4);
                font-size: 0.8rem;
            }

            .menu-category {
                margin-bottom: var(--space-4);
            }

            .menu-item {
                padding: var(--space-2) var(--space-3);
            }

            .form-container {
                padding: var(--space-6);
            }
        }

        /* 动画 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        .animate-fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.8s ease-out;
        }

        /* 文件上传样式 */
        .file-upload-container {
            position: relative;
            margin-bottom: 1rem;
        }
        
        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        .file-label {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            border: 2px dashed var(--border-color);
            border-radius: var(--radius-lg);
            background: var(--bg-card);
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .file-label:hover {
            border-color: var(--primary-color);
            background: var(--bg-hover);
        }
        
        .file-label i {
            font-size: 2rem;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
        }
        
        .file-label span {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }
        
        .file-label small {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .file-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.75rem;
            background: var(--bg-success);
            border-radius: var(--radius-lg);
            margin-top: 0.5rem;
        }
        
        .file-details {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .file-details i {
            color: var(--success-color);
        }
        
        .file-size {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .btn-remove {
            background: none;
            border: none;
            color: var(--danger-color);
            cursor: pointer;
            padding: 0.25rem;
            border-radius: var(--radius-sm);
            transition: background 0.2s ease;
        }
        
        .btn-remove:hover {
            background: var(--bg-danger);
        }
        
        .upload-progress {
            margin-top: 0.5rem;
        }
        
        .upload-progress .progress-bar {
            height: 4px;
            background: var(--bg-secondary);
            border-radius: 2px;
            overflow: hidden;
        }
        
        .upload-progress .progress-fill {
            height: 100%;
            background: var(--primary-color);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .upload-progress .progress-text {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-secondary);
            border-radius: var(--radius-md);
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--border-primary);
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="top-navbar" id="topNavbar">
        <div class="container">
            <div class="top-navbar-content">
                <a href="/" class="top-navbar-brand">
                    <div class="top-navbar-brand-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <span>数据规范化处理子系统</span>
                </a>
                <div class="top-navbar-actions">
                    <button class="btn btn-ghost" id="helpBtn">
                        <i class="fas fa-question-circle"></i>
                        <span>帮助</span>
                    </button>
                    <button class="btn btn-ghost" id="historyBtn">
                        <i class="fas fa-history"></i>
                        <span>历史</span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主布局 -->
    <div class="main-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-title">数据预处理</div>
                <div class="sidebar-subtitle">选择功能模块开始处理</div>
            </div>
            <nav class="sidebar-menu">
                <div class="menu-category">
                    <div class="menu-category-title">文件处理</div>
                    <a href="#" class="menu-item active" data-function="text_extract">
                        <div class="menu-item-icon">
                            <i class="fas fa-file-text"></i>
                        </div>
                        <div class="menu-item-text">文本提取</div>
                    </a>
                    <a href="#" class="menu-item" data-function="file_preprocess">
                        <div class="menu-item-icon">
                            <i class="fas fa-file-upload"></i>
                        </div>
                        <div class="menu-item-text">文件预处理</div>
                    </a>
                </div>

                <div class="menu-category">
                    <div class="menu-category-title">坐标转换</div>
                    <a href="#" class="menu-item" data-function="reproject_transform">
                        <div class="menu-item-icon">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="menu-item-text">geojson/shp文件坐标转换</div>
                    </a>
                    <a href="#" class="menu-item" data-function="single_coord">
                        <div class="menu-item-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="menu-item-text">单点坐标转换</div>
                    </a>
                </div>

                <div class="menu-category">
                    <div class="menu-category-title">批量定义投影</div>
                    <a href="#" class="menu-item" data-function="batch_reproject">
                        <div class="menu-item-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <div class="menu-item-text">批量定义投影</div>
                    </a>
                </div>

                <div class="menu-category">
                    <div class="menu-category-title">数据接边</div>
                    <a href="#" class="menu-item" data-function="mosaic">
                        <div class="menu-item-icon">
                            <i class="fas fa-puzzle-piece"></i>
                        </div>
                        <div class="menu-item-text">数据接边</div>
                    </a>
                </div>

                <div class="menu-category">
                    <div class="menu-category-title">空间信息计算</div>
                    <a href="#" class="menu-item" data-function="shp_info">
                        <div class="menu-item-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="menu-item-text">Shp文件空间信息计算</div>
                    </a>
                    <a href="#" class="menu-item" data-function="tif_info">
                        <div class="menu-item-icon">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="menu-item-text">TIF信息空间信息计算</div>
                    </a>
                </div>

                <div class="menu-category">
                    <div class="menu-category-title">影像数据预处理</div>
                    <a href="#" class="menu-item" data-function="image_process">
                        <div class="menu-item-icon">
                            <i class="fas fa-crop"></i>
                        </div>
                        <div class="menu-item-text">影像处理</div>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <div class="content-header">
                <h1 class="content-title">数据预处理</h1>
                <p class="content-subtitle">选择预处理功能，上传数据文件，开始数据处理</p>
            </div>

            <!-- 表单容器 -->
            <div class="form-container animate-fade-in-up">
                <form id="preprocessForm">
                    <input type="hidden" id="functionSelect" name="functionSelect" value="text_extract">

                    <div class="form-group">
                        <label class="form-label">上传文件</label>
                        <div class="file-upload-container">
                            <input type="file" id="fileInput" class="file-input" accept=".zip,.tif,.geojson,.txt,.doc,.docx,.pdf,.png,.jpg,.jpeg" required>
                            <label for="fileInput" class="file-label">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <span>选择文件或拖拽到此处</span>
                                <small id="fileTypeTip">支持 TXT, DOC, DOCX, PDF, TIF, GeoJSON, PNG, JPG, 以及 <b>Shapefile请上传zip包</b></small>
                            </label>
                        </div>
                        <div id="fileInfo" class="file-info" style="display: none;">
                            <div class="file-details">
                                <i class="fas fa-file-alt"></i>
                                <span id="fileName"></span>
                                <span id="fileSize" class="file-size"></span>
                            </div>
                            <button type="button" class="btn-remove" onclick="removeFile()" title="移除文件">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div id="uploadProgress" class="upload-progress" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <span class="progress-text">上传中...</span>
                        </div>
                    </div>

                    <div id="dynamicForm" class="form-group">
                        <!-- 动态表单内容 -->
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-play"></i>
                        <span>开始处理</span>
                    </button>
                </form>
            </div>

            <!-- 进度条 -->
            <div class="progress-container" id="progressContainer">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">处理中...</div>
            </div>

            <!-- 结果容器 -->
            <div class="result-container" id="resultContainer">
                <div class="result-header">
                    <h3 class="result-title">处理结果</h3>
                    <span class="result-status status-success" id="resultStatus">成功</span>
                </div>
                <div id="resultContent">
                    <!-- 结果内容 -->
                </div>
            </div>
        </main>
    </div>

    <!-- 帮助模态框 -->
    <div class="modal" id="helpModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">预处理功能帮助</h3>
                <button class="modal-close" onclick="closeModal('helpModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="mb-6">
                    <h4>
                        <i class="fas fa-file-text" style="margin-right: 0.5rem;"></i>
                        文本提取
                    </h4>
                    <ul>
                        <li>• 支持从Word、PDF、TXT等文档中提取文本</li>
                        <li>• 自动识别文档编码格式</li>
                        <li>• 输出为UTF-8编码的文本文件</li>
                    </ul>
                </div>
                
                <div class="mb-6">
                    <h4>
                        <i class="fas fa-exchange-alt" style="margin-right: 0.5rem;"></i>
                        坐标转换
                    </h4>
                    <ul>
                        <li>• 支持WGS84、CGCS2000、Beijing1954等坐标系</li>
                        <li>• 支持地理坐标与投影坐标转换</li>
                        <li>• 支持批量转换多个文件</li>
                    </ul>
                </div>
                
                <div class="mb-6">
                    <h4>
                        <i class="fas fa-image" style="margin-right: 0.5rem;"></i>
                        影像处理
                    </h4>
                    <ul>
                        <li>• 影像增强、重采样、裁剪等操作</li>
                        <li>• 支持多种影像格式</li>
                        <li>• 支持批量处理</li>
                    </ul>
                </div>
                
                <div>
                    <h4>
                        <i class="fas fa-lightbulb" style="margin-right: 0.5rem;"></i>
                        使用建议
                    </h4>
                    <ul>
                        <li>• 建议先检查文件格式是否支持</li>
                        <li>• 大文件处理可能需要较长时间</li>
                        <li>• 处理完成后请及时下载结果文件</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 历史记录模态框 -->
    <div class="modal" id="historyModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">预处理历史记录</h3>
                <button class="modal-close" onclick="closeModal('historyModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h4>历史任务</h4>
                        <button class="btn btn-secondary" onclick="clearHistory()">
                            <i class="fas fa-trash" style="margin-right: 0.25rem;"></i>
                            清空
                        </button>
                    </div>
                    <div id="historyList" class="space-y-2 max-h-40 overflow-y-auto"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let uploadResult = null;
        // 导航栏滚动效果
        window.addEventListener('scroll', () => {
            const navbar = document.getElementById('topNavbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // 侧边栏功能选择
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                
                // 更新侧边栏激活状态
                document.querySelectorAll('.menu-item').forEach(i => i.classList.remove('active'));
                item.classList.add('active');
                
                // 更新表单选择
                const functionValue = item.getAttribute('data-function');
                const functionSelect = document.getElementById('functionSelect');
                functionSelect.value = functionValue;
                
                // 清空表单
                clearForm();
                
                // 更新动态表单
                updateDynamicForm(functionValue);
                
                // 更新页面标题显示当前选择的功能
                updatePageTitle(functionValue);
            });
        });

        // 文件上传处理
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const uploadProgress = document.getElementById('uploadProgress');

        fileInput.addEventListener('change', handleFileSelect);
        fileInput.addEventListener('dragover', handleDragOver);
        fileInput.addEventListener('drop', handleFileDrop);

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                displayFileInfo(file);
            }
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.closest('.file-label').style.borderColor = 'var(--primary-color)';
        }

        function handleFileDrop(e) {
            e.preventDefault();
            const file = e.dataTransfer.files[0];
            if (file) {
                fileInput.files = e.dataTransfer.files;
                displayFileInfo(file);
            }
            e.currentTarget.closest('.file-label').style.borderColor = 'var(--border-color)';
        }

        function displayFileInfo(file) {
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.style.display = 'flex';
            uploadProgress.style.display = 'none';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function removeFile() {
            fileInput.value = '';
            fileInfo.style.display = 'none';
            uploadProgress.style.display = 'none';
        }

        function clearForm() {
            // 清空文件上传
            fileInput.value = '';
            fileInfo.style.display = 'none';
            uploadProgress.style.display = 'none';
            
            // 清空动态表单
            const dynamicForm = document.getElementById('dynamicForm');
            dynamicForm.innerHTML = '';
            
            // 隐藏进度和结果
            document.getElementById('progressContainer').style.display = 'none';
            document.getElementById('resultContainer').style.display = 'none';
        }



        // 更新页面标题显示当前选择的功能
        function updatePageTitle(functionType) {
            const contentTitle = document.querySelector('.content-title');
            const functionNames = {
                'text_extract': '文本提取',
                'file_preprocess': '文件预处理',
                'reproject_transform': '坐标重投影',
                'batch_reproject': '批量重投影',
                'single_coord': '单点坐标转换',
                'geojson_convert': 'GeoJSON转换',
                'geojson_shp': 'Shapefile转换',
                'shp_info': 'Shapefile信息提取',
                'tif_info': 'TIF信息提取',
                'image_process': '影像处理',
                'mosaic': '影像拼接'
            };
            
            const functionName = functionNames[functionType] || '数据预处理';
            contentTitle.textContent = functionName;
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化默认功能
            updateDynamicForm('text_extract');
            updatePageTitle('text_extract');
        });

        // 更新动态表单
        function updateDynamicForm(functionType) {
            const dynamicForm = document.getElementById('dynamicForm');
            let formHTML = '';

            switch(functionType) {
                case 'text_extract':
                    formHTML = `
{#                        <div class="form-group">#}
{#                            <label class="form-label">输出格式</label>#}
{#                            <select class="form-select" id="outputFormat" name="output_format">#}
{#                                <option value="txt">TXT文本文件</option>#}
{#                                <option value="csv">CSV表格文件</option>#}
{#                                <option value="json">JSON格式</option>#}
{#                            </select>#}
{#                        </div>#}
{#                        <div class="form-group">#}
{#                            <label class="form-label">编码格式</label>#}
{#                            <select class="form-select" id="encodingFormat" name="encoding">#}
{#                                <option value="utf-8">UTF-8</option>#}
{#                                <option value="gbk">GBK</option>#}
{#                                <option value="gb2312">GB2312</option>#}
{#                            </select>#}
{#                        </div>#}
                    `;
                    break;
                case 'file_preprocess':
                    formHTML = `

                    `;
                    break;
                case 'reproject_transform':
                    formHTML = `
                        <div class="form-group">
                            <label class="form-label">源坐标系</label>
                            <select class="form-select" id="srcCrs" name="source_crs">
                                <option value="EPSG:4326">WGS84 (EPSG:4326)</option>
                                <option value="EPSG:4490">CGCS2000 (EPSG:4490)</option>
                                {#<option value="EPSG:4214">Beijing1954 (EPSG:4214)</option>#}
                                <option value="EPSG:3857">Web Mercator (EPSG:3857)</option>
                                {#<option value="EPSG:32648">UTM Zone 48N (EPSG:32648)</option>#}
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">目标坐标系</label>
                            <select class="form-select" id="dstCrs" name="target_crs">
                                <option value="EPSG:4326">WGS84 (EPSG:4326)</option>
                                <option value="EPSG:4490">CGCS2000 (EPSG:4490)</option>
                                {#<option value="EPSG:4214">Beijing1954 (EPSG:4214)</option>#}
                                <option value="EPSG:3857">Web Mercator (EPSG:3857)</option>
                                {#<option value="EPSG:32648">UTM Zone 48N (EPSG:32648)</option>#}
                            </select>
                        </div>
{#                        <div class="form-group">#}
{#                            <label class="form-label">重采样方法</label>#}
{#                            <select class="form-select" id="resampleMethod" name="resample_method">#}
{#                                <option value="nearest">最近邻</option>#}
{#                                <option value="bilinear">双线性</option>#}
{#                                <option value="cubic">三次卷积</option>#}
{#                            </select>#}
{#                        </div>#}
                    `;
                    break;
                case 'batch_reproject':
                    formHTML = `
                        <div class="form-group">
                            <label class="form-label">源坐标系</label>
                            <select class="form-select" id="srcCrs" name="source_crs">
                                <option value="EPSG:4326">WGS84 (EPSG:4326)</option>
                                <option value="EPSG:4490">CGCS2000 (EPSG:4490)</option>
                                <option value="EPSG:4214">Beijing1954 (EPSG:4214)</option>
                                <option value="EPSG:3857">Web Mercator (EPSG:3857)</option>
                                <option value="EPSG:32648">UTM Zone 48N (EPSG:32648)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">目标坐标系</label>
                            <select class="form-select" id="dstCrs" name="target_crs">
                                <option value="EPSG:4326">WGS84 (EPSG:4326)</option>
                                <option value="EPSG:4490">CGCS2000 (EPSG:4490)</option>
                                <option value="EPSG:4214">Beijing1954 (EPSG:4214)</option>
                                <option value="EPSG:3857">Web Mercator (EPSG:3857)</option>
                                <option value="EPSG:32648">UTM Zone 48N (EPSG:32648)</option>
                            </select>
                        </div>
{#                        <div class="form-group">#}
{#                            <label class="form-label">输出目录</label>#}
{#                            <input type="text" class="form-input" id="outputDir" name="output_dir" placeholder="输出目录路径">#}
{#                        </div>#}
                    `;
                    break;
                case 'single_coord':
                    formHTML = `
                        <div class="form-group">
                            <label class="form-label">坐标输入</label>
                            <textarea class="form-textarea" id="coordInput" name="coordinates" placeholder="请输入坐标，格式：经度,纬度&#10;例如：116.3974,39.9093"></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">源坐标系</label>
                            <select class="form-select" id="srcCrs" name="source_crs">
                                <option value="EPSG:4326">WGS84 (EPSG:4326)</option>
                                <option value="EPSG:3857">Web Mercator (EPSG:3857)</option>
                                <option value="EPSG:4490">CGCS2000 (EPSG:4490)</option>
                                <option value="EPSG:4214">Beijing1954 (EPSG:4214)</option>
                                <option value="EPSG:32648">UTM Zone 48N (EPSG:32648)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">目标坐标系</label>
                            <select class="form-select" id="dstCrs" name="target_crs">
                                <option value="EPSG:4326">WGS84 (EPSG:4326)</option>
                                <option value="EPSG:3857">Web Mercator (EPSG:3857)</option>
                                <option value="EPSG:4490">CGCS2000 (EPSG:4490)</option>
                                <option value="EPSG:4214">Beijing1954 (EPSG:4214)</option>
                                <option value="EPSG:32648">UTM Zone 48N (EPSG:32648)</option>
                            </select>
                        </div>
                    `;
                    break;
                case 'geojson_convert':
                    formHTML = `
                        <div class="form-group">
                            <label class="form-label">GeoJSON输入</label>
                            <textarea class="form-textarea" id="geojsonInput" name="geojson_input" placeholder="输入GeoJSON数据或文件路径"></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">源坐标系</label>
                            <select class="form-select" id="srcCrs" name="source_crs">
                                <option value="EPSG:4326">WGS84 (EPSG:4326)</option>
                                <option value="EPSG:3857">Web Mercator (EPSG:3857)</option>
                                <option value="EPSG:4490">CGCS2000 (EPSG:4490)</option>
                                <option value="EPSG:4214">Beijing1954 (EPSG:4214)</option>
                                <option value="EPSG:32648">UTM Zone 48N (EPSG:32648)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">目标坐标系</label>
                            <select class="form-select" id="dstCrs" name="target_crs">
                                <option value="EPSG:4326">WGS84 (EPSG:4326)</option>
                                <option value="EPSG:3857">Web Mercator (EPSG:3857)</option>
                                <option value="EPSG:4490">CGCS2000 (EPSG:4490)</option>
                                <option value="EPSG:4214">Beijing1954 (EPSG:4214)</option>
                                <option value="EPSG:32648">UTM Zone 48N (EPSG:32648)</option>
                            </select>
                        </div>
                    `;
                    break;
                case 'geojson_shp':
                    formHTML = `
                        <div class="form-group">
                            <label class="form-label">输出路径</label>
                            <input type="text" class="form-input" id="outputPath" name="output_path" placeholder="输出文件路径">
                        </div>
                        <div class="form-group">
                            <label class="form-label">源坐标系</label>
                            <select class="form-select" id="srcCrs" name="source_crs">
                                <option value="EPSG:4326">WGS84 (EPSG:4326)</option>
                                <option value="EPSG:3857">Web Mercator (EPSG:3857)</option>
                                <option value="EPSG:4490">CGCS2000 (EPSG:4490)</option>
                                <option value="EPSG:4214">Beijing1954 (EPSG:4214)</option>
                                <option value="EPSG:32648">UTM Zone 48N (EPSG:32648)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">目标坐标系</label>
                            <select class="form-select" id="dstCrs" name="target_crs">
                                <option value="EPSG:4326">WGS84 (EPSG:4326)</option>
                                <option value="EPSG:3857">Web Mercator (EPSG:3857)</option>
                                <option value="EPSG:4490">CGCS2000 (EPSG:4490)</option>
                                <option value="EPSG:4214">Beijing1954 (EPSG:4214)</option>
                                <option value="EPSG:32648">UTM Zone 48N (EPSG:32648)</option>
                            </select>
                        </div>
                    `;
                    break;
                case 'shp_info':
                    formHTML = `

                        <div class="form-group">
                            <label class="form-label">返回所有要素详细信息</label>
                            <input type="checkbox" class="form-checkbox" id="returnAllFeatures" name="return_all_features">
                            <label for="returnAllFeatures">返回所有要素的详细信息（默认只返回前10个样本）</label>
                        </div>
                    `;
                    break;
                case 'tif_info':
                    formHTML = `

                    `;
                    break;
                case 'image_process':
                    formHTML = `
                        <div class="form-group">
                            <label class="form-label">去噪处理</label>
                            <input type="checkbox" class="form-checkbox" id="denoise" name="denoise">
                            <label for="denoise">启用去噪</label>
                        </div>
                        <div class="form-group">
                            <label class="form-label">构建金字塔</label>
                            <input type="checkbox" class="form-checkbox" id="buildPyramids" name="build_pyramids" checked>
                            <label for="buildPyramids">构建影像金字塔</label>
                        </div>
                        <div class="form-group">
                            <label class="form-label">创建缩略图</label>
                            <input type="checkbox" class="form-checkbox" id="createThumbnail" name="create_thumbnail" checked>
                            <label for="createThumbnail">创建缩略图</label>
                        </div>
                        <div class="form-group">
                            <label class="form-label">重采样分辨率</label>
                            <input type="text" class="form-input" id="resampleResolution" name="resample_resolution" placeholder="如 200,200 或 0.1,0.1">
                        </div>
                    `;
                    break;
                case 'mosaic':
                    formHTML = `
                        <div class="form-group">
                            <label class="form-label">拼接参数（可选）</label>
                            <div style="display: flex; flex-wrap: wrap; gap: 1rem;">
                                <div style="flex:1;min-width:180px;">
                                    <label class="form-label">目标坐标系</label>
                                    <select class="form-select" id="targetCrs" name="target_crs">
                                        <option value="EPSG:4326">WGS84 (EPSG:4326)</option>
                                        <option value="EPSG:3857">Web Mercator (EPSG:3857)</option>
                                    </select>
                                </div>
                                <div style="flex:1;min-width:180px;">
                                    <label class="form-label">重采样算法</label>
                                    <select class="form-select" id="resampleAlg" name="resample_alg">
                                        <option value="cubic">cubic</option>
                                        <option value="nearest">nearest</option>
                                        <option value="bilinear">bilinear</option>
                                        <option value="average">average</option>
                                    </select>
                                </div>
                                <div style="flex:1;min-width:180px;">
                                    <label class="form-label">金字塔</label>
                                    <input type="checkbox" id="buildOverviews" name="build_overviews" checked>
                                    <label for="buildOverviews">构建金字塔</label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">请上传包含一组TIF的zip包</label>
                            <div class="alert alert-info" style="margin-top: 10px;">
                                <i class="fas fa-info-circle"></i>
                                <strong>数据接边提示：</strong>
                                <ul style="margin: 5px 0; padding-left: 20px;">
                                    <li>支持大文件上传，处理时间较长</li>
                                    <li>建议文件大小不超过2GB</li>
                                    <li>处理过程中请耐心等待</li>
                                    <li>可随时查看处理进度</li>
                                </ul>
                            </div>
                        </div>
                    `;
                    break;
            }

            dynamicForm.innerHTML = formHTML;

            // 1. 动态表单切换到single_coord时隐藏上传控件
            if (functionType === 'single_coord') {
                document.getElementById('fileInput').required = false;
                // 可选：隐藏上传区域
                document.querySelector('.file-upload-container').style.display = 'none';
            } else {
                document.getElementById('fileInput').required = true;
                document.querySelector('.file-upload-container').style.display = '';
            }

            const fileInput = document.getElementById('fileInput');
            const fileTypeTip = document.getElementById('fileTypeTip');
            const shpTypes = ['shp_info', 'geojson_shp', 'reproject_transform', 'batch_reproject'];
            if (shpTypes.includes(functionType)) {
                fileInput.accept = '.zip';
                fileTypeTip.innerHTML = '请上传包含 .shp/.shx/.dbf/.prj 等所有文件的 <b>zip包</b>'; 
            } else {
                fileInput.accept = '.txt,.doc,.docx,.pdf,.tif,.geojson,.png,.jpg,.jpeg,.zip';
                fileTypeTip.innerHTML = '支持 TXT, DOC, DOCX, PDF, TIF, GeoJSON, PNG, JPG, 以及 <b>Shapefile请上传zip包</b>';
            }

            if (functionType === 'reproject_transform') {
                fileInput.accept = '.geojson,.zip';
                fileTypeTip.innerHTML = '只支持GeoJSON(.geojson)和Shapefile(.zip)上传';
            } else if (functionType === 'batch_reproject') {
                fileInput.accept = '.zip';
                fileTypeTip.innerHTML = '请上传TIF文件的zip包（包内全是tif）';
            }
        }

        // 表单提交
        document.getElementById('preprocessForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const functionType = formData.get('functionSelect');
            const shpTypes = ['shp_info', 'geojson_shp', 'reproject_transform', 'batch_reproject'];
            if (shpTypes.includes(functionType)) {
                const fileInput = document.getElementById('fileInput');
                if (!fileInput.files || fileInput.files.length === 0) {
                    alert('请上传GeoJSON或Shapefile zip包');
                    return;
                }
                const file = fileInput.files[0];
                if (!(file.name.endsWith('.geojson') || file.name.endsWith('.zip'))) {
                    alert('坐标重投影只支持GeoJSON(.geojson)和Shapefile(.zip)上传！');
                    return;
                }
            }
            
            // 需要文件的功能类型
            const fileRequiredTypes = [
                'text_extract', 'file_preprocess', 'reproject_transform', 'batch_reproject',
                'geojson_shp', 'shp_info', 'tif_info', 'image_process', 'mosaic'
            ];
            
            let requestData = {
                function_type: functionType,
                params: {}
            };
            
            // 如果需要文件但还没有上传结果，先上传文件
            if (fileRequiredTypes.includes(functionType)) {
                const fileInput = document.getElementById('fileInput');
                if (!fileInput.files || fileInput.files.length === 0) {
                    alert('请上传文件');
                    return;
                }
                
                // 获取文件信息
                const file = fileInput.files[0];
                
                // 上传文件
                const uploadFormData = new FormData();
                uploadFormData.append('file', file);
                
                try {
                    const uploadResponse = await fetch('/glff/api/preprocess/upload/', {
                        method: 'POST',
                        body: uploadFormData
                    });
                    
                    if (!uploadResponse.ok) {
                        const errorData = await uploadResponse.json();
                        throw new Error(errorData.error || '文件上传失败');
                    }
                    
                    uploadResult = await uploadResponse.json();
                    console.log('文件上传成功:', uploadResult);
                    
                    // 设置文件相关参数
                    requestData.file_id = uploadResult.file_id;
                    requestData.filename = uploadResult.filename;
                } catch (error) {
                    console.error('文件上传失败:', error);
                    alert(`文件上传失败: ${error.message}`);
                    return;
                }
            } else {
                // 不需要文件的功能类型，不传文件相关参数
                delete requestData.file_id;
                delete requestData.filename;
            }

            // 根据功能类型添加特定参数
            if (functionType === 'text_extract') {
                const outputFormat = document.getElementById('outputFormat');
                const encodingFormat = document.getElementById('encodingFormat');
                if (outputFormat) requestData.params.output_format = outputFormat.value;
                if (encodingFormat) requestData.params.encoding = encodingFormat.value;
            } else if (functionType === 'file_preprocess') {
                const outputFormat = document.getElementById('outputFormat');
                if (outputFormat) requestData.params.output_format = outputFormat.value;
            } else if (functionType === 'reproject_transform') {
                const srcCrs = document.getElementById('srcCrs');
                const dstCrs = document.getElementById('dstCrs');
                const resampleMethod = document.getElementById('resampleMethod');
                if (srcCrs) requestData.params.src_crs = srcCrs.value;
                if (dstCrs) requestData.params.dst_crs = dstCrs.value;
                if (resampleMethod) requestData.params.resample_method = resampleMethod.value;
            } else if (functionType === 'batch_reproject') {
                const srcCrs = document.getElementById('srcCrs');
                const dstCrs = document.getElementById('dstCrs');
                const outputDir = document.getElementById('outputDir');
                if (srcCrs) requestData.params.src_crs = srcCrs.value;
                if (dstCrs) requestData.params.dst_crs = dstCrs.value;
                if (outputDir) requestData.params.output_dir = outputDir.value;
            } else if (functionType === 'single_coord') {
                // 单点坐标转换不需要文件，但需要将功能类型映射到后端支持的类型
                requestData.function_type = 'single_coord_transform';
                const coordInput = document.getElementById('coordInput');
                const srcCrs = document.getElementById('srcCrs');
                const dstCrs = document.getElementById('dstCrs');
                if (coordInput) requestData.params.coord = coordInput.value;
                if (srcCrs) requestData.params.src_crs = srcCrs.value;
                if (dstCrs) requestData.params.dst_crs = dstCrs.value;
            } else if (functionType === 'geojson_convert') {
                const geojsonInput = document.getElementById('geojsonInput');
                const srcCrs = document.getElementById('srcCrs');
                const dstCrs = document.getElementById('dstCrs');
                if (geojsonInput) requestData.params.geojson = geojsonInput.value;
                if (srcCrs) requestData.params.src_crs = srcCrs.value;
                if (dstCrs) requestData.params.dst_crs = dstCrs.value;
            } else if (functionType === 'geojson_shp') {
                const outputPath = document.getElementById('outputPath');
                const srcCrs = document.getElementById('srcCrs');
                const dstCrs = document.getElementById('dstCrs');
                if (outputPath) requestData.params.output_path = outputPath.value;
                if (srcCrs) requestData.params.src_crs = srcCrs.value;
                if (dstCrs) requestData.params.dst_crs = dstCrs.value;
            } else if (functionType === 'shp_info') {
                const infoType = document.querySelector('select[name="info_type"]');
                const returnAllFeatures = document.getElementById('returnAllFeatures');
                if (infoType) requestData.params.info_type = infoType.value;
                if (returnAllFeatures) requestData.params.return_all_features = returnAllFeatures.checked;
            } else if (functionType === 'tif_info') {
                const infoType = document.querySelector('select[name="info_type"]');
                if (infoType) requestData.params.info_type = infoType.value;
            } else if (functionType === 'image_process') {
                // 只收集 test_api 所需参数
                const denoise = document.getElementById('denoise');
                const buildPyramids = document.getElementById('buildPyramids');
                const createThumbnail = document.getElementById('createThumbnail');
                const resampleResolution = document.getElementById('resampleResolution');
                if (denoise) requestData.params.denoise = denoise.checked;
                if (buildPyramids) requestData.params.build_pyramids = buildPyramids.checked;
                if (createThumbnail) requestData.params.create_thumbnail = createThumbnail.checked;
                if (resampleResolution && resampleResolution.value) {
                    requestData.params.resample_resolution = resampleResolution.value.split(',').map(Number);
                }
            } else if (functionType === 'mosaic') {
                // 检查文件大小
                const fileInput = document.getElementById('fileInput');
                if (fileInput && fileInput.files && fileInput.files[0]) {
                    const fileSizeMB = fileInput.files[0].size / (1024 * 1024);
                    
                    // 大文件警告（简化）
                    if (fileSizeMB > 100) {
                        console.log(`文件大小: ${fileSizeMB.toFixed(2)} MB，处理时间可能较长`);
                    }
                }
                
                // 只传options参数
                const targetCrs = document.getElementById('targetCrs');
                const resampleAlg = document.getElementById('resampleAlg');
                const buildOverviews = document.getElementById('buildOverviews');
                requestData.params.options = {
                    target_crs: targetCrs ? targetCrs.value : 'EPSG:4326',
                    resample_alg: resampleAlg ? resampleAlg.value : 'cubic',
                    build_overviews: buildOverviews ? buildOverviews.checked : true
                };
            }
            
            // 提交处理请求
            const processResponse = await fetch('/glff/api/preprocess/run/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });
            
            if (!processResponse.ok) {
                throw new Error('处理请求失败');
            }
            
            const processResult = await processResponse.json();
            const taskId = processResult.task_id;
            
            // 显示进度条
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('resultContainer').style.display = 'none';
            
            // 为mosaic功能添加特殊样式
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            if (functionType === 'mosaic') {
                progressFill.classList.add('mosaic-processing');
                // 为mosaic功能设置初始进度
                progressFill.style.width = '0%';
                progressText.textContent = '数据接边处理中... 0%';
            } else {
                progressFill.classList.remove('mosaic-processing');
            }

            // 模拟进度
            let progress = 0;
            
            // 检查任务状态
            const checkTaskStatus = async () => {
                try {
                    const statusResponse = await fetch(`/glff/api/preprocess/status/?task_id=${taskId}`);
                    if (statusResponse.ok) {
                        const statusData = await statusResponse.json();
                        console.log('状态检查结果:', statusData); // 添加调试信息
                        
                        if (statusData.status === 'completed') {
                            clearInterval(progressInterval);
                            
                            progressFill.style.width = '100%';
                            progressText.textContent = '处理完成!';
                            // 移除mosaic特殊样式
                            if (functionType === 'mosaic') {
                                progressFill.classList.remove('mosaic-processing');
                                progressText.style.animation = '';
                            }
                            setTimeout(() => {
                                showResult(taskId);
                                saveToHistory(functionType);
                            }, 500);
                            return;
                        } else if (statusData.status === 'error') {
                            clearInterval(progressInterval);
                            
                            // 移除mosaic特殊样式
                            if (functionType === 'mosaic') {
                                progressFill.classList.remove('mosaic-processing');
                                progressText.style.animation = '';
                            }
                            throw new Error(statusData.error || '处理失败');
                        } else if (statusData.status === 'processing' && functionType === 'mosaic') {
                            // 使用后端返回的真实进度，但避免大幅跳动
                            if (statusData.progress !== undefined) {
                                const realProgress = statusData.progress;
                                // 如果真实进度比当前进度高，则更新
                                if (realProgress > progress) {
                                    progress = realProgress;
                                    progressFill.style.width = progress + '%';
                                    progressText.textContent = `数据接边处理中... ${Math.round(progress)}%`;
                                }
                            }
                        } else if (statusData.status === 'processing') {
                            // 其他功能的处理状态
                            if (statusData.progress !== undefined) {
                                const realProgress = statusData.progress;
                                if (realProgress > progress) {
                                    progress = realProgress;
                                    progressFill.style.width = progress + '%';
                                    progressText.textContent = `处理中... ${Math.round(progress)}%`;
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.error('状态检查错误:', error);
                }
            };

            // 模拟进度并检查任务状态
            const progressInterval = setInterval(() => {
                if (functionType === 'mosaic') {
                    // mosaic功能：只依赖后端真实进度，不进行模拟
                    // 这里不做任何进度更新，完全依赖checkTaskStatus中的真实进度
                    // 直接检查任务状态，不依赖progress变量
                    checkTaskStatus();
                } else {
                    // 其他功能：使用模拟进度
                    let progressIncrement = Math.random() * 15;
                    progress += progressIncrement;
                    if (progress > 90) progress = 90; // 最多到90%，等待真实完成
                    
                    // 更新进度条宽度和文字
                    progressFill.style.width = progress + '%';
                    progressText.textContent = `处理中... ${Math.round(progress)}%`;
                    
                    // 检查任务状态的频率
                    let checkFrequency = 30;
                    if (progress % checkFrequency === 0) {
                        checkTaskStatus();
                    }
                }
            }, 200);
            
        });

        // 显示结果
        function showResult(taskId) {
            document.getElementById('progressContainer').style.display = 'none';
            document.getElementById('resultContainer').style.display = 'block';
            
            const resultContent = document.getElementById('resultContent');
            
            // 获取当前功能类型，添加安全检查
            const functionSelect = document.querySelector('select[name="functionSelect"]');
            const functionType = functionSelect ? functionSelect.value : '';
            
            if (functionType === 'mosaic') {
                resultContent.innerHTML = `
                    <div style="color: var(--text-secondary);">
                        <div class="alert alert-success" style="margin-bottom: 15px;">
                            <i class="fas fa-check-circle"></i>
                            <strong>数据接边处理完成！</strong>
                        </div>
                        <p>影像拼接任务已成功完成，结果文件已生成。</p>
                        <div style="margin-top: var(--space-4);">
                            <button class="btn btn-primary" onclick="downloadResult('${taskId}')">
                                <i class="fas fa-download"></i>
                                <span>下载拼接结果</span>
                            </button>
                        </div>
                        <div style="margin-top: 10px; font-size: 0.9em; color: var(--text-muted);">
                            <i class="fas fa-info-circle"></i>
                            提示：拼接结果文件可能较大，下载时请耐心等待
                        </div>
                    </div>
                `;
            } else {
                // 保持其他功能的原始样式
                resultContent.innerHTML = `
                    <div style="color: var(--text-secondary);">
                        <p>处理完成！结果文件已生成。</p>
                        <div style="margin-top: var(--space-4);">
                            <button class="btn btn-primary" onclick="downloadResult('${taskId}')">
                                <i class="fas fa-download"></i>
                                <span>下载结果</span>
                            </button>
                        </div>
                    </div>
                `;
            }
        }

        // 下载结果
        async function downloadResult(taskId) {
            try {
                const response = await fetch(`/glff/api/preprocess/download/?task_id=${taskId}`);
                const contentType = response.headers.get('Content-Type');
                if (contentType && contentType.startsWith('application/json')) {
                    const result = await response.json();
                    if (result && result.file_path) {
                        // 自动下载真实文件

                        window.location.href = `/glff/api/preprocess/download/?task_id=${taskId}&download=1`;
                    } else {
                        // 页面展示json内容
                        showJsonOnPage(result);
                    }
                } else {
                    // 其他类型（如pdf、docx等）直接下载
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    // 获取文件名
                    let filename = 'result';
                    const contentDisposition = response.headers.get('Content-Disposition');
                    if (contentDisposition) {
                        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                        if (filenameMatch) filename = filenameMatch[1];
                    }
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                }
            } catch (error) {
                alert('下载失败: ' + error.message);
            }
        }

        // 展示json内容到页面
        function showJsonOnPage(result) {
            const resultContent = document.getElementById('resultContent');
            resultContent.innerHTML = `<pre style="max-height:400px;overflow:auto;background:#222;color:#fff;padding:1em;border-radius:6px;">${JSON.stringify(result, null, 2)}</pre>`;
        }

        // 保存到历史记录
        function saveToHistory(functionType) {
            const history = JSON.parse(localStorage.getItem('preprocessHistory') || '[]');
            const newTask = {
                function: functionType,
                timestamp: Date.now(),
                status: 'success'
            };
            
            history.unshift(newTask);
            if (history.length > 50) history.pop(); // 保留最近50条
            
            localStorage.setItem('preprocessHistory', JSON.stringify(history));
        }



        // 模态框控制
        function openModal(modalId) {
            document.getElementById(modalId).classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
            document.body.style.overflow = 'auto';
        }

        // 点击模态框外部关闭
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeModal(modal.id);
                }
            });
        });

        // 事件监听
        document.getElementById('helpBtn').addEventListener('click', () => {
            openModal('helpModal');
        });

        document.getElementById('historyBtn').addEventListener('click', () => {
            loadHistory();
            openModal('historyModal');
        });

        // 加载历史记录
        function loadHistory() {
            const history = JSON.parse(localStorage.getItem('preprocessHistory') || '[]');
            const historyList = document.getElementById('historyList');
            
            historyList.innerHTML = history.length > 0 ? 
                history.slice(0, 10).map(task => `
                    <div style="padding: 0.75rem; background: var(--bg-card); border-radius: var(--radius-lg);">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-size: 0.875rem; font-weight: 500;">${task.function}</span>
                            <span style="font-size: 0.75rem; color: var(--text-secondary);">${new Date(task.timestamp).toLocaleString()}</span>
                        </div>
                    </div>
                `).join('') : '<p style="color: var(--text-secondary); font-size: 0.875rem;">暂无历史记录</p>';
        }

        // 清空历史记录
        function clearHistory() {
            localStorage.removeItem('preprocessHistory');
            loadHistory();
        }

        // 结果展示区域集成高性能树形JSON懒加载
        function renderJsonTree(obj, container, level = 0) {
            for (let key in obj) {
                if (!Object.prototype.hasOwnProperty.call(obj, key)) continue;
                const value = obj[key];
                const div = document.createElement('div');
                div.style.marginLeft = (level * 16) + 'px';
                if (typeof value === 'object' && value !== null) {
                    const details = document.createElement('details');
                    const summary = document.createElement('summary');
                    summary.textContent = `${key}: ${Array.isArray(value) ? '[Array]' : '[Object]'}`;
                    details.appendChild(summary);
                    renderJsonTree(value, details, level + 1);
                    div.appendChild(details);
                } else {
                    div.textContent = `${key}: ${value}`;
                }
                container.appendChild(div);
            }
        }

        function renderResult(result) {
            const container = document.getElementById('resultContainer');
            container.innerHTML = '';
            if (typeof result === 'object' && result !== null) {
                renderJsonTree(result, container);
            } else {
                container.textContent = result;
            }
            // 下载按钮
            const btn = document.createElement('button');
            btn.textContent = '下载完整JSON';
            btn.onclick = function() {
                const blob = new Blob([JSON.stringify(result, null, 2)], {type: 'application/json'});
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'result.json';
                a.click();
                URL.revokeObjectURL(url);
            };
            container.appendChild(btn);
        }

        function showDownloadButton(taskId, result) {
            const container = document.getElementById('resultContainer');
            container.innerHTML = '';
            const btn = document.createElement('a');
            btn.href = `/glff/api/preprocess/download/?task_id=${taskId}`;
            btn.textContent = '下载结果';
            btn.className = 'btn btn-success';
            btn.target = '_blank';
            container.appendChild(btn);
            // 展示result字典（如有）
            if (result && typeof result === 'object') {
                const table = document.createElement('table');
                table.style.marginTop = '1em';
                table.innerHTML = '<tr><th>文件</th><th>状态</th></tr>';
                for (const [file, ok] of Object.entries(result)) {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `<td>${file}</td><td>${ok ? '成功' : '失败'}</td>`;
                    table.appendChild(tr);
                }
                container.appendChild(table);
            }
        }
    </script>
</body>
</html>
