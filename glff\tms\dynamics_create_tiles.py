import os
from PIL import Image
from osgeo import gdal, osr
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm

# 启用GDAL异常模式
gdal.UseExceptions()


# 根据您的环境设置PROJ_LIB路径，如果GDAL已正确配置，通常不需要
# os.environ['PROJ_LIB'] = r'H:\conda\miniforge3\envs\py31013\Library\share\proj'

class TiffTileCacher:
    """
    一个高效的、可扩展的TIFF瓦片缓存生成器。

    该类负责从一个源TIFF文件，为指定的目标投影和级别范围，预先生成一套瓦片缓存。
    它采用了以下优化策略：
    1.  **VRT（虚拟栅格）技术**: 使用`gdal.AutoCreateWarpedVRT`在内存中创建一个虚拟的重投影数据集，
        避免了将整个大文件读入内存，极大地降低了内存消耗，适合处理大型栅格数据。
    2.  **原生GDAL流程**: 直接使用`gdal.Translate`从VRT中裁剪并生成瓦片PNG文件，利用GDAL内部
        高效的I/O和NoData处理能力，避免了Python层面复杂的数据转换。
    3.  **真正的并行处理**: 通过`ThreadPoolExecutor`和`as_completed`模式，实现了高效的并行化，
        能够充分利用多核CPU来加速瓦片生成过程。
    4.  **精确的地理计算**: 确保瓦片的行列号范围是基于重投影后的地理范围计算的，保证了瓦片的
        空间位置准确性。
    """

    def __init__(self, tiff_path, epsg_code, min_level, max_level, tile_size=256, max_workers=8):
        """
        初始化 TiffTileCacher 实例。

        参数:
        - tiff_path (str): 源TIFF文件的路径。
        - epsg_code (int): 目标瓦片坐标系的EPSG代码 (例如 3857, 4326)。
        - min_level (int): 最小瓦片级别。
        - max_level (int): 最大瓦片级别。
        - tile_size (int): 瓦片大小（像素），默认为256。
        - max_workers (int): 用于并行处理的最大线程数，默认为8。
        """
        if not os.path.exists(tiff_path):
            raise FileNotFoundError(f"指定的TIFF文件不存在: {tiff_path}")

        self.tiff_path = tiff_path
        self.epsg_code = int(epsg_code)
        self.min_level = min_level
        self.max_level = max_level
        self.tile_size = tile_size
        self.max_workers = max_workers
        self.tiling_schemes = self._get_tiling_schemes()

    def _get_tiling_schemes(self):
        """定义不同坐标系的切片方案参数"""
        return {
            3857: {
                "origin_x": -20037508.342789244,
                "origin_y": 20037508.342789244,
                "initial_resolution": 2 * 20037508.342789244 / self.tile_size,
            },
            # EPSG:4326 和 EPSG:4490 (CGCS2000) 都使用地理坐标，切片方案类似
            # 注意：标准的WMTS服务可能会对CGCS2000有不同的原点或范围，这里采用通用的地理切片方案
            4326: {
                "origin_x": -180.0,
                "origin_y": 90.0,
                "initial_resolution": 360.0 / self.tile_size,
            },
            4490: {
                "origin_x": -180.0,
                "origin_y": 90.0,
                "initial_resolution": 360.0 / self.tile_size,
            },
        }

    def process_tiles(self):
        """
        处理TIFF文件，生成所有指定级别和范围的瓦片并缓存到磁盘。
        """
        print(f"开始处理TIFF文件: {self.tiff_path}")
        print(f"目标EPSG: {self.epsg_code}, 级别范围: {self.min_level}-{self.max_level}")

        source_ds = gdal.Open(self.tiff_path, gdal.GA_ReadOnly)
        if not source_ds:
            raise IOError(f"无法打开TIFF文件: {self.tiff_path}")

        # 获取源投影
        source_srs = osr.SpatialReference()
        source_srs.ImportFromWkt(source_ds.GetProjection())

        # 定义目标投影
        target_srs = osr.SpatialReference()
        target_srs.ImportFromEPSG(self.epsg_code)

        # 1. 核心优化：使用VRT进行虚拟重投影，避免内存爆炸
        # 这个操作几乎是瞬时的，只在内存中创建了一个描述文件
        reprojected_vrt = gdal.AutoCreateWarpedVRT(source_ds,
                                                   source_srs.ExportToWkt(),
                                                   target_srs.ExportToWkt(),
                                                   gdal.GRA_NearestNeighbour)  # 可根据需要选择重采样算法

        if not reprojected_vrt:
            raise Exception("创建重投影VRT失败")

        print("已成功创建重投影VRT，开始生成瓦片...")

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 遍历所有级别
            for level in range(self.min_level, self.max_level + 1):
                print(f"\n--- 正在处理 Level {level} ---")

                # 4. 核心修正：基于重投影后的VRT范围计算瓦片行列号
                min_col, max_col, min_row, max_row = self._get_tile_range_for_level(level, reprojected_vrt)

                if min_col > max_col or min_row > max_row:
                    print(f"在 Level {level} 没有有效的瓦片范围，跳过。")
                    continue

                total_tiles = (max_col - min_col + 1) * (max_row - min_row + 1)
                print(f"瓦片范围: 列 {min_col}-{max_col}, 行 {min_row}-{max_row} (共 {total_tiles} 个瓦片)")

                # 2. 核心修正：实现真正的并行处理
                futures = []
                for tile_row in range(min_row, max_row + 1):
                    for tile_col in range(min_col, max_col + 1):
                        future = executor.submit(self._generate_and_save_tile, reprojected_vrt, level, tile_col,
                                                 tile_row)
                        futures.append(future)

                # 使用tqdm显示进度条
                with tqdm(total=total_tiles, desc=f"Level {level}") as pbar:
                    for future in as_completed(futures):
                        try:
                            result = future.result()
                            if result:
                                pbar.set_description(f"Level {level} - Saved {os.path.basename(result)}")
                        except Exception as e:
                            print(f"一个瓦片生成失败: {e}")
                        finally:
                            pbar.update(1)

        print("\n所有瓦片处理完成！")

    def _generate_and_save_tile(self, vrt_ds, level, col, row):
        """
        生成并保存单个瓦片。此函数被并行调用。

        参数:
        - vrt_ds: 虚拟重投影数据集 (VRT)。
        - level (int): 瓦片级别。
        - col (int): 瓦片列号。
        - row (int): 瓦片行号。

        返回:
        - str 或 None: 成功则返回保存路径，否则返回None。
        """
        tile_cache_dir = os.path.join(os.path.dirname(self.tiff_path), "tile_cache", str(self.epsg_code))
        tile_cache_path = os.path.join(tile_cache_dir, str(level), str(col), f"{row}.png")

        if os.path.exists(tile_cache_path):
            return tile_cache_path  # 瓦片已存在，直接返回

        os.makedirs(os.path.dirname(tile_cache_path), exist_ok=True)

        # 计算该瓦片的地理边界
        bounds = self._get_tile_bounds(level, col, row)

        # 3. 核心优化：使用单一gdal.Translate调用完成所有操作
        # 它会从VRT中按需读取、重投影、裁剪，并直接生成透明PNG
        # nodata值会被自动处理成透明
        temp_tile_path = tile_cache_path + ".tmp"
        gdal.Translate(
            temp_tile_path,
            vrt_ds,
            format='PNG',
            projWin=bounds,  # [ulx, uly, lrx, lry]
            width=self.tile_size,
            height=self.tile_size,
            outputType=gdal.GDT_Byte,
            creationOptions=['WORLDFILE=NO']  # 不生成关联的世界文件
        )

        # 检查生成的瓦片是否完全透明（即无有效数据）
        with Image.open(temp_tile_path) as img:
            if img.mode == 'RGBA':
                # 如果alpha通道所有像素都为0，则为空瓦片
                if not np.any(np.array(img)[:, :, 3]):
                    os.remove(temp_tile_path)  # 删除空的临时瓦片
                    return None

        # 将临时文件重命名为最终文件，这是一个原子操作
        os.rename(temp_tile_path, tile_cache_path)

        return tile_cache_path

    def _get_tile_range_for_level(self, level, reprojected_ds):
        """
        根据重投影后数据集的范围和级别，计算瓦片的最大最小行列值。
        """
        scheme = self.tiling_schemes.get(self.epsg_code)
        if not scheme:
            raise ValueError(f"不支持的EPSG代码: {self.epsg_code}")

        # 获取重投影后VRT的地理范围
        gt = reprojected_ds.GetGeoTransform()
        min_x = gt[0]
        max_y = gt[3]
        max_x = min_x + gt[1] * reprojected_ds.RasterXSize
        min_y = max_y + gt[5] * reprojected_ds.RasterYSize

        resolution = scheme["initial_resolution"] / (2 ** level)

        min_col = int((min_x - scheme["origin_x"]) / (self.tile_size * resolution))
        max_col = int((max_x - scheme["origin_x"]) / (self.tile_size * resolution))

        min_row = int((scheme["origin_y"] - max_y) / (self.tile_size * resolution))
        max_row = int((scheme["origin_y"] - min_y) / (self.tile_size * resolution))

        return max(0, min_col), max_col, max(0, min_row), max_row

    def _get_tile_bounds(self, level, col, row):
        """计算指定瓦片的地理边界坐标 [ulx, uly, lrx, lry]"""
        scheme = self.tiling_schemes.get(self.epsg_code)
        if not scheme:
            raise ValueError(f"不支持的EPSG代码: {self.epsg_code}")

        resolution = scheme["initial_resolution"] / (2 ** level)

        ulx = scheme["origin_x"] + col * self.tile_size * resolution
        uly = scheme["origin_y"] - row * self.tile_size * resolution
        lrx = ulx + self.tile_size * resolution
        lry = uly - self.tile_size * resolution

        return [ulx, uly, lrx, lry]


if __name__ == "__main__":
    # --- 使用示例 ---
    try:
        # 目标坐标系: 3857 (Web Mercator), 4326 (WGS 84), 4490 (CGCS2000)
        epsg_code = 4490
        # TIFF文件路径 (请使用一个你本地存在的TIFF文件)
        tiff_path = r"G:\testcode\ceshitiff\aaa\world.tif"
        min_level = 0  # 最小瓦片级别
        max_level = 5  # 最大瓦片级别

        # 创建处理器实例
        processor = TiffTileCacher(
            tiff_path=tiff_path,
            epsg_code=epsg_code,
            min_level=min_level,
            max_level=max_level,
            max_workers=os.cpu_count() or 4  # 使用CPU核心数作为工作线程数
        )

        # 开始处理
        processor.process_tiles()

    except FileNotFoundError as e:
        print(f"错误: {e}")
    except Exception as e:
        print(f"发生了一个未预料的错误: {e}")
