# -*- coding: utf-8 -*-

"""
 (数据接边)

该版本主要增加了在脚本启动时强制设置 PROJ_LIB 环境变量的逻辑，
以解决因系统中存在多个GDAL/PROJ安装导致的环境变量冲突问题。
同时保留了 v2.1 版本中对GDAL对象生命周期的安全处理方式。
"""

import os
import sys
import site
from collections import Counter
from typing import List, Dict, Any, Optional, Tuple, Union

# os.environ['PROJ_LIB'] = r'H:\conda\miniforge3\envs\py31013\Library\share\proj'

# 现在可以安全地导入 gdal 和其他模块
from osgeo import gdal, osr
import glob
from tqdm.auto import tqdm

gdal.UseExceptions()

class MosaicError(Exception): pass


class CRSValidationError(MosaicError): pass


class InputAttributeError(MosaicError): pass


class ProcessingError(MosaicError): pass


def gdal_progress_callback(complete, message, user_data):
    pbar = user_data
    if pbar:
        current_progress = int(complete * 100)
        if current_progress > pbar.n:
            pbar.update(current_progress - pbar.n)
    return 1


class MosaicProcessor:
    def __init__(self, input_paths: List[str], output_path: str, **options: Any):
        if not input_paths: raise InputAttributeError("输入影像列表不能为空。")
        self.input_paths = input_paths
        self.output_path = output_path
        self.options = options
        self.tmp_output_path = self.output_path + ".tmp"
        self.vrt_path = os.path.splitext(self.output_path)[0] + ".vrt"
        self.final_crs: Optional[str] = None
        self.final_nodata: Optional[float] = None
        self.show_progress = self.options.get('show_progress', True)

    def process(self) -> str:
        try:
            self._validate_inputs()
            self._create_vrt()
            self._run_warp()
            self._build_overviews()
            os.rename(self.tmp_output_path, self.output_path)
            return self.output_path
        except Exception as e:
            # 清理是必须的，即使是在重新抛出异常之前
            self._cleanup()
            raise MosaicError(f"镶嵌任务执行失败: {e}") from e
        # finally 块在这里不再需要，因为无论成功还是失败，清理都在try-except中处理了
        # 如果成功，process结束前不清理。如果失败，在raise前清理。
        # 我们需要保留临时文件直到重命名，所以只有失败时才清理

    def _validate_inputs(self):
        ref_srs, ref_bands, ref_dtype = None, None, None
        self.final_crs = self.options.get('target_crs')
        for i, path in enumerate(self.input_paths):
            ds = None
            try:
                ds = gdal.Open(path, gdal.GA_ReadOnly)
                if not ds: raise InputAttributeError(f"无法打开文件: {path}")
                srs = osr.SpatialReference(ds.GetProjection())
                bands = ds.RasterCount
                if bands == 0: raise InputAttributeError(f"文件 '{os.path.basename(path)}' 没有栅格波段。")
                band1 = ds.GetRasterBand(1)
                dtype = band1.DataType
                if i == 0:
                    ref_srs, ref_bands, ref_dtype = srs, bands, dtype
                    if self.final_crs is None: self.final_crs = ref_srs.ExportToWkt()
                else:
                    if self.options.get('target_crs') is None and not srs.IsSame(ref_srs):
                        raise CRSValidationError(f"文件 '{os.path.basename(path)}' 的CRS与第一个文件不一致。")
                    if bands != ref_bands: raise InputAttributeError(
                        f"文件 '{os.path.basename(path)}' 的波段数 ({bands}) 与第一个文件 ({ref_bands}) 不一致。")
                    if dtype != ref_dtype: raise InputAttributeError(
                        f"文件 '{os.path.basename(path)}' 的数据类型与第一个文件不一致。")
            finally:
                ds = None;
                band1 = None

    def _create_vrt(self):
        nodata_opt = self.options.get('nodata_value')
        if nodata_opt == 'auto':
            nodata_values = []
            for path in self.input_paths:
                ds = None
                try:
                    ds = gdal.Open(path)
                    if ds.RasterCount > 0:
                        band = ds.GetRasterBand(1)
                        nodata = band.GetNoDataValue()
                        if nodata is not None: nodata_values.append(nodata)
                finally:
                    ds = None;
                    band = None
            if nodata_values:
                self.final_nodata = Counter(nodata_values).most_common(1)[0][0]
        elif isinstance(nodata_opt, (int, float)):
            self.final_nodata = nodata_opt
        vrt_options = gdal.BuildVRTOptions(srcNodata=self.final_nodata, VRTNodata=self.final_nodata)
        gdal.BuildVRT(self.vrt_path, self.input_paths, options=vrt_options)

    def _run_warp(self):
        warp_options_dict = {
            'format': self.options.get('output_format', 'GTiff'), 'dstSRS': self.final_crs,
            'resampleAlg': self.options.get('resample_alg', 'cubic'),
            'creationOptions': self.options.get('creationOptions', ['COMPRESS=LZW', 'TILED=YES', 'BIGTIFF=YES']),
            'warpOptions': [], 'srcNodata': self.final_nodata, 'dstNodata': self.final_nodata,
        }
        if self.options.get('blend_distance', 0) > 0:
            warp_options_dict['warpOptions'].append(f"CUTLINE_BLEND_DIST={self.options['blend_distance']}")
        if self.options.get('num_threads'):
            warp_options_dict['warpOptions'].append(f"NUM_THREADS={self.options['num_threads']}")
        if self.options.get('warp_memory_mb'):
            warp_options_dict['warpOptions'].append(f"-wm {self.options['warp_memory_mb']}")
        pbar = None
        if self.show_progress:
            pbar = tqdm(total=100, desc="镶嵌与融合", unit="%")
            warp_options_dict['callback'] = gdal_progress_callback
            warp_options_dict['callback_data'] = pbar
        gdal_warp_options = gdal.WarpOptions(**warp_options_dict)
        try:
            gdal.Warp(self.tmp_output_path, self.vrt_path, options=gdal_warp_options)
        except RuntimeError as e:
            raise ProcessingError(f"GDAL Warp操作失败: {e}") from e
        finally:
            if pbar: pbar.close()

    def _build_overviews(self):
        if self.options.get('build_overviews', False):
            ds = gdal.Open(self.tmp_output_path, gdal.GA_Update)
            ds.BuildOverviews(self.options.get('overview_resampling', 'AVERAGE'),
                              self.options.get('overview_levels', [2, 4, 8, 16, 32]))
            ds = None

    def _cleanup(self):
        for path in [self.tmp_output_path, self.vrt_path, self.vrt_path + '.ovr']:
            try:
                if os.path.exists(path): os.remove(path)
            except OSError:
                pass

    # 小修改：将清理逻辑移到 exception handling 中，以确保原子性
    def process(self) -> str:
        try:
            self._validate_inputs()
            self._create_vrt()
            self._run_warp()
            self._build_overviews()
            # 只有在所有步骤成功后才重命名
            os.rename(self.tmp_output_path, self.output_path)
            # 成功后，只清理VRT
            if os.path.exists(self.vrt_path): os.remove(self.vrt_path)
            if os.path.exists(self.vrt_path + '.ovr'): os.remove(self.vrt_path + '.ovr')
            return self.output_path
        except Exception as e:
            # 如果发生任何错误，清理所有临时文件
            self._cleanup()
            raise MosaicError(f"镶嵌任务执行失败: {e}") from e


# ... (if __name__ == '__main__') ...
if __name__ == '__main__':
    if not os.path.exists('H:\\测试数据\\testdata\\aaa'):
        os.makedirs('H:\\测试数据\\testdata\\aaa')
        print("创建了 'H:\\测试数据\\testdata\\aaa' 文件夹，请放入你的TIF文件进行测试。")
    input_files = glob.glob(os.path.join('H:\\测试数据\\testdata\\aaa', '*.tif'))
    if not input_files:
        print("警告: 'H:\\测试数据\\testdata\\aaa' 文件夹中没有找到TIF文件，无法运行示例。")
    else:
        print("\n--- 场景1: 执行成功的镶嵌任务 ---")
        output_file_success = 'H:\\测试数据\\testdata\\aaa\\mosaic_api_success.tif'
        config_success = {
            "target_crs": "EPSG:3857", "blend_distance": 25, "nodata_value": "auto",
            "num_threads": "ALL_CPUS", "build_overviews": True, "show_progress": True
        }
        try:
            if os.path.exists(output_file_success): os.remove(output_file_success)
            if os.path.exists(output_file_success + '.ovr'): os.remove(output_file_success + '.ovr')
            processor = MosaicProcessor(input_files, output_file_success, **config_success)
            result_path = processor.process()
            print(f"\n[成功] API调用成功，结果已生成: {result_path}")
            if os.path.exists(result_path): print(f"验证：文件 '{result_path}' 已创建。")
            if os.path.exists(result_path + '.ovr'): print(f"验证：金字塔文件 '{result_path}.ovr' 已创建。")
        except MosaicError as e:
            print(f"\n[失败] API调用失败: {e}")

        print("\n\n--- 场景2: 模拟因CRS不匹配导致的预期失败 ---")
        output_file_fail = 'H:\\测试数据\\testdata\\aaa\\mosaic_api_fail.tif'
        config_fail = {"target_crs": None, "show_progress": False}
        try:
            processor_fail = MosaicProcessor(input_files, output_file_fail, **config_fail)
            processor_fail.process()
        except CRSValidationError as e:
            print(f"\n[成功捕获预期的错误] API按预期失败，并抛出CRSValidationError:")
            print(f"  错误信息: {e}")
            if not os.path.exists(output_file_fail) and not os.path.exists(output_file_fail + ".tmp"):
                print("验证：没有留下任何输出文件或临时文件，清理成功。")
        except MosaicError as e:
            print(f"\n[信息] 任务未因CRSValidationError失败，可能是因为所有测试影像CRS相同。错误: {e}")

