from django.http import JsonResponse, HttpResponse, FileResponse
from django.views.decorators.csrf import csrf_exempt
from django.views import View
from django.utils.decorators import method_decorator
from django.conf import settings
import os
import uuid
import json
import traceback
import time
import threading
from concurrent.futures import ThreadPoolExecutor
import shutil
from pathlib import Path
import logging
import zipfile
import tempfile

from shapely.geometry import mapping

# 导入底层处理模块
from glff.normalization.data_pre_processing.file_processing import file_extract, file_preprocess
from glff.normalization.data_pre_processing.reproject import Reprojector, transform_single_coord, convert_geojson_data, convert_geojson_shp
from pyproj import Transformer
from glff.normalization.data_pre_processing.geo_info import shp_info_extract, tif_info_extract
from glff.normalization.data_pre_processing.image_processing import preprocess_image
from glff.normalization.data_pre_processing.mosaic import geo_mosaic_tool
import glob

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建必要的目录
UPLOAD_DIR = os.path.join(settings.BASE_DIR, 'media', 'preprocess', 'uploads')
RESULT_DIR = os.path.join(settings.BASE_DIR, 'media', 'preprocess', 'results')
TEMP_DIR = os.path.join(settings.BASE_DIR, 'media', 'preprocess', 'temp')

def ensure_directories_exist():
    """确保所有必要的目录都存在"""
    directories = [UPLOAD_DIR, RESULT_DIR, TEMP_DIR]
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            logger.info(f"确保目录存在: {directory}")
        except Exception as e:
            logger.error(f"创建目录失败 {directory}: {e}")
            raise

# 任务状态管理
task_status = {}
task_lock = threading.Lock()

def update_task_status(task_id, status, result=None, error=None):
    """更新任务状态"""
    with task_lock:
        task_status[task_id] = {
            'status': status,
            'result': result,
            'error': error,
            'timestamp': time.time()
        }
        logger.info(f"任务 {task_id} 状态更新: {status}")

def get_task_status(task_id):
    """获取任务状态"""
    with task_lock:
        return task_status.get(task_id, {'status': 'not_found'})

def generate_unique_filename(original_name, directory):
    """生成唯一文件名"""
    name, ext = os.path.splitext(original_name)
    counter = 1
    unique_name = original_name
    
    while os.path.exists(os.path.join(directory, unique_name)):
        unique_name = f"{name}_{counter}{ext}"
        counter += 1
    
    return unique_name

def extract_shp_from_zip(zip_path):
    temp_dir = os.path.join(TEMP_DIR, f"shp_{uuid.uuid4().hex}")
    os.makedirs(temp_dir, exist_ok=True)
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(temp_dir)
        # shp_files = glob.glob(os.path.join(temp_dir, '*.shp'))
        shp_files = glob.glob(os.path.join(temp_dir, '*', '*.shp'))
        if not shp_files:
            raise FileNotFoundError('zip包中未找到shp文件')
        return shp_files[0], temp_dir
    except Exception as e:
        shutil.rmtree(temp_dir, ignore_errors=True)
        raise e

@method_decorator(csrf_exempt, name='dispatch')
class PreprocessUploadView(View):
    """文件上传API"""
    def post(self, request):
        try:
            # 确保目录存在
            ensure_directories_exist()
            
            file = request.FILES.get('file')
            if not file:
                return JsonResponse({'error': '请选择要上传的文件'}, status=400)
            
            logger.info(f"开始上传文件: {file.name}, 大小: {file.size} bytes")
            
            # 检查文件大小
            if file.size > settings.MAX_FILE_SIZE:
                return JsonResponse({
                    'error': f'文件大小超过限制，最大允许 {settings.MAX_FILE_SIZE // 1024 // 1024}MB'
                }, status=400)
            
            # 检查文件类型
            file_ext = os.path.splitext(file.name)[1].lower()
            if file_ext not in settings.ALLOWED_FILE_TYPES:
                return JsonResponse({
                    'error': f'不支持的文件类型: {file_ext}，支持的类型: {", ".join(settings.ALLOWED_FILE_TYPES)}'
                }, status=400)
            
            # 确保上传目录存在
            os.makedirs(UPLOAD_DIR, exist_ok=True)
            
            # 生成唯一文件名和文件ID
            unique_filename = generate_unique_filename(file.name, UPLOAD_DIR)
            file_id = str(uuid.uuid4())
            file_path = os.path.join(UPLOAD_DIR, f"{file_id}_{unique_filename}")
            
            logger.info(f"文件路径: {file_path}")
            
            # 保存文件
            with open(file_path, 'wb+') as destination:
                for chunk in file.chunks():
                    destination.write(chunk)
            
            # 验证文件是否成功保存
            if not os.path.exists(file_path):
                raise Exception("文件保存失败")
            
            logger.info(f"文件上传成功: {file_path}")
            
            return JsonResponse({
                'file_id': file_id,
                'filename': f"{file_id}_{unique_filename}",  # 返回完整的文件名（包含file_id）
                'original_name': file.name,
                'file_path': file_path,
                'file_size': file.size
            })
        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            return JsonResponse({'error': f'文件上传失败: {str(e)}'}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class PreprocessRunView(View):
    """预处理任务执行API"""
    def post(self, request):
        try:
            # 确保目录存在
            ensure_directories_exist()
            
            data = json.loads(request.body.decode('utf-8'))
            if not data:
                return JsonResponse({'error': '无效的请求数据'}, status=400)
            
            task_id = str(uuid.uuid4())
            
            logger.info(f"开始处理任务 {task_id}: {data}")
            
            # 更新任务状态为开始
            update_task_status(task_id, 'processing')
            
            # 启动异步处理
            thread = threading.Thread(
                target=self._process_task,
                args=(task_id, data)
            )
            thread.daemon = True
            thread.start()
            
            return JsonResponse({'task_id': task_id, 'status': 'processing'})
        except Exception as e:
            logger.error(f"任务启动失败: {str(e)}")
            return JsonResponse({'error': str(e)}, status=500)
    
    def _process_task(self, task_id, data):
        """处理任务"""
        try:
            # 更新任务状态为处理中
            update_task_status(task_id, 'processing')
            
            function_type = data.get('function_type')
            params = data.get('params', {})
            
            logger.info(f"开始处理任务 {task_id}: {function_type}")
            
            # 需要文件的功能类型
            file_required_types = [
                'text_extract', 'file_preprocess', 'reproject', 'batch_reproject',
                'shp_transform', 'shp_info', 'tif_info', 'image_process', 'mosaic'
            ]
            
            input_path = None
            if function_type in file_required_types:
                file_id = data.get('file_id')
                filename = data.get('filename')
                file_path = data.get('file_path')
                
                logger.info(f"文件信息 - file_id: {file_id}, filename: {filename}, file_path: {file_path}")
                
                # 优先用file_path
                if file_path and os.path.exists(file_path):
                    input_path = file_path
                    logger.info(f"使用file_path: {input_path}")
                elif filename:
                    input_path = os.path.join(UPLOAD_DIR, filename)
                    logger.info(f"使用filename构建路径: {input_path}")
                else:
                    logger.error("未上传文件或文件名缺失")
                    update_task_status(task_id, 'error', error='未上传文件或文件名缺失')
                    return
                
                logger.info(f"查找文件: {input_path}")
                if not os.path.exists(input_path):
                    logger.error(f"文件不存在: {input_path}")
                    update_task_status(task_id, 'error', error=f'文件不存在: {input_path}')
                    return
            
            # 根据功能类型调用相应的处理函数
            if function_type == 'text_extract':
                result = self._handle_text_extract(input_path, params)
            elif function_type == 'file_preprocess':
                result = self._handle_file_preprocess(input_path, params)
            elif function_type == 'reproject':
                result = self._handle_reproject(input_path, params)
            elif function_type == 'batch_reproject':
                result = self._handle_batch_reproject(input_path, params)
            elif function_type == 'single_coord_transform':
                result = self._handle_single_coord_transform(params)
            elif function_type == 'geojson_transform':
                result = self._handle_geojson_transform(params)
            elif function_type == 'shp_transform':
                result = self._handle_shp_transform(input_path, params)
            elif function_type == 'shp_info':
                result = self._handle_shp_info(input_path, params)
            elif function_type == 'tif_info':
                result = self._handle_tif_info(input_path, params)
            elif function_type == 'image_process':
                result = self._handle_image_process(input_path, params)
            elif function_type == 'mosaic':
                # 为mosaic功能传递task_id
                result = self._handle_mosaic_with_progress(task_id, input_path, params)
            else:
                result = {'status': 'error', 'message': f'不支持的功能类型: {function_type}'}
            
            # 保存结果到文件
            result_file = os.path.join(RESULT_DIR, f"{task_id}_result.json")
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            # 更新任务状态为完成
            if result.get('status') == 'success':
                update_task_status(task_id, 'completed', result)
                logger.info(f"任务 {task_id} 处理完成")
            else:
                update_task_status(task_id, 'error', error=result.get('message', '处理失败'))
                logger.error(f"任务 {task_id} 处理失败: {result.get('message')}")
                
        except Exception as e:
            logger.error(f"任务 {task_id} 处理异常: {str(e)}")
            update_task_status(task_id, 'error', error=str(e))
    
    def _handle_text_extract(self, input_path, params):
        """处理文本提取"""
        try:
            logger.info(f"开始文本提取: {input_path}")
            result = file_extract.process_file(input_path)
            return {
                'status': 'success',
                'result': result,
                'input_file': input_path
            }
        except Exception as e:
            logger.error(f"文本提取失败: {str(e)}")
            return {'status': 'error', 'message': str(e)}
    
    def _handle_file_preprocess(self, input_path, params):
        """处理文档格式转换"""
        try:
            logger.info(f"开始文档预处理: {input_path}")
            output_dir = params.get('output_dir', RESULT_DIR)
            converter = file_preprocess.LibreOfficeConverter()
            output_file = converter.convert_to_pdf(input_path, output_dir)
            return {
                'status': 'success',
                'result': str(output_file),
                'input_file': input_path,
                'output_file': str(output_file)
            }
        except Exception as e:
            logger.error(f"文档预处理失败: {str(e)}")
            return {'status': 'error', 'message': str(e)}
    
    def _handle_reproject(self, input_path, params):
        """处理坐标重投影"""
        try:
            logger.info(f"开始坐标重投影: {input_path}")
            
            # 获取参数
            src_crs = params.get('src_crs', 'EPSG:4326')
            dst_crs = params.get('dst_crs', 'EPSG:3857')
            output_path = params.get('output_path')
            
            if not output_path:
                name, ext = os.path.splitext(os.path.basename(input_path))
                output_path = os.path.join(RESULT_DIR, f"{name}_reprojected{ext}")
            
            # 根据文件类型处理
            file_ext = input_path.lower()
            
            if file_ext.endswith('.geojson'):
                from glff.normalization.data_pre_processing.reproject.vector_reproject import convert_geojson_file
                result = convert_geojson_file(input_path, output_path, src_crs, dst_crs)
                return {'status': 'success', 'output_file': output_path, 'result': result}
                
            elif file_ext.endswith('.zip'):
                # 处理ZIP包中的Shapefile
                shp_path, temp_dir = extract_shp_from_zip(input_path)
                from glff.normalization.data_pre_processing.reproject.vector_reproject import convert_geojson_shp
                name, ext = os.path.splitext(os.path.basename(shp_path))
                out_dir = os.path.join(RESULT_DIR, 'out')
                os.makedirs(out_dir, exist_ok=True)
                output_shp_path = os.path.join(out_dir, f"{name}_out{dst_crs+ext}")
                result = convert_geojson_shp(shp_path, output_shp_path, src_crs, dst_crs)
                # 打包 out 目录下所有 xxx_out.* 文件为 zip
                output_files = []
                for ext in ['.shp', '.dbf', '.shx', '.prj', '.cpg']:
                    f = os.path.join(out_dir, f"{name}_out{ext}")
                    if os.path.exists(f):
                        output_files.append(f)
                zip_name = f"{name}_out{dst_crs}.zip"
                zip_path = os.path.join(RESULT_DIR, zip_name)
                with zipfile.ZipFile(zip_path, 'w') as zipf:
                    for f in output_files:
                        zipf.write(f, os.path.basename(f))
                shutil.rmtree(temp_dir, ignore_errors=True)
                return {'status': 'success', 'output_file': zip_path, 'result': result}
                
            elif file_ext.endswith('.shp'):
                # 直接处理单独的Shapefile
                from glff.normalization.data_pre_processing.reproject.vector_reproject import convert_geojson_shp
                result = convert_geojson_shp(input_path, output_path, src_crs, dst_crs)
                return {'status': 'success', 'output_file': output_path, 'result': result}
                
            elif file_ext.endswith(('.tif', '.tiff')):
                # 处理栅格文件
                from glff.normalization.data_pre_processing.reproject import Reprojector
                reprojector = Reprojector()
                success = reprojector.transform_coordinates(
                    input_path, output_path, 
                    src_crs=src_crs, dst_crs=dst_crs
                )
                if success:
                    return {'status': 'success', 'output_file': output_path, 'result': 'success'}
                else:
                    return {'status': 'error', 'message': '栅格重投影失败'}
            else:
                return {
                    'status': 'error', 
                    'message': '不支持的文件格式。支持：GeoJSON(.geojson)、Shapefile(.shp或.zip)、栅格(.tif/.tiff)'
                }
                
        except Exception as e:
            logger.error(f"坐标重投影失败: {str(e)}")
            return {'status': 'error', 'message': f'坐标重投影失败: {str(e)}'}

    def _handle_batch_reproject(self, input_path, params):
        try:
            logger.info(f"开始批量重投影: {input_path}")
            if not input_path.endswith('.zip'):
                return {'status': 'error', 'message': '只支持TIF ZIP包'}
            temp_dir = os.path.join(TEMP_DIR, f"tif_{uuid.uuid4().hex}")
            os.makedirs(temp_dir, exist_ok=True)
            with zipfile.ZipFile(input_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)
            tif_files = glob.glob(os.path.join(temp_dir, '**', '*.tif'), recursive=True)
            if not tif_files:
                shutil.rmtree(temp_dir, ignore_errors=True)
                return {'status': 'error', 'message': 'zip包中未找到tif文件'}
            output_dir = os.path.join(RESULT_DIR, f"batch_{uuid.uuid4().hex}")
            os.makedirs(output_dir, exist_ok=True)
            src_crs = params.get('src_crs', 'EPSG:4326')
            dst_crs = params.get('dst_crs', 'EPSG:3857')
            reprojector = Reprojector()
            result = reprojector.batch_transform_coordinates(
                tif_files, output_dir, src_crs=src_crs, dst_crs=dst_crs
            )
            # 只打包result中value为True且存在的tif
            result_zip = os.path.join(RESULT_DIR, f"batch_reproject_result_{uuid.uuid4().hex}.zip")
            with zipfile.ZipFile(result_zip, 'w') as zipf:
                for tif_path, ok in result.items():
                    if ok and os.path.exists(tif_path):
                        zipf.write(tif_path, os.path.basename(tif_path))
            shutil.rmtree(temp_dir, ignore_errors=True)
            shutil.rmtree(output_dir, ignore_errors=True)
            return {'status': 'success', 'output_file': result_zip, 'result': result}
        except Exception as e:
            logger.error(f"批量重投影失败: {str(e)}")
            return {'status': 'error', 'message': str(e)}
    
    def _handle_single_coord_transform(self, params):
        """处理单点坐标转换"""
        try:
            logger.info("开始单点坐标转换")
            coord_str = params.get('coord')
            src_crs = params.get('src_crs', 'EPSG:32648')
            dst_crs = params.get('dst_crs', 'EPSG:4326')
            # 解析坐标字符串为坐标列表
            if isinstance(coord_str, str):
                try:
                    # 处理 "112,23" 格式的坐标字符串
                    coord_parts = coord_str.split(',')
                    if len(coord_parts) == 2:
                        coord = [float(coord_parts[0].strip()), float(coord_parts[1].strip())]
                    else:
                        raise ValueError(f"坐标格式错误: {coord_str}，应为 '经度,纬度' 格式")
                except ValueError as e:
                    return {'status': 'error', 'message': f'坐标解析失败: {str(e)}'}
            elif isinstance(coord_str, list):
                coord = coord_str
            else:
                return {'status': 'error', 'message': f'坐标格式错误: {coord_str}'}
            # 创建坐标转换器
            transformer = Transformer.from_crs(src_crs, dst_crs, always_xy=True)
            # 执行坐标转换（只传两个参数）
            result = transform_single_coord(coord, transformer)
            return {
                'status': 'success',
                'result': result,
                'input_coord': coord_str,
                'parsed_coord': coord,
                'src_crs': src_crs,
                'dst_crs': dst_crs
            }
        except Exception as e:
            logger.error(f"单点坐标转换失败: {str(e)}")
            return {'status': 'error', 'message': str(e)}
    
    def _handle_geojson_transform(self, params):
        """处理GeoJSON坐标转换"""
        try:
            logger.info("开始GeoJSON坐标转换")
            geojson = params.get('geojson')
            src_crs = params.get('src_crs', 'EPSG:32648')
            dst_crs = params.get('dst_crs', 'EPSG:4326')
            
            result = convert_geojson_data(geojson, src_crs, dst_crs)
            
            return {
                'status': 'success',
                'result': result,
                'src_crs': src_crs,
                'dst_crs': dst_crs
            }
        except Exception as e:
            logger.error(f"GeoJSON坐标转换失败: {str(e)}")
            return {'status': 'error', 'message': str(e)}
    
    def _handle_shp_transform(self, input_path, params):
        """处理Shapefile坐标转换"""
        try:
            shp_path, temp_dir = extract_shp_from_zip(input_path)
            logger.info(f"开始Shapefile转换: {shp_path}")
            # 输出路径为 RESULT_DIR/out/xxx_out.shp
            name, ext = os.path.splitext(os.path.basename(shp_path))
            out_dir = os.path.join(RESULT_DIR, 'out')
            os.makedirs(out_dir, exist_ok=True)
            output_shp_path = os.path.join(out_dir, f"{name}_out{ext}")
            src_crs = params.get('src_crs', 'EPSG:4326')
            dst_crs = params.get('dst_crs', 'EPSG:3857')
            # 坐标转换
            result = convert_geojson_shp(shp_path, output_shp_path, src_crs, dst_crs)
            # 打包 out 目录下所有 xxx_out.* 文件为 zip
            output_files = []
            for ext in ['.shp', '.dbf', '.shx', '.prj', '.cpg']:
                f = os.path.join(out_dir, f"{name}_out{ext}")
                if os.path.exists(f):
                    output_files.append(f)
            zip_name = f"{name}_out.zip"
            zip_path = os.path.join(RESULT_DIR, zip_name)
            with zipfile.ZipFile(zip_path, 'w') as zipf:
                for f in output_files:
                    zipf.write(f, os.path.basename(f))
            shutil.rmtree(temp_dir, ignore_errors=True)
            return {
                'status': 'success',
                'result': result,
                'input_file': shp_path,
                'output_file': zip_path,
                'src_crs': src_crs,
                'dst_crs': dst_crs
            }
        except Exception as e:
            logger.error(f"Shapefile转换失败: {str(e)}")
            return {'status': 'error', 'message': str(e)}
    
    def _handle_shp_info(self, input_path, params):
        """处理Shapefile信息提取"""
        try:
            shp_path, temp_dir = extract_shp_from_zip(input_path)
            logger.info(f"开始Shapefile信息提取: {shp_path}")
            info_type = params.get('info_type', 'all')
            max_features = params.get('max_features', 10000)
            if isinstance(max_features, str):
                try:
                    max_features = int(max_features)
                except Exception:
                    max_features = 10000
            
            # 添加对return_all_features参数的支持
            return_all_features = params.get('return_all_features', False)
            if isinstance(return_all_features, str):
                return_all_features = return_all_features.lower() in ['true', '1', 'yes']
            
            result = shp_info_extract.extract_shp_info(
                shp_path, 
                max_features=max_features, 
                convex_hull_only=True,
                return_all_features=return_all_features
            )
            return {
                'status': 'success',
                'result': result,
                'input_file': shp_path,
                'info_type': info_type
            }
        except Exception as e:
            logger.error(f"Shapefile信息提取失败: {str(e)}")
            return {'status': 'error', 'message': str(e)}
        finally:
            if 'temp_dir' in locals():
                shutil.rmtree(temp_dir)
    
    def _handle_tif_info(self, input_path, params):
        """处理TIF信息提取"""
        try:
            logger.info(f"开始TIF信息提取: {input_path}")
            
            # 生成输出文件路径
            output_dir = os.path.join(RESULT_DIR, 'tif_info')
            os.makedirs(output_dir, exist_ok=True)
            output_bounds_geojson = os.path.join(output_dir, 'output_bounds.geojson')
            output_actual_geojson = os.path.join(output_dir, 'output_actual_polygon.geojson')
            
            # 调用正确的方法
            result = tif_info_extract.read_tif_info(
                input_path, 
                output_bounds_geojson,
                output_actual_geojson
            )
            
            return {
                'status': 'success',
                'result': result,
                'input_file': input_path,
                'output_bounds': output_bounds_geojson,
                'output_actual': output_actual_geojson
            }
        except Exception as e:
            logger.error(f"TIF信息提取失败: {str(e)}")
            return {'status': 'error', 'message': str(e)}
    
    def _handle_image_process(self, input_path, params):
        """处理影像预处理，参数严格按 test_api 用法，结果自动打包zip"""
        import zipfile
        try:
            logger.info(f"开始影像处理: {input_path}")
            denoise = params.get('denoise', False)
            build_pyramids = params.get('build_pyramids', True)
            create_thumbnail = params.get('create_thumbnail', True)
            resample_resolution = params.get('resample_resolution')
            if isinstance(resample_resolution, str):
                try:
                    resample_resolution = [float(x) for x in resample_resolution.split(',')]
                except Exception:
                    resample_resolution = None
            processor = preprocess_image.GeospatialProcessor(
                denoise=denoise,
                build_pyramids=build_pyramids,
                resample_resolution=resample_resolution
            )
            result = processor.process_file(
                input_path,
                create_thumbnail=create_thumbnail
            )
            # 自动打包 processed_path 和 thumbnail_path
            processed_path = result.get('processed_path')
            thumbnail_path = result.get('thumbnail_path')
            zip_name = f"processed_result_{uuid.uuid4().hex}.zip"
            zip_path = os.path.join(RESULT_DIR, zip_name)
            with zipfile.ZipFile(zip_path, 'w') as zipf:
                if processed_path and os.path.exists(processed_path):
                    zipf.write(processed_path, os.path.basename(processed_path))
                if thumbnail_path and os.path.exists(thumbnail_path):
                    zipf.write(thumbnail_path, os.path.basename(thumbnail_path))
            return {
                'status': 'success',
                'result': {
                    'zip_file': zip_path,
                    'message': result.get('message', '处理成功完成。'),
                    'post_check_passed': result.get('post_check_passed', True)
                },
                'input_file': input_path,
                'denoise': denoise,
                'build_pyramids': build_pyramids,
                'create_thumbnail': create_thumbnail,
                'resample_resolution': resample_resolution
            }
        except Exception as e:
            logger.error(f"影像处理失败: {str(e)}")
            return {'status': 'error', 'message': str(e)}
    
    def _handle_mosaic(self, input_path, params):
        """处理影像拼接（仅支持上传zip包，自动解压并拼接所有tif）"""
        try:
            # 安全检查input_path
            if input_path is None:
                logger.error("input_path为None")
                return {'status': 'error', 'message': '输入文件路径为空'}
            
            if not os.path.exists(input_path):
                logger.error(f"文件不存在: {input_path}")
                return {'status': 'error', 'message': f'文件不存在: {input_path}'}
            
            logger.info(f"开始影像拼接: {input_path}")
            
            # 检查文件大小
            file_size_mb = os.path.getsize(input_path) / (1024 * 1024)
            logger.info(f"上传文件大小: {file_size_mb:.2f} MB")
            
            # 仅支持zip包，解压后自动查找所有tif
            if not input_path.endswith('.zip'):
                return {'status': 'error', 'message': '请上传包含一组TIF的zip包'}
            
            # 大文件警告
            if file_size_mb > 100:  # 超过100MB
                logger.warning(f"大文件警告: {file_size_mb:.2f} MB，处理时间可能较长")
            
            temp_dir = os.path.join(TEMP_DIR, f"mosaic_{uuid.uuid4().hex}")
            os.makedirs(temp_dir, exist_ok=True)
            
            # 解压文件
            logger.info("开始解压文件...")
            with zipfile.ZipFile(input_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)
            
            # 查找TIF文件
            tif_files = glob.glob(os.path.join(temp_dir, '**', '*.tif'), recursive=True)
            if not tif_files:
                shutil.rmtree(temp_dir, ignore_errors=True)
                return {'status': 'error', 'message': 'zip包中未找到tif文件'}
            
            logger.info(f"找到 {len(tif_files)} 个TIF文件")
            
            # 输出路径自动生成
            output_path = os.path.join(RESULT_DIR, f"mosaic_result_{uuid.uuid4().hex}.tif")
            
            # 获取options参数
            options = params.get('options', {})
            
            # 为mosaic添加进度显示
            options['show_progress'] = True
            
            logger.info("开始数据接边处理...")
            # 创建处理器并执行
            processor = geo_mosaic_tool.MosaicProcessor(tif_files, output_path, **options)
            result = processor.process()
            
            # 清理临时文件
            shutil.rmtree(temp_dir, ignore_errors=True)
            
            logger.info(f"数据接边完成: {result}")
            return {
                'status': 'success',
                'result': result,
                'input_files': tif_files,
                'output_file': output_path,
                'file_size_mb': round(file_size_mb, 2),
                'tif_count': len(tif_files)
            }
        except Exception as e:
            logger.error(f"影像拼接失败: {str(e)}")
            # 清理临时文件
            if 'temp_dir' in locals():
                shutil.rmtree(temp_dir, ignore_errors=True)
            return {'status': 'error', 'message': str(e)}

    def _handle_mosaic_with_progress(self, task_id, input_path, params):
        """处理影像拼接（带进度更新）"""
        try:
            # 安全检查input_path
            if input_path is None:
                logger.error("input_path为None")
                return {'status': 'error', 'message': '输入文件路径为空'}
            
            if not os.path.exists(input_path):
                logger.error(f"文件不存在: {input_path}")
                return {'status': 'error', 'message': f'文件不存在: {input_path}'}
            
            logger.info(f"开始影像拼接: {input_path}")
            
            # 检查文件大小
            file_size_mb = os.path.getsize(input_path) / (1024 * 1024)
            logger.info(f"上传文件大小: {file_size_mb:.2f} MB")
            
            # 仅支持zip包，解压后自动查找所有tif
            if not input_path.endswith('.zip'):
                return {'status': 'error', 'message': '请上传包含一组TIF的zip包'}
            
            # 大文件警告
            if file_size_mb > 100:  # 超过100MB
                logger.warning(f"大文件警告: {file_size_mb:.2f} MB，处理时间可能较长")
            
            temp_dir = os.path.join(TEMP_DIR, f"mosaic_{uuid.uuid4().hex}")
            os.makedirs(temp_dir, exist_ok=True)
            
            # 更新进度：开始解压 (10%)
            update_task_status(task_id, 'processing', {'progress': 10, 'stage': '解压文件'})
            
            # 解压文件
            logger.info("开始解压文件...")
            with zipfile.ZipFile(input_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)
            
            # 查找TIF文件
            tif_files = glob.glob(os.path.join(temp_dir, '**', '*.tif'), recursive=True)
            if not tif_files:
                shutil.rmtree(temp_dir, ignore_errors=True)
                return {'status': 'error', 'message': 'zip包中未找到tif文件'}
            
            logger.info(f"找到 {len(tif_files)} 个TIF文件")
            
            # 更新进度：验证数据 (30%)
            update_task_status(task_id, 'processing', {'progress': 30, 'stage': '验证影像数据'})
            
            # 输出路径自动生成
            output_path = os.path.join(RESULT_DIR, f"mosaic_result_{uuid.uuid4().hex}.tif")
            
            # 获取options参数
            options = params.get('options', {})
            
            # 为mosaic添加进度显示
            options['show_progress'] = True
            
            # 更新进度：开始拼接 (60%)
            update_task_status(task_id, 'processing', {'progress': 60, 'stage': '进行影像拼接'})
            
            logger.info("开始数据接边处理...")
            # 创建处理器并执行
            processor = geo_mosaic_tool.MosaicProcessor(tif_files, output_path, **options)
            result = processor.process()
            
            # 更新进度：生成结果 (90%)
            update_task_status(task_id, 'processing', {'progress': 90, 'stage': '生成最终结果'})
            
            # 清理临时文件
            shutil.rmtree(temp_dir, ignore_errors=True)
            
            logger.info(f"数据接边完成: {result}")
            
            # 添加调试信息
            logger.info(f"任务 {task_id} 最终状态: completed")
            logger.info(f"结果: {result}")
            
            return {
                'status': 'success',
                'result': result,
                'input_files': tif_files,
                'output_file': output_path,
                'file_size_mb': round(file_size_mb, 2),
                'tif_count': len(tif_files)
            }
        except Exception as e:
            logger.error(f"影像拼接失败: {str(e)}")
            # 清理临时文件
            if 'temp_dir' in locals():
                shutil.rmtree(temp_dir, ignore_errors=True)
            return {'status': 'error', 'message': str(e)}

@method_decorator(csrf_exempt, name='dispatch')
class PreprocessStatusView(View):
    """任务状态查询API"""
    def get(self, request):
        task_id = request.GET.get('task_id')
        if not task_id:
            return JsonResponse({'error': 'task_id is required'}, status=400)
        
        status = get_task_status(task_id)
        logger.info(f"查询任务状态 {task_id}: {status}")
        
        # 如果是mosaic功能且正在处理中，返回进度信息
        if status.get('status') == 'processing' and status.get('result'):
            progress_info = status.get('result')
            if isinstance(progress_info, dict) and 'progress' in progress_info:
                return JsonResponse({
                    'status': 'processing',
                    'progress': progress_info.get('progress', 0),
                    'stage': progress_info.get('stage', '处理中'),
                    'timestamp': status.get('timestamp')
                })
        
        return JsonResponse(status)

def _find_download_file(result_content):
    """递归查找 zip_file、output_file、file_path"""
    if isinstance(result_content, dict):
        # 优先 zip_file
        for key in ["zip_file", "output_file", "file_path"]:
            path = result_content.get(key)
            if path and os.path.exists(path):
                return path
        # 递归查找嵌套 result 字段，但排除 input_file
        for key, v in result_content.items():
            if key != "input_file":  # 排除输入文件路径
                found = _find_download_file(v)
                if found:
                    return found
    elif isinstance(result_content, list):
        for item in result_content:
            found = _find_download_file(item)
            if found:
                return found
    elif isinstance(result_content, str):
        # 处理字符串类型的结果（如mosaic功能）
        if result_content and os.path.exists(result_content):
            return result_content
    return None

@method_decorator(csrf_exempt, name='dispatch')
class PreprocessDownloadView(View):
    """结果下载API"""
    def get(self, request):
        try:
            # 确保目录存在
            ensure_directories_exist()
            
            task_id = request.GET.get('task_id')
            download = request.GET.get('download')
            if not task_id:
                return JsonResponse({'error': 'task_id is required'}, status=400)
            status = get_task_status(task_id)
            if status.get('status') != 'completed':
                return JsonResponse({'error': 'Task not completed'}, status=404)
            result_file = os.path.join(RESULT_DIR, f"{task_id}_result.json")
            if not os.path.exists(result_file):
                return JsonResponse({'error': 'Result file not found'}, status=404)
            try:
                with open(result_file, 'r', encoding='utf-8') as f:
                    result_data = json.load(f)
                result_content = result_data.get('result')
                file_path = _find_download_file(result_content)
                if file_path:
                    filename = os.path.basename(file_path)
                    content_type, _ = mimetypes.guess_type(filename)
                    response = FileResponse(open(file_path, 'rb'), content_type=content_type or 'application/octet-stream')
                    response['Content-Disposition'] = f'attachment; filename="{filename}"'
                    return response
                return JsonResponse(result_content, json_dumps_params={'ensure_ascii': False, 'indent': 2})
            except Exception as e:
                logger.error(f"下载失败: {str(e)}")
                return JsonResponse({'error': f'Download failed: {str(e)}'}, status=500)
        except Exception as e:
            logger.error(f"目录创建失败: {str(e)}")
            return JsonResponse({'error': f'Directory creation failed: {str(e)}'}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class PreprocessResultView(View):
    """结果查询API"""
    def get(self, request):
        task_id = request.GET.get('task_id')
        if not task_id:
            return JsonResponse({'error': 'task_id is required'}, status=400)
        
        status = get_task_status(task_id)
        return JsonResponse(status)
