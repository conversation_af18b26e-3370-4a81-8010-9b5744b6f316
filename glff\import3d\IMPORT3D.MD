# 地理数据处理工具

本工具用于将地理数据（GeoJSON 格式）导入 PostgreSQL/PostGIS 数据库，并进行字段计算和结果导出。工具基于 Python 和 SQLAlchemy 实现，支持进度跟踪、数据表检查和数据分布分析。

## 功能概述

1. **数据导入**：将 GeoJSON 文件导入 PostgreSQL/PostGIS 数据库。
2. **字段计算**：对导入的数据进行字段计算（如几何字段转换）。
3. **数据导出**：将处理后的数据导出为 GeoJSON 文件。
4. **进度跟踪**：实时跟踪任务进度。
5. **数据表检查**：支持检查子表和数据分布。

## 类说明

### `ProgressTracker`

用于跟踪和记录任务的进度。

#### 属性
- `current` (int): 当前进度。
- `total` (int): 总进度。
- `stage` (str): 当前阶段名称。

#### 方法
- `update(current: int, total: int, stage: str)`: 更新进度信息。

### `GeoDataProcessor`

用于处理地理数据的核心类。

#### 方法
- `import_to_postgis(gdf: gpd.GeoDataFrame, engine, table_name: str, srid: int, append: bool = False)`: 将 GeoDataFrame 导入 PostGIS 数据库。
- `update_fields(engine, table_name: str, srid_local: int, srid_wgs84: int)`: 对表中的字段进行计算和更新。
- `export_geojson(engine, table_name: str, output_geojson: str, srid: int)`: 将表中的数据导出为 GeoJSON 文件。

## 调用方法

### 1. 配置文件
在 `config.json` 中配置数据库连接信息和处理参数：
```json
{
  "db_url": "postgresql://user:password@localhost:5432/dbname",
  "input_geojson": "input.geojson",
  "table_name": "geodata",
  "srid_local": 4326,
  "srid_wgs84": 4326,
  "output_geojson": "output.geojson"
}
```
### 2.数据表检查
#### 检查子表
    SELECT
        nmsp_child.nspname AS child_schema,
        child.relname AS child_table
    FROM
        pg_inherits
        JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
        JOIN pg_class child ON pg_inherits.inhrelid = child.oid
        JOIN pg_namespace nmsp_child ON nmsp_child.oid = child.relnamespace
    WHERE
        parent.relname = 'your_table_name';

#### 检查数据分布
        SELECT
        column_name,
        COUNT(*) AS count,
        MIN(value) AS min_value,
        MAX(value) AS max_value,
        AVG(value) AS avg_value
    FROM
        your_table_name
    GROUP BY
        column_name;
### 3.依赖库
    pandas
    geopandas
    sqlalchemy
    shapely
    tqdm
### 9.导入后的数据的结构 
![img.png](img.png)
