# ===========================================================================
#         TIFF 更新器 - 按范围更新
#         GeoJSON范围原地更新 (带 Web 接口)
# ===========================================================================

# ----------------------------------------------------------------------------
# 导入依赖
# ----------------------------------------------------------------------------
import os
import shutil
import logging
import asyncio
import json
import uuid
from concurrent.futures import ThreadPoolExecutor
from functools import partial

# Django HTTP 支持 (模拟)
try:
    from django.http import JsonResponse, HttpResponseBadRequest
    from django.views import View
    from django.utils.decorators import method_decorator
    from django.views.decorators.csrf import csrf_exempt
except ImportError:
    # 模拟类，以便脚本可以独立运行测试
    print("警告：未找到Django，将使用模拟类。Web API功能将不可用。")


    class View:
        def dispatch(self, request, *args, **kwargs): handler = getattr(self, request.method.lower(),
                                                                        self.http_method_not_allowed); return handler(
            request, *args, **kwargs)

        async def post(self, request): raise NotImplementedError

        def http_method_not_allowed(self, request, *args, **kwargs): return JsonResponse(
            {'error': 'Method not allowed'}, status=405)


    class JsonResponse:
        def __init__(self, data, status=200): self.data, self.status = data, status


    class HttpResponseBadRequest(JsonResponse):
        def __init__(self, message): super().__init__({'error': message}, status=400)


    def method_decorator(decorator, name='dispatch'):
        return lambda cls: cls


    def csrf_exempt(view_func):
        return view_func

import rasterio
import numpy as np
from rasterio.crs import CRS
from rasterio.transform import from_bounds
from rasterio.features import geometry_window

# ----------------------------------------------------------------------------
# 日志与常量配置
# ----------------------------------------------------------------------------
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(name)s: %(message)s')
logger = logging.getLogger(__name__)

ALLOWED_EXTS = {'.tif', '.tiff'}
SAFE_DATA_ROOT = os.path.abspath('safe_data_storage_geojson_inplace')
os.makedirs(SAFE_DATA_ROOT, exist_ok=True)


# ----------------------------------------------------------------------------
# 路径与文件校验
# ----------------------------------------------------------------------------
def validate_and_sanitize_paths(src_rel: str, update_rel: str) -> tuple[str, str]:
    """校验路径是否在安全目录内，并返回绝对路径。"""
    src_abs = os.path.abspath(os.path.join(SAFE_DATA_ROOT, src_rel))
    update_abs = os.path.abspath(os.path.join(SAFE_DATA_ROOT, update_rel))

    if not all(p.startswith(SAFE_DATA_ROOT) for p in [src_abs, update_abs]):
        raise PermissionError("不允许访问安全数据目录之外的路径")

    for path, name in [(src_abs, "源文件"), (update_abs, "更新文件")]:
        if not os.path.isfile(path): raise FileNotFoundError(f"找不到{name}：{path}")
        ext = os.path.splitext(path)[1].lower()
        if ext not in ALLOWED_EXTS: raise ValueError(f"不支持的文件后缀 '{ext}'")

    return src_abs, update_abs


# ----------------------------------------------------------------------------
# 核心逻辑 - 原地更新
# ----------------------------------------------------------------------------
def _preflight_check(src_path: str, update_path: str):
    """
    预检：在不修改任何文件的情况下，检查所有前提条件。
    如果任何检查失败，则抛出异常。
    """
    with rasterio.open(src_path, 'r') as src, rasterio.open(update_path, 'r') as upd:
        if src.crs != upd.crs:
            raise ValueError(f"坐标系(CRS)不一致: 源({src.crs}) vs 更新({upd.crs})")
        if not np.allclose(src.res, upd.res):
            raise ValueError(f"分辨率不一致: 源({src.res}) vs 更新({upd.res})")
        logger.info("预检通过: CRS和分辨率一致。")


def _core_update_by_geojson_inplace(src_path: str, update_path: str, geojson_geom: dict):
    """
    核心原地更新函数。假定所有校验已通过。
    """
    with rasterio.open(update_path) as upd, rasterio.open(src_path, 'r+') as dst:
        try:
            dst_win = geometry_window(dst, [geojson_geom])
            upd_win = geometry_window(upd, [geojson_geom])
        except Exception as e:
            raise ValueError(f"无法根据GeoJSON计算窗口: {e}")

        if not dst_win.width or not dst_win.height or not upd_win.width or not upd_win.height:
            logger.warning("GeoJSON范围与文件无有效重叠，无需更新。")
            return

        update_data = upd.read(window=upd_win)
        dst.write(update_data, window=dst_win)
        logger.info(f"成功将 '{os.path.basename(update_path)}' 的部分区域原地更新到 '{os.path.basename(src_path)}'")


# ----------------------------------------------------------------------------
# 同步与异步接口函数
# ----------------------------------------------------------------------------
def update_tiff_by_geojson_inplace(geojson_data: dict, src: str, update: str) -> dict:
    """
    同步接口，封装所有校验和业务逻辑，执行原地更新。
    """
    try:
        # 1. 解析参数
        if 'geometry' in geojson_data:
            geom = geojson_data['geometry']
        elif 'type' in geojson_data:
            geom = geojson_data
        else:
            raise ValueError("无效的GeoJSON数据，缺少 'geometry' 或 'type' 键")

        # 2. 校验路径
        src_abs, update_abs = validate_and_sanitize_paths(src, update)

        # 3. 执行预检（关键的安全步骤）
        _preflight_check(src_abs, update_abs)

        # 4. 所有检查通过后，执行核心更新
        _core_update_by_geojson_inplace(src_abs, update_abs, geom)

        return {"status": "SUCCESS", "output": src}  # 返回被修改的源文件路径
    except Exception as e:
        logger.error(f"原地更新Tiff时发生错误: {e}")
        return {"status": "ERROR", "message": str(e)}


_executor = ThreadPoolExecutor(max_workers=os.cpu_count() or 4)


async def async_update_tiff_by_geojson_inplace(*args, **kwargs) -> dict:
    """异步接口，将同步函数放入线程池执行。"""
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(_executor, partial(update_tiff_by_geojson_inplace, *args, **kwargs))


# ----------------------------------------------------------------------------
# Web API 接口 (Django View)
# ----------------------------------------------------------------------------
@method_decorator(csrf_exempt, name='dispatch')
class TiffUpdateByGeoJsonInplaceView(View):
    async def post(self, request):
        task_id = str(uuid.uuid4())
        data = getattr(request, 'POST', {})

        required_params = ['geojson_data', 'src_path', 'update_path']
        if not all(p in data for p in required_params):
            missing = [p for p in required_params if p not in data]
            return HttpResponseBadRequest(f"缺少必要参数: {', '.join(missing)}")

        try:
            kwargs = {
                'geojson_data': json.loads(data['geojson_data']),
                'src': data['src_path'],
                'update': data['update_path'],
            }
        except json.JSONDecodeError:
            return HttpResponseBadRequest("参数 'geojson_data' 不是有效的JSON格式。")
        except Exception as e:
            return HttpResponseBadRequest(f"参数解析错误: {e}")

        result = await async_update_tiff_by_geojson_inplace(**kwargs)

        response_data = {'task_id': task_id, **result}
        status_code = 200 if result['status'] == 'SUCCESS' else 500
        return JsonResponse(response_data, status=status_code)


# ----------------------------------------------------------------------------
# 本地测试验证代码
# ----------------------------------------------------------------------------
def create_test_tiff(path, bounds, w, h, color, crs_epsg=4326):
    os.makedirs(os.path.dirname(path), exist_ok=True)
    profile = {'driver': 'GTiff', 'dtype': 'uint8', 'count': 3, 'width': w, 'height': h, 'crs': CRS.from_epsg(crs_epsg),
               'transform': from_bounds(*bounds, w, h)}
    with rasterio.open(path, 'w', **profile) as dst:
        data = np.zeros((3, h, w), dtype=np.uint8);
        data[:3] = np.array(color).reshape(3, 1, 1)
        dst.write(data)


async def local_test():
    TEST_ROOT_REL = 'test_geojson_inplace'
    TEST_ROOT_ABS = os.path.join(SAFE_DATA_ROOT, TEST_ROOT_REL)
    if os.path.exists(TEST_ROOT_ABS): shutil.rmtree(TEST_ROOT_ABS)

    bounds = (110.0, 30.0, 110.5, 30.5);
    w, h = 512, 512;
    crs = 4326
    src_color, upd_color = (20, 20, 20), (200, 50, 50)
    center_rect_geom = {'type': 'Polygon',
                        'coordinates': [[(110.1, 30.1), (110.4, 30.1), (110.4, 30.4), (110.1, 30.4), (110.1, 30.1)]]}

    scenarios = {
        '01_中心矩形原地更新': {'desc': '使用矩形GeoJSON原地更新图像中心。', 'geojson': center_rect_geom},
        '02_坐标系不匹配': {'desc': '更新文件的CRS与源文件不同，预期报错且源文件不变。', 'geojson': center_rect_geom,
                            'update_crs': 3857, 'expect_fail': True},
        '03_分辨率不匹配': {'desc': '更新文件分辨率不同，预期报错且源文件不变。', 'geojson': center_rect_geom,
                            'update_size': (256, 256), 'expect_fail': True},
    }

    print("--- 开始GeoJSON原地更新本地测试 ---")
    for code, config in scenarios.items():
        print(f"\n--- 测试场景: {code} ({config['desc']}) ---")
        dir_path_rel = os.path.join(TEST_ROOT_REL, code)
        src_rel, update_rel = (os.path.join(dir_path_rel, f) for f in ["src.tif", "update.tif"])

        # 每次都创建全新的文件，避免互相影响
        src_abs = os.path.join(SAFE_DATA_ROOT, src_rel)
        create_test_tiff(src_abs, bounds, w, h, src_color, crs_epsg=crs)
        upd_bounds, upd_size, upd_crs = config.get('update_bounds', bounds), config.get('update_size',
                                                                                        (w, h)), config.get(
            'update_crs', crs)
        create_test_tiff(os.path.join(SAFE_DATA_ROOT, update_rel), upd_bounds, upd_size[0], upd_size[1], upd_color,
                         crs_epsg=upd_crs)

        result = await async_update_tiff_by_geojson_inplace(config['geojson'], src_rel, update_rel)

        is_success, should_fail = (result['status'] == 'SUCCESS'), config.get('expect_fail', False)

        if is_success and not should_fail:
            print(f"[{code}] \033[92m测试通过\033[0m. 源文件已原地更新: {src_abs}")
            # 验证步骤
            with rasterio.open(src_abs) as final_src:
                win = geometry_window(final_src, [config['geojson']])
                # 检查更新区域的中心像素
                updated_pixel = final_src.read((1, 2, 3), window=rasterio.windows.Window(win.col_off + win.width // 2,
                                                                                         win.row_off + win.height // 2,
                                                                                         1, 1))
                # 检查未更新区域的左上角像素
                original_pixel = final_src.read((1, 2, 3), window=rasterio.windows.Window(0, 0, 1, 1))
                if np.array_equal(updated_pixel.flatten(), upd_color) and np.array_equal(original_pixel.flatten(),
                                                                                         src_color):
                    print(f"[{code}] \033[92m验证成功\033[0m: 像素颜色符合预期。")
                else:
                    print(f"[{code}] \033[91m验证失败\033[0m: 更新后的像素颜色不正确！")

        elif not is_success and should_fail:
            print(f"[{code}] \033[92m测试通过\033[0m. 按预期抛出错误: '{result['message']}'")
            # 验证源文件未被修改
            with rasterio.open(src_abs) as final_src:
                original_pixel = final_src.read((1, 2, 3), window=rasterio.windows.Window(w // 2, h // 2, 1, 1))
                if np.array_equal(original_pixel.flatten(), src_color):
                    print(f"[{code}] \033[92m验证成功\033[0m: 源文件未被修改。")
                else:
                    print(f"[{code}] \033[91m验证失败\033[0m: 源文件被意外修改！")
        else:
            print(f"[{code}] \033[91m测试失败\033[0m. " + (
                f"预期应报错但却成功了！" if should_fail else f"错误原因: {result['message']}"))


if __name__ == '__main__':
    asyncio.run(local_test())
