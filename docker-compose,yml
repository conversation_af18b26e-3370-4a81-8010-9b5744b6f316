version: '3'

services:
  web:
    image: encrypted-django-app:latest
    container_name: django_web
    environment:
      - TZ=Asia/Shanghai
      - DJANGO_SECRET_KEY=your_secret_key_here
      - DJANGO_ALLOWED_HOSTS=localhost,127.0.0.1
    volumes:
      - ./app:/app
      - ./data:/app/data
    working_dir: /app
    command: bash -c "python manage.py migrate && python manage.py runserver 0.0.0.0:8000"
    ports:
      - "8000:8000"
