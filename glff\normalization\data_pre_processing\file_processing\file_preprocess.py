"""
 文档预处理  将文件统一转换为PDF
 file_converter_libreoffice.py
 基于 LibreOffice 的文件转 PDF 工具
 支持格式：doc, docx, xls, xlsx, ppt, pptx, txt, csv, png, jpeg 等
"""
import os
import subprocess
import tempfile
import locale
from pathlib import Path
from typing import Union
import sys

class LibreOfficeConverter:
    def __init__(self, libreoffice_path: str = None):
        """
        :param libreoffice_path: LibreOffice 安装路径（默认自动检测）
        """
        self.libreoffice_path = self._detect_libreoffice_path(libreoffice_path)
    def get_soffice_paths(self):
        platform = sys.platform
        if platform.startswith('win'):
            key = 'SOFFICE_PATHS_WIN32'
        elif platform == 'darwin':
            key = 'SOFFICE_PATHS_DARWIN'
        elif platform.startswith('linux'):
            key = 'SOFFICE_PATHS_LINUX'
        else:
            return []
        value = os.environ.get(key, '')
        return [p.strip() for p in value.split(';') if p.strip()]
    def _detect_libreoffice_path(self, custom_path: str) -> str:
        """自动检测 LibreOffice 可执行文件路径"""
        if custom_path:
            return custom_path

        # 常见系统默认路径
        paths = self.get_soffice_paths()
        for path in paths:
            if os.path.exists(path):
                return path
        raise FileNotFoundError("LibreOffice 未找到，请手动指定安装路径")

    def _encode_path(self, path: Union[str, Path]) -> str:
        """处理中文路径编码问题"""
        path = str(Path(path).resolve())
        if sys.platform.startswith('win'):
            # Windows 环境下使用 GBK 编码处理中文路径
            return path.encode('gbk').decode('gbk')
        return path

    def convert(
            self,
            input_path: Union[str, Path],
            output_dir: Union[str, Path] = None,
            output_format: str = "pdf",
            timeout: int = 600
    ) -> Path:
        """
        转换文件为 PDF 或其他格式
        :param input_path: 输入文件路径
        :param output_dir: 输出目录（默认同输入目录）
        :param output_format: 输出格式（pdf, html, odt 等）
        :param timeout: 超时时间（秒）
        :return: 输出文件路径
        """
        input_path = Path(input_path).resolve()
        if not input_path.exists():
            raise FileNotFoundError(f"输入文件不存在: {input_path}")

        output_dir = Path(output_dir) if output_dir else input_path.parent
        output_dir.mkdir(parents=True, exist_ok=True)

        # 处理中文路径
        input_path_str = self._encode_path(input_path)
        output_dir_str = self._encode_path(output_dir)

        # 构建命令行参数
        cmd = [
            self.libreoffice_path,
            '--headless',
            '--convert-to', output_format,
            '--outdir', output_dir_str,
        ]

        # 如果是Excel文件，添加参数确保转换所有工作表
        if input_path.suffix.lower() in ('.xls', '.xlsx'):
            cmd.extend([
                '--calc',  # 使用Calc模块处理Excel文件
                '--infilter="Calc8"',  # 指定过滤器
                '--norestore',  # 不恢复上次会话
                '--nodefault',  # 不使用默认设置
                '--nolockcheck',  # 不检查文件锁定
                '--nofirststartwizard',  # 不显示首次启动向导
            ])

        cmd.append(input_path_str)

        # 设置环境变量以解决中文乱码问题
        env = os.environ.copy()
        env['LC_ALL'] = 'zh_CN.UTF-8' if sys.platform != 'win32' else 'zh-CN'

        # 执行转换
        try:
            subprocess.run(
                cmd,
                check=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                timeout=timeout,
                env=env,
                shell=False  # 避免 shell=True 的性能和安全问题
            )
        except subprocess.TimeoutExpired:
            raise RuntimeError("转换超时，请增大 timeout 参数或检查文件复杂度")
        except subprocess.CalledProcessError as e:
            error_msg = e.stderr.decode(errors='ignore').strip()
            raise RuntimeError(f"转换失败: {error_msg}")

        # 获取输出路径
        output_file = output_dir / f"{input_path.stem}.{output_format}"
        if not output_file.exists():
            raise FileNotFoundError("输出文件未生成，请检查格式是否支持")
        return output_file

    def convert_to_pdf(
        self,
        input_path: Union[str, Path],
        output_path: Union[str, Path] = None
    ) -> Path:
        """快捷 PDF 转换方法，支持中文路径"""
        input_path = Path(input_path).resolve()
        if not input_path.exists():
            raise FileNotFoundError(f"输入文件不存在: {input_path}")

        if output_path:
            output_path = Path(output_path)
            output_dir = output_path.parent
            output_dir.mkdir(parents=True, exist_ok=True)
            return self.convert(input_path, output_path, "pdf")
        else:
            with tempfile.TemporaryDirectory() as tmp_dir:
                result = self.convert(input_path, tmp_dir, "pdf")
                if output_path is None:
                    return result
                os.replace(result, output_path)
                return Path(output_path)

# 使用示例
if __name__ == "__main__":
    # Windows 示例（需根据实际路径调整）
    converter = LibreOfficeConverter()

    # 支持的文件格式列表
    supported_formats = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'csv', 'png']
    # 为每个格式创建不同的测试文件名
    test_files = [r"H:\test\test_{}.{}".format(ext, ext) for ext in supported_formats]

    # 批量转换测试
    for file in test_files:
        if Path(file).exists():
            try:
                # 输出文件名保持唯一性
                output_path = r"H:\test\{}_converted.pdf".format(Path(file).stem)
                pdf_path = converter.convert_to_pdf(file, output_path)
                print(f"生成 PDF: {pdf_path}")
            except Exception as e:
                print(f"转换失败 {file}: {str(e)}")
        else:
            print(f"测试文件不存在: {file}")



