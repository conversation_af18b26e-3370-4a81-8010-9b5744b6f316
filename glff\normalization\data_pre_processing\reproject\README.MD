# 地理数据重投影模块

## 概述
本模块提供栅格和矢量数据的坐标转换功能，支持多种坐标参考系统之间的转换。适用于地理信息系统(GIS)数据处理、遥感影像处理等场景。

## 主要功能
- **栅格数据重投影**：支持TIF/GeoTIFF格式的栅格数据转换
- **矢量数据重投影**：支持GeoJSON和Shapefile格式的矢量数据转换
- **多种转换方式**：
  - 单点坐标转换
  - 几何图形坐标转换
  - 文件批量转换
- **坐标系支持**：
  - EPSG代码(如4326)
  - EPSG字符串(如"EPSG:4326")
  - WKT字符串
  - PROJ字符串

## 安装和依赖
```bash
pip install pyproj rasterio geopandas
```

## 使用示例

### 栅格数据转换
```python
from glff.normalization.data_pre_processing.reproject import Reprojector

reprojector = Reprojector()
reprojector.reproject_tif(
    "input.tif", 
    "output.tif", 
    src_crs="EPSG:32648", 
    dst_crs="EPSG:4326"
)
```

### 矢量数据转换
```python
from glff.normalization.data_pre_processing.reproject import Reprojector

reprojector = Reprojector()
reprojector.reproject_vector(
    "input.geojson", 
    "output.geojson", 
    src_crs="EPSG:32648", 
    dst_crs="EPSG:4326"
)
```

### 单点坐标转换
```python
from glff.normalization.data_pre_processing.reproject import transform_single_coord

point = (123.45, 67.89)
transformed_point = transform_single_coord(
    point, 
    src_crs="EPSG:32648", 
    dst_crs="EPSG:4326"
)
```

## 注意事项
1. 确保输入文件格式正确
2. 转换大文件时可能需要较多内存
3. 转换精度受原始数据质量和目标坐标系影响
4. 对于Shapefile转换，需要确保所有相关文件(.shp, .shx, .dbf等)在同一目录

## 模块结构
- `reproject.py`: 主模块，提供Reprojector类  提供批量定义投影
- `tif_reproject.py`: 栅格数据转换实现       提供影像数据坐标转换
- `vector_reproject.py`: 矢量数据转换实现    提供矢量数据坐标转换
