#### 
    属性结构正确性检查
# 属性与几何检查器 (Attribute & Geometry Checker)

**版本: v14.0 (The DBA's Final Cut)**

[![Python Version](https://img.shields.io/badge/Python-3.9+-blue.svg)](https://www.python.org/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Status](https://img.shields.io/badge/status-production/stable-green.svg)]()

一个强大、灵活且高度可配置的工具，用于验证地理空间数据（来自Shapefile或PostgreSQL/PostGIS数据库）的属性结构和几何类型的正确性。本项目从一个简单的想法，在严苛、全面的测试和反馈下，演进为一个能够覆盖几乎所有数据库原生类型的、真正可靠的工业级数据质量保证引擎。

---

## ✨ 核心功能

*   **多源支持**:
    *   ✅ 直接检查 **Shapefile** (`.shp`) 文件。
    *   ✅ 直接连接并检查 **PostgreSQL/PostGIS** 数据库中的表。
    *   ✅ 智能处理Shapefile字段名截断问题。

*   **全面的字段存在性与约束检查**:
    *   ✅ **字段存在性**: 检查必要字段是否存在。
    *   ✅ **必填项 (`required`)**: 检查字段值是否为 `NULL` 或空字符串。
    *   ✅ **唯一性 (`unique`)**: 检查字段中的所有值是否唯一。

*   **终极的数据库原生类型验证 (`type`)**:
    *   **数值**: `int`, `float`, 以及DBA级原生类型 `SMALLINT`, `BIGINT`, `DECIMAL`/`NUMERIC`。
    *   **字符串**: `string`, 以及DBA级原生类型 `TEXT`, `VARCHAR(n)`, `CHAR(n)`。
    *   **时序**: `date`, `datetime`, 以及DBA级原生类型 `TIMESTAMP`, `TIMESTAMPTZ`, `TIME`。
    *   **布尔**: `boolean` (能智能识别 `True`, `False`, `1`, `0`, `'true'`, `'false'`)。
    *   **高级原生**: `uuid`, `json`, `jsonb` (推荐), `array` (原生数组), `bytea` (二进制)。

*   **灵活的字段内容格式与范围检查**:
    *   ✅ **长度 (`min_length`, `max_length`)**: 验证字符串长度是否在指定范围内。
    *   ✅ **范围 (`range`)**: 验证数值或日期是否在指定区间内。
    *   ✅ **格式 (`format`)**: 使用正则表达式验证字符串格式。
    *   ✅ **枚举 (`enum`)**: 检查值是否属于一个给定的有效列表。

*   **专业的几何字段检查**:
    *   ✅ **几何必填**: 检查是否存在`NULL`或空的几何对象。
    *   ✅ **几何类型 (`geom_type`)**: 验证几何类型是否为预期的类型 (如 `'Point'`, `'Polygon'`)，**支持单一类型或多类型列表** (如 `['PointZ', 'LineStringM']`)。
    *   ✅ **高维几何**: 支持对 `PointZ`, `LineStringM`, `PolygonZM` 等高维几何类型的检查。

*   **清晰的JSON报告**:
    *   ✅ 生成结构化、易于解析的JSON报告，详细列出每一项检查的状态、信息和失败的样本数据。

## ⚙️ 环境要求与安装

本项目需要 Python 3.9+ 环境。

1.  **安装核心依赖**:
    ```bash
    pip install pandas geopandas sqlalchemy pyogrio
    ```

2.  **安装数据库驱动 (以PostgreSQL为例)**:
    ```bash
    pip install psycopg2-binary
    ```

## 🚀 如何使用

### 1. 作为独立脚本运行演示

您可以直接运行 `attribute_checker.py` 文件来查看完整的演示流程。脚本将自动创建临时数据（一个Shapefile和一个数据库表），执行所有类型的检查，并打印详细的报告。

```bash
python attribute_checker.py
```

> **注意**: 数据库演示需要一个正在运行的PostgreSQL服务。默认连接字符串为 `postgresql://postgres:postgres@localhost:5432/skgeo-manager`。您可以通过设置环境变量 `TEST_DB_URL` 来修改它。
>
> ```bash
# Linux/macOS
> export TEST_DB_URL="postgresql://user:password@host:port/dbname"
> python attribute_checker.py
>
> # Windows (CMD)
> set TEST_DB_URL="postgresql://user:password@host:port/dbname"
> python attribute_checker.py
> ```

### 2. 作为模块集成到您的项目中 (Programmatic Use)

这是本工具最核心的用法。您可以轻松地将其集成到您的数据ETL、数据治理或自动化测试流程中。

```python
import json
from attribute_checker import AttributeChecker

# 1. 定义您的检查规则 (Schema)
my_schema = {
    # 字段名: {规则1: 值, 规则2: 值, ...}
    'object_id': {'required': True, 'unique': True, 'type': 'int'},
    'road_name': {'required': True, 'type': 'string', 'max_length': 50},
    'speed_limit': {'type': 'int', 'range': [0, 120]},
    'status': {'type': 'string', 'enum': ['open', 'closed', 'under_construction']},
    'last_updated': {'type': 'date'},
    'geom': {'required': True, 'geom_type': 'LineString'}
}

# 2. 使用方法

# --- 2.1 从 Shapefile 检查 ---
try:
    print("--- 检查 Shapefile ---")
    shp_checker = AttributeChecker.from_shapefile(
        path='path/to/your/roads.shp',
        schema=my_schema,
        id_field='object_id' # 指定用于在报告中识别要素的字段
    )
    shp_report = shp_checker.run()
    print(json.dumps(shp_report, indent=2, ensure_ascii=False))

except Exception as e:
    print(f"Shapefile检查出错: {e}")


# --- 2.2 从 PostgreSQL 数据库检查 ---
try:
    print("\n--- 检查 PostgreSQL 表 ---")
    db_conn_str = "postgresql://user:password@host:port/dbname"
    db_checker = AttributeChecker.from_database(
        conn_str=db_conn_str,
        schema=my_schema,
        table_name='roads_table',
        db_schema='public', # 可选，指定数据库的schema
        id_field='object_id'
    )
    db_report = db_checker.run()
    print(json.dumps(db_report, indent=2, ensure_ascii=False))

except Exception as e:
    print(f"数据库检查出错: {e}")
```

## 📚 规则定义详解 (The Schema)

`schema` 是一个字典，用于定义所有检查规则。`key`是字段名，`value`是包含具体规则的另一个字典。

| 规则 | 类型 | 描述与示例 |
| :--- | :--- | :--- |
| **`required`** | `boolean` | 如果为 `True`，则字段值不能为空 (`NULL`) 或空字符串。 |
| **`unique`** | `boolean` | 如果为 `True`，则字段中的所有非空值必须是唯一的。 |
| **`type`** | `string` | **[核心]** 指定期望的数据类型。支持的值包括：<br>- `int`, `float`, `string`, `boolean` <br>- `date` (可识别日期和日期时间) <br>- `time` (仅时间) <br>- `uuid`, `json`, `jsonb`, `array`, `bytea` |
| **`range`** | `list` | 对于数值或日期类型，指定一个包含 `[最小值, 最大值]` 的列表。 |
| **`min_length`** | `int` | 字符串的最小长度。 |
| **`max_length`** | `int` | 字符串的最大长度。 |
| **`format`** | `string` | 一个正则表达式字符串，用于匹配字段值。 |
| **`enum`** | `list` | 一个包含所有允许值的列表。 |
| **`geom_type`** | `str` 或 `list` | 指定期望的几何类型。可以是单个字符串 (如 `'Point'`) 或一个包含多个可接受类型的列表 (如 `['Polygon', 'MultiPolygon']`)。 |

## 📊 理解输出报告

`run()` 方法返回一个详细的JSON格式字典。

```json
{
  "overall_status": "FAILED",
  "field_reports": {
    "speed_limit": {
      "field_name": "speed_limit",
      "overall_status": "FAILED",
      "checks": {
        "type": {
          "status": "PASSED",
          "message": "所有值均符合 'int' 类型。",
          "failed_count": 0,
          "failures_preview": []
        },
        "range": {
          "status": "FAILED",
          "message": "部分数值超出有效范围 [0, 120]。",
          "failed_count": 1,
          "failures_preview": [
            {
              "id": 103,
              "value": "150"
            }
          ]
        }
      }
    },
    "geom": {
      "field_name": "geom",
      "overall_status": "FAILED",
      "checks": {
        "geom_type": {
          "status": "FAILED",
          "message": "部分几何类型不符合期望 'LineString'。",
          "failed_count": 1,
          "failures_preview": [
            {
              "id": 104,
              "actual_type": "Point"
            }
          ]
        }
      }
    }
  }
}
```

-   **`overall_status`**: 整个检查的最终状态 (`PASSED` 或 `FAILED`)。
-   **`field_reports`**: 一个字典，包含每个被检查字段的详细报告。
-   **`field_name`**: 字段的名称。
-   **`overall_status`**: 该字段的总体检查状态。
-   **`checks`**: 一个字典，包含对该字段执行的每一项子检查的结果。
-   **`status`**: 子检查的状态 (`PASSED`, `FAILED`, `NOT_APPLICABLE`, `ERROR`)。
-   **`message`**: 对检查结果的人类可读描述。
-   **`failed_count`**: 违反此规则的记录数。
-   **`failures_preview`**: 一个最多包含10条失败记录样本的列表，每条记录都包含其**ID**和**违规值**。

## 🤝 贡献与反馈

本项目是在持续的、高质量的反馈和迭代中演进的。如果您发现任何问题或有改进建议，欢迎提出。我们追求的是一个在逻辑上无懈可击、在功能上极致完备的工具。

## 📄 许可证

本项目采用 [MIT](https://opensource.org/licenses/MIT) 许可证。