#### 
    空间参考检查


---

# 空间参考检查器 (Spatial Reference Checker) v3.0

一个强大、灵活且易于集成的Python工具，专用于检查地理空间数据文件的空间参考系统(CRS)。它可以确保您的数据在入库或进入处理流程前，符合项目定义的地理坐标系（GCS）和投影坐标系（PCS）标准。

本工具支持 **Shapefile (.shp)**, **GeoTIFF (.tif)**, 和 **GeoJSON (.geojson)** 格式，并提供结构化的JSON报告和全中文的提示信息。

## ✨ 核心特性

- **多格式支持**: 无缝处理常见的矢量和栅格数据格式。
- **配置灵活**:
    - **严格模式**: 可同时指定标准的**地理坐标系(GCS)**和**投影坐标系(PCS)**。
    - **宽松模式**: 可只指定标准的**地理坐标系(GCS)**，不强制要求投影。
- **标准化报告**: 所有检查结果以结构化的 **JSON** 格式返回，便于自动化解析和处理。
- **清晰的诊断**: 报告信息完全中文化，准确指出不匹配的项（例如，“标准GCS与数据GCS不符”）。
- **健壮性**: 基于 `geopandas`, `rasterio`, `pyproj` 等业界标准库，代码健壮可靠。
- **开箱即用**: 包含完整的调用示例和自验证的测试套件。

## ⚙️ 环境与安装

#### 1. 环境要求
- Python 3.8 或更高版本
- 建议在虚拟环境中安装 (如 `venv` 或 `conda`)

#### 2. 安装依赖
您需要安装以下核心库。可以将它们保存到 `requirements.txt` 文件中，然后通过 `pip install -r requirements.txt` 安装。

```text
geopandas
rasterio
simplejson
pyproj
numpy
```

## 🚀 快速上手

下面是如何在您的项目中使用 `SpatialReferenceChecker`：

#### 步骤 1: 导入模块

```python
from spatial_reference_checker_v3 import SpatialReferenceChecker, CRSError
import json
```

#### 步骤 2: 初始化检查器 (选择一种模式)

**模式A: 严格模式 (要求特定的GCS和PCS)**

这适用于最终成果数据，这些数据必须是特定的投影坐标系。

```python
try:
    # 标准: GCS必须是CGCS2000，PCS必须是CGCS2000 / 3-degree GK CM 111E
    strict_checker = SpatialReferenceChecker(
        standard_gcs_str="EPSG:4490",
        standard_pcs_str="EPSG:4547"
    )
    print("严格检查器初始化成功！")
except CRSError as e:
    print(f"初始化失败: {e}")
```

**模式B: 宽松模式 (只要求特定的GCS)**

这适用于原始数据或中间数据，我们只关心其大地基准是否正确。

```python
try:
    # 标准: GCS必须是CGCS2000，对投影无要求
    loose_checker = SpatialReferenceChecker(standard_gcs_str="EPSG:4490")
    print("宽松检查器初始化成功！")
except CRSError as e:
    print(f"初始化失败: {e}")
```

#### 步骤 3: 执行检查

使用初始化好的检查器对文件进行检查。

```python
file_path = "path/to/your/data.shp"
report = strict_checker.check(file_path)
```

#### 步骤 4: 分析报告

检查报告是一个Python字典，可以轻松转换为JSON。

```python
# 以美化的JSON格式打印报告
print(json.dumps(report, indent=2, ensure_ascii=False))

# 根据总体状态进行逻辑判断
if report['overall_status'] == '通过':
    print(f"文件 '{file_path}' 空间参考检查通过。")
else:
    print(f"文件 '{file_path}' 空间参考检查失败或出错！")

```

## 📋 理解检查报告

检查报告的JSON结构如下，旨在提供清晰、可操作的信息。

```json
{
  "overall_status": "通过" | "失败" | "错误",
  "checks": {
    "crs_existence": {
      "status": "PASSED" | "FAILED",
      "message": "中文描述信息",
      "details": {}
    },
    "geodetic_crs_check": {
      "status": "PASSED" | "FAILED",
      "message": "地理坐标系 (GCS) 与标准不一致。",
      "details": {
        "标准GCS": "China Geodetic Coordinate System 2000",
        "数据GCS": "WGS 84"
      }
    },
    "projected_crs_check": {
      "status": "PASSED" | "FAILED",
      "message": "投影坐标系 (PCS) 与标准不一致。",
      "details": {
        "标准PCS": "CGCS2000 / 3-degree Gauss-Kruger CM 111E",
        "数据PCS": "WGS 84 / UTM zone 50N"
      }
    }
    // ... 可能还有针对GeoJSON的特定检查项
  }
}
```
- **`overall_status`**: 最终的检查结论。只有当所有子项检查都为 `PASSED` 时，此项才为 `通过`。
- **`checks`**: 一个字典，包含所有执行的子检查项的结果。
- **`status`**: 单项检查的状态 (`PASSED`, `FAILED`, `ERROR`)。
- **`message`**: 对该项检查结果的中文摘要。
- **`details`**: 一个包含额外上下文信息的字典，例如标准值和数据中的实际值，便于快速定位问题。

## 🔬 API 参考

### `SpatialReferenceChecker(standard_gcs_str, standard_pcs_str=None)`
- **`standard_gcs_str` (str)**: 定义标准地理坐标系的字符串 (如 `"EPSG:4490"`)。**此参数为必需项**。
- **`standard_pcs_str` (Optional[str])**: 定义标准投影坐标系的字符串 (如 `"EPSG:4547"`)。如果为 `None` 或未提供，则不检查投影。
- **引发 (Raises)**: `pyproj.exceptions.CRSError` 如果提供的字符串无法解析，或者类型不匹配（例如，为GCS参数传入了投影坐标系）。

### `checker.check(filepath)`
- **`filepath` (str)**: 待检查的空间数据文件路径。
- **返回 (Returns)**: `Dict[str, Any]`，一个包含完整检查结果的字典。

## ✅ 运行测试

该脚本包含一个自验证的测试套件。要运行它，只需在终端中直接执行此Python文件：
```bash
python spatial_reference_checker_v3.0.py
```
如果所有断言都通过，您将在最后看到 `🎉 演示与自动化测试全部通过！` 的消息，这证明您的环境配置正确且模块功能正常。