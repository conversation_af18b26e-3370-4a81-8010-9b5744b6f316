<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据质量检查 - 数据规范化处理子系统</title>
    <link href="../lib/css/googleapis.css" rel="stylesheet">
    <link rel="stylesheet" href="lib/css/all.min.css">
    <style>
        /* 设计系统 - 现代化科技蓝主题 */
        :root {
            /* 主色调 */
            --primary-50: #e6f7ff;
            --primary-100: #b3e0ff;
            --primary-200: #80c9ff;
            --primary-300: #4db3ff;
            --primary-400: #1a9cff;
            --primary-500: #0085ff;
            --primary-600: #0066cc;
            --primary-700: #004799;
            --primary-800: #002866;
            --primary-900: #000933;
            
            /* 中性色 */
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            
            /* 背景色 */
            --bg-primary: #0a0f1c;
            --bg-secondary: #111827;
            --bg-tertiary: #1f2937;
            --bg-card: rgba(31, 41, 55, 0.8);
            --bg-overlay: rgba(10, 15, 28, 0.95);
            --bg-sidebar: rgba(15, 20, 25, 0.95);
            
            /* 文字色 */
            --text-primary: #ffffff;
            --text-secondary: #94a3b8;
            --text-tertiary: #64748b;
            
            /* 边框色 */
            --border-primary: #374151;
            --border-secondary: #4b5563;
            --border-accent: #3b82f6;
            
            /* 状态色 */
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --info: #3b82f6;
            
            /* 阴影 */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            
            /* 动画 */
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
            
            /* 间距 */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
            --space-24: 6rem;
            
            /* 圆角 */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
            --radius-3xl: 2rem;
        }

        /* 重置样式 */
        *,
        *::before,
        *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            color: var(--text-primary);
            line-height: 1.6;
            font-size: 16px;
            font-weight: 400;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* 工具类 */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 var(--space-6);
        }

        /* 侧边栏 */
        .sidebar {
            position: fixed;
            top: 80px;
            left: 0;
            width: 280px;
            height: calc(100vh - 80px);
            background: var(--bg-sidebar);
            backdrop-filter: blur(20px);
            border-right: 1px solid var(--border-primary);
            overflow-y: auto;
            z-index: 100;
        }

        .sidebar-header {
            padding: var(--space-6);
            border-bottom: 1px solid var(--border-primary);
        }

        .sidebar-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--space-2);
        }

        .sidebar-subtitle {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .sidebar-menu {
            padding: var(--space-4);
        }

        .menu-category {
            margin-bottom: var(--space-6);
        }

        .menu-category-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-tertiary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: var(--space-3);
            padding: 0 var(--space-3);
        }

        .menu-item {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-3) var(--space-4);
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--radius-md);
            transition: var(--transition-fast);
            margin-bottom: var(--space-1);
            cursor: pointer;
        }

        .menu-item:hover {
            background: rgba(55, 65, 81, 0.3);
            color: var(--text-primary);
        }

        .menu-item.active {
            background: rgba(59, 130, 246, 0.1);
            color: var(--primary-400);
            border-left: 3px solid var(--primary-400);
        }

        .menu-item-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: inherit;
        }

        .menu-item-text {
            flex: 1;
            font-size: 0.875rem;
            font-weight: 500;
        }

        /* 主内容区 */
        .main-content {
            margin-left: 280px;
            padding-top: 80px;
            min-height: 100vh;
        }

        .flex {
            display: flex;
        }

        .flex-col {
            flex-direction: column;
        }

        .items-center {
            align-items: center;
        }

        .justify-between {
            justify-content: space-between;
        }

        .justify-center {
            justify-content: center;
        }

        .text-center {
            text-align: center;
        }

        .gap-4 {
            gap: var(--space-4);
        }

        .gap-6 {
            gap: var(--space-6);
        }

        .gap-8 {
            gap: var(--space-8);
        }

        .mb-4 {
            margin-bottom: var(--space-4);
        }

        .mb-6 {
            margin-bottom: var(--space-6);
        }

        .mb-8 {
            margin-bottom: var(--space-8);
        }

        .mb-12 {
            margin-bottom: var(--space-12);
        }

        .mt-8 {
            margin-top: var(--space-8);
        }

        .mt-12 {
            margin-top: var(--space-12);
        }

        .pt-20 {
            padding-top: var(--space-20);
        }

        .pb-20 {
            padding-bottom: var(--space-20);
        }

        .px-6 {
            padding-left: var(--space-6);
            padding-right: var(--space-6);
        }

        .py-8 {
            padding-top: var(--space-8);
            padding-bottom: var(--space-8);
        }

        .py-12 {
            padding-top: var(--space-12);
            padding-bottom: var(--space-12);
        }

        .py-16 {
            padding-top: var(--space-16);
            padding-bottom: var(--space-16);
        }

        /* 顶部导航栏 */
        .top-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(10, 15, 28, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-primary);
            transition: var(--transition-normal);
        }

        .top-navbar.scrolled {
            background: rgba(10, 15, 28, 0.98);
            box-shadow: var(--shadow-lg);
        }

        .top-navbar-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-4) 0;
        }

        .top-navbar-brand {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
            text-decoration: none;
        }

        .top-navbar-brand-icon {
            width: 28px;
            height: 28px;
            background: linear-gradient(135deg, var(--primary-400), var(--primary-600));
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.875rem;
        }

        .top-navbar-actions {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        /* 按钮组件 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
            padding: var(--space-3) var(--space-6);
            border: none;
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: var(--transition-fast);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: var(--transition-normal);
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: rgba(55, 65, 81, 0.8);
            color: var(--text-primary);
            border: 1px solid var(--border-primary);
        }

        .btn-secondary:hover {
            background: rgba(55, 65, 81, 0.9);
            border-color: var(--primary-400);
            color: var(--primary-400);
        }

        .btn-ghost {
            background: transparent;
            color: var(--text-secondary);
            border: 1px solid transparent;
        }

        .btn-ghost:hover {
            background: rgba(55, 65, 81, 0.5);
            color: var(--text-primary);
        }

        /* 主布局 */
        .main-layout {
            display: flex;
            min-height: 100vh;
            padding-top: 80px;
        }

        /* 侧边栏 */
        .sidebar {
            width: 280px;
            background: var(--bg-sidebar);
            backdrop-filter: blur(20px);
            border-right: 1px solid var(--border-primary);
            padding: var(--space-8) 0;
            position: fixed;
            top: 80px;
            left: 0;
            bottom: 0;
            overflow-y: auto;
        }

        .sidebar-nav {
            padding: 0 var(--space-6);
        }

        .sidebar-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: var(--space-4);
            padding: 0 var(--space-4);
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-3) var(--space-4);
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--radius-md);
            margin-bottom: var(--space-1);
            transition: var(--transition-fast);
            cursor: pointer;
        }

        .sidebar-item:hover {
            background: rgba(59, 130, 246, 0.1);
            color: var(--primary-400);
        }

        .sidebar-item.active {
            background: rgba(59, 130, 246, 0.15);
            color: var(--primary-400);
            border-left: 3px solid var(--primary-400);
        }

        .sidebar-item i {
            width: 20px;
            text-align: center;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: var(--space-8);
        }

        .content-header {
            margin-bottom: var(--space-8);
        }

        .content-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--space-2);
        }

        .content-subtitle {
            color: var(--text-secondary);
            font-size: 1.125rem;
        }

        /* 表单样式 */
        .form-container {
            background: var(--bg-card);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            margin-bottom: var(--space-8);
        }

        .form-group {
            margin-bottom: var(--space-6);
        }

        .form-label {
            display: block;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--space-2);
        }

        .form-input {
            width: 100%;
            padding: var(--space-3) var(--space-4);
            background: rgba(15, 20, 25, 0.8);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-md);
            color: var(--text-primary);
            font-size: 0.875rem;
            transition: var(--transition-fast);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-400);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-select {
            width: 100%;
            padding: var(--space-3) var(--space-4);
            background: rgba(15, 20, 25, 0.8);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-md);
            color: var(--text-primary);
            font-size: 0.875rem;
            transition: var(--transition-fast);
        }

        .form-select:focus {
            outline: none;
            border-color: var(--primary-400);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-textarea {
            width: 100%;
            padding: var(--space-3) var(--space-4);
            background: rgba(15, 20, 25, 0.8);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-md);
            color: var(--text-primary);
            font-size: 0.875rem;
            transition: var(--transition-fast);
            resize: vertical;
            min-height: 100px;
        }

        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-400);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 进度条 */
        .progress-container {
            display: none;
            margin: var(--space-6) 0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(15, 20, 25, 0.8);
            border-radius: var(--radius-md);
            overflow: hidden;
            margin-bottom: var(--space-2);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
            border-radius: var(--radius-md);
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-text {
            font-size: 0.875rem;
            color: var(--text-secondary);
            text-align: center;
        }

        /* 结果区域 */
        .result-container {
            display: none;
            background: var(--bg-card);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            margin-top: var(--space-8);
        }

        .result-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: var(--space-6);
            padding-bottom: var(--space-4);
            border-bottom: 1px solid var(--border-primary);
        }

        .result-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .result-status {
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-md);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }

        .status-error {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error);
        }

        .status-warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning);
        }

        /* 模态框 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            z-index: 2000;
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: var(--bg-overlay);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-xl);
            padding: var(--space-8);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-6);
            padding-bottom: var(--space-4);
            border-bottom: 1px solid var(--border-primary);
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .modal-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.5rem;
            cursor: pointer;
            padding: var(--space-2);
            border-radius: var(--radius-md);
            transition: var(--transition-fast);
        }

        .modal-close:hover {
            background: rgba(55, 65, 81, 0.5);
            color: var(--text-primary);
        }

        .modal-body {
            color: var(--text-secondary);
        }

        .modal-body h4 {
            color: var(--text-primary);
            margin-bottom: var(--space-3);
            font-weight: 600;
        }

        .modal-body ul {
            margin-left: var(--space-4);
            margin-bottom: var(--space-6);
        }

        .modal-body li {
            margin-bottom: var(--space-1);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .sidebar {
                transform: translateX(-100%);
                transition: var(--transition-normal);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 var(--space-4);
            }

            .main-content {
                padding: var(--space-4);
            }

            .top-navbar-actions {
                gap: var(--space-2);
            }

            .btn {
                padding: var(--space-2) var(--space-4);
                font-size: 0.8rem;
            }

            .menu-category {
                margin-bottom: var(--space-4);
            }

            .menu-item {
                padding: var(--space-2) var(--space-3);
            }

            .form-container {
                padding: var(--space-6);
            }
        }

        /* 动画 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        .animate-fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.8s ease-out;
        }

        /* 文件上传样式 */
        .file-upload-container {
            position: relative;
            margin-bottom: 1rem;
        }
        
        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        .file-label {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            border: 2px dashed var(--border-color);
            border-radius: var(--radius-lg);
            background: var(--bg-card);
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .file-label:hover {
            border-color: var(--primary-color);
            background: var(--bg-hover);
        }
        
        .file-label i {
            font-size: 2rem;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
        }
        
        .file-label span {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }
        
        .file-label small {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .file-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.75rem;
            background: var(--bg-success);
            border-radius: var(--radius-lg);
            margin-top: 0.5rem;
        }
        
        .file-details {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .file-details i {
            color: var(--success-color);
        }
        
        .file-size {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .btn-remove {
            background: none;
            border: none;
            color: var(--danger-color);
            cursor: pointer;
            padding: 0.25rem;
            border-radius: var(--radius-sm);
            transition: background 0.2s ease;
        }
        
        .btn-remove:hover {
            background: var(--bg-danger);
        }
        
        .upload-progress {
            margin-top: 0.5rem;
        }
        
        .upload-progress .progress-bar {
            height: 4px;
            background: var(--bg-secondary);
            border-radius: 2px;
            overflow: hidden;
        }
        
        .upload-progress .progress-fill {
            height: 100%;
            background: var(--primary-color);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .upload-progress .progress-text {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-top: 0.25rem;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-secondary);
            border-radius: var(--radius-md);
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--border-primary);
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="top-navbar" id="topNavbar">
        <div class="container">
            <div class="top-navbar-content">
                <a href="/" class="top-navbar-brand">
                    <div class="top-navbar-brand-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <span>数据规范化处理子系统</span>
                </a>
                <div class="top-navbar-actions">
                    <button class="btn btn-ghost" id="helpBtn">
                        <i class="fas fa-question-circle"></i>
                        <span>帮助</span>
                    </button>
                    <button class="btn btn-ghost" id="historyBtn">
                        <i class="fas fa-history"></i>
                        <span>历史</span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主布局 -->
    <div class="main-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-title">数据质量检查</div>
                <div class="sidebar-subtitle">选择检查功能开始验证</div>
            </div>
            <nav class="sidebar-menu">
                <div class="menu-category">
                    <div class="menu-category-title">属性结构正确性检查</div>
                    <a href="#" class="menu-item active" data-function="attribute_check">
                        <div class="menu-item-icon">
                            <i class="fas fa-list-check"></i>
                        </div>
                        <div class="menu-item-text">属性结构正确性检查（文件）</div>
                    </a>
                    <a href="#" class="menu-item" data-function="attribute_check_db">
                        <div class="menu-item-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="menu-item-text">属性结构正确性检查（库）</div>
                    </a>
                </div>

                <div class="menu-category">
                    <div class="menu-category-title">拓扑关系检查</div>
                    <a href="#" class="menu-item" data-function="topology">
                        <div class="menu-item-icon">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <div class="menu-item-text">拓扑关系检查（文件）</div>
                    </a>
            
                    <a href="#" class="menu-item" data-function="topology_db">
                        <div class="menu-item-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="menu-item-text">拓扑关系检查（库）</div>
                    </a>
                </div>

                <div class="menu-category">
                    <div class="menu-category-title">空间参考检查</div>
                    <a href="#" class="menu-item" data-function="spatial_reference">
                        <div class="menu-item-icon">
                            <i class="fas fa-compass"></i>
                        </div>
                        <div class="menu-item-text">空间参考检查（文件）</div>
                    </a>
                    <a href="#" class="menu-item" data-function="spatial_reference_db">
                        <div class="menu-item-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="menu-item-text">空间参考检查（库）</div>
                    </a>
                </div>


                <div class="menu-category">
                    <div class="menu-category-title">数据完整性检查</div>
                    <a href="#" class="menu-item" data-function="integrity">
                        <div class="menu-item-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="menu-item-text">数据完整性检查（文件）</div>
                    </a>
                    <a href="#" class="menu-item" data-function="db_integrity">
                        <div class="menu-item-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="menu-item-text">数据完整性检查（库）</div>
                    </a>
                </div>

                <div class="menu-category">
                    <div class="menu-category-title">逻辑一致性检查</div>
                    <a href="#" class="menu-item" data-function="logical_consistency">
                        <div class="menu-item-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <div class="menu-item-text">逻辑一致性检查</div>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <div class="content-header">
                <h1 class="content-title">数据质量检查</h1>
                <p class="content-subtitle">选择质量检查功能，上传数据文件，开始质量检查</p>
            </div>

            <!-- 表单容器 -->
            <div class="form-container animate-fade-in-up">
                <form id="qualityForm">
                    <input type="hidden" id="functionSelect" name="functionSelect" value="attribute_check">

                    <div class="form-group">
                        <label class="form-label">上传文件</label>
                        <div class="file-upload-container">
                            <input type="file" id="fileInput" class="file-input" accept=".zip" required>
                            <label for="fileInput" class="file-label">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <span>选择文件或拖拽到此处</span>
                                <small id="fileTypeTip">请上传包含 .shp/.shx/.dbf/.prj 等所有文件的 <b>zip包</b></small>
                            </label>
                        </div>
                        <div id="fileInfo" class="file-info" style="display: none;">
                            <div class="file-details">
                                <i class="fas fa-file-alt"></i>
                                <span id="fileName"></span>
                                <span id="fileSize" class="file-size"></span>
                            </div>
                            <button type="button" class="btn-remove" onclick="removeFile()" title="移除文件">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div id="uploadProgress" class="upload-progress" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <span class="progress-text">上传中...</span>
                        </div>
                    </div>

                    <div id="dynamicForm" class="form-group">
                        <!-- 动态表单内容 -->
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        <span>开始检查</span>
                    </button>
                </form>
            </div>

            <!-- 进度条 -->
            <div class="progress-container" id="progressContainer">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">检查中...</div>
            </div>

            <!-- 结果容器 -->
            <div class="result-container" id="resultContainer">
                <div class="result-header">
                    <h3 class="result-title">检查结果</h3>
                    <span class="result-status status-success" id="resultStatus">通过</span>
                </div>
                <div id="resultContent">
                    <!-- 结果内容 -->
                </div>
            </div>

            <!-- 数据完整性检查（库）表单 -->
            <div id="db-integrity-form" class="quality-form" style="display:none;">
                <h3>数据完整性检查（库）</h3>
                <div class="form-group">
                    <label for="db-conn">数据库连接字符串</label>
                    <input type="text" id="db-conn" name="db_conn" class="form-control" placeholder="sqlite:///db.sqlite3 或其他连接串">
                </div>
                <div class="form-group">
                    <label for="db-schema">Schema（可选）</label>
                    <input type="text" id="db-schema" name="db_schema" class="form-control" placeholder="如 public">
                </div>
                <div class="form-group">
                    <label for="db-table">表名</label>
                    <input type="text" id="db-table" name="db_table" class="form-control" placeholder="如 my_table">
                </div>
                <div class="form-group">
                    <label for="db-fields">必填字段（逗号分隔）</label>
                    <input type="text" id="db-fields" name="db_fields" class="form-control" placeholder="如 id,name,geom">
                </div>
                <div class="form-group">
                    <label for="db-expected-count">期望条数（可选）</label>
                    <input type="number" id="db-expected-count" name="db_expected_count" class="form-control" placeholder="如 100">
                </div>
                <div class="form-group">
                    <label for="spatial-fields">空间字段（空间字段检查，逗号分隔）</label>
                    <input type="text" id="spatial-fields" name="spatial_fields" class="form-input" placeholder="如 geom,geometry">
                </div>
                <button type="button" class="btn btn-primary" id="run-db-integrity">运行检查</button>
            </div>
        </main>
    </div>

    <!-- 帮助模态框 -->
    <div class="modal" id="helpModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">质量检查功能帮助</h3>
                <button class="modal-close" onclick="closeModal('helpModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="mb-6">
                    <h4>
                        <i class="fas fa-list-alt" style="margin-right: 0.5rem;"></i>
                        属性检查
                    </h4>
                    <ul>
                        <li>• 检查数据字段结构和类型</li>
                        <li>• 验证属性值的有效性</li>
                        <li>• 检查必填字段的完整性</li>
                    </ul>
                </div>
                
                <div class="mb-6">
                    <h4>
                        <i class="fas fa-project-diagram" style="margin-right: 0.5rem;"></i>
                        拓扑检查
                    </h4>
                    <ul>
                        <li>• 检查空间数据的几何关系</li>
                        <li>• 验证多边形闭合性</li>
                        <li>• 检查重叠和缝隙问题</li>
                    </ul>
                </div>
                
                <div class="mb-6">
                    <h4>
                        <i class="fas fa-check-circle" style="margin-right: 0.5rem;"></i>
                        逻辑一致性
                    </h4>
                    <ul>
                        <li>• 检查数据逻辑关系</li>
                        <li>• 验证业务规则</li>
                        <li>• 检查数据约束条件</li>
                    </ul>
                </div>
                
                <div>
                    <h4>
                        <i class="fas fa-lightbulb" style="margin-right: 0.5rem;"></i>
                        使用建议
                    </h4>
                    <ul>
                        <li>• 建议先进行数据预处理，再进行质量检查</li>
                        <li>• 检查结果会生成详细报告</li>
                        <li>• 支持批量检查多个文件</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 历史记录模态框 -->
    <div class="modal" id="historyModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">质量检查历史记录</h3>
                <button class="modal-close" onclick="closeModal('historyModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h4>历史任务</h4>
                        <button class="btn btn-secondary" onclick="clearHistory()">
                            <i class="fas fa-trash" style="margin-right: 0.25rem;"></i>
                            清空
                        </button>
                    </div>
                    <div id="historyList" class="space-y-2 max-h-40 overflow-y-auto"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 导航栏滚动效果
        window.addEventListener('scroll', () => {
            const navbar = document.getElementById('topNavbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // 侧边栏功能选择
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                
                // 更新侧边栏激活状态
                document.querySelectorAll('.menu-item').forEach(i => i.classList.remove('active'));
                item.classList.add('active');
                
                // 更新表单选择
                const functionValue = item.getAttribute('data-function');
                const functionSelect = document.getElementById('functionSelect');
                functionSelect.value = functionValue;
                
                // 清空表单
                clearForm();
                
                // 更新动态表单
                updateDynamicForm(functionValue);
                
                // 更新页面标题显示当前选择的功能
                updatePageTitle(functionValue);
            });
        });

        // 文件上传处理
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const uploadProgress = document.getElementById('uploadProgress');
        const fileTypeTip = document.getElementById('fileTypeTip');
        const fileUploadGroup = document.querySelector('.form-group'); // 上传文件区域

        fileInput.addEventListener('change', handleFileSelect);
        fileInput.addEventListener('dragover', handleDragOver);
        fileInput.addEventListener('drop', handleFileDrop);

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                displayFileInfo(file);
            }
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.closest('.file-label').style.borderColor = 'var(--primary-color)';
        }

        function handleFileDrop(e) {
            e.preventDefault();
            const file = e.dataTransfer.files[0];
            if (file) {
                fileInput.files = e.dataTransfer.files;
                displayFileInfo(file);
            }
            e.currentTarget.closest('.file-label').style.borderColor = 'var(--border-color)';
        }

        function displayFileInfo(file) {
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.style.display = 'flex';
            uploadProgress.style.display = 'none';
            fileTypeTip.style.display = 'block'; // 显示提示
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function removeFile() {
            fileInput.value = '';
            fileInfo.style.display = 'none';
            uploadProgress.style.display = 'none';
            fileTypeTip.style.display = 'none'; // 隐藏提示
        }

        function clearForm() {
            // 清空文件上传
            fileInput.value = '';
            fileInfo.style.display = 'none';
            uploadProgress.style.display = 'none';
            fileTypeTip.style.display = 'none'; // 隐藏提示
            // 隐藏上传区域（数据库拓扑检查时）
            fileUploadGroup.style.display = '';
            // 清空动态表单
            const dynamicForm = document.getElementById('dynamicForm');
            dynamicForm.innerHTML = '';
            // 隐藏进度和结果
            document.getElementById('progressContainer').style.display = 'none';
            document.getElementById('resultContainer').style.display = 'none';
        }



        // 更新页面标题显示当前选择的功能
        function updatePageTitle(functionType) {
            const contentTitle = document.querySelector('.content-title');
            const functionNames = {
                'attribute_check': '属性检查',
                'attribute_check_db': '数据库属性检查',
                'topology': '拓扑关系',
                'integrity': '数据完整性',
                'spatial_reference': '空间参考',
                'logical_consistency': '逻辑一致性'
            };
            
            const functionName = functionNames[functionType] || '数据质量检查';
            contentTitle.textContent = functionName;
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化默认功能
            updateDynamicForm('attribute_check');
            updatePageTitle('attribute_check');
        });

        // 更新动态表单
        function updateDynamicForm(functionType) {
            const dynamicForm = document.getElementById('dynamicForm');
            let formHTML = '';

            // 控制上传文件区域显示/隐藏，并动态设置 required 属性
            const fileUploadGroup = document.querySelector('.form-group');
            const fileInput = document.getElementById('fileInput');
            if (fileUploadGroup && fileInput) {
                if (functionType === 'db_integrity') {
                    fileUploadGroup.style.display = 'none';
                    fileInput.removeAttribute('required');
                } else if (functionType === 'topology_db' || functionType === 'attribute_check_db' || functionType === 'spatial_reference_db' || functionType === 'logical_consistency') {
                    fileUploadGroup.style.display = 'none';
                    fileInput.removeAttribute('required');
                } else {
                    fileUploadGroup.style.display = '';
                    fileInput.setAttribute('required', 'required');
                }
            }

            switch(functionType) {
                case 'attribute_check':
                    formHTML = `
                        <div class="form-group">
                            <label class="form-label">检查Schema (JSON)</label>
                            <textarea class="form-textarea" id="schema" name="schema" placeholder='{"id": {"required": true, "unique": false, "type": "int"}}'></textarea>
                            <small style="color: var(--warning); display: block; margin-top: 0.5rem;">格式要求：true/false 必须小写，不能用 True/False。示例：<br>{"id": {"required": true, "unique": false, "type": "int"}}</small>
                        </div>
                        <div class="form-group">
                            <label class="form-label">ID字段</label>
                            <input type="text" class="form-input" id="idField" name="id_field" placeholder="ID字段名" value="id">
                        </div>
                    `;
                    break;
                case 'db_integrity':
                    formHTML = `
                        <div class="form-group">
                            <label class="form-label">数据库连接字符串</label>
                            <input type="text" class="form-input" id="dbConnStr" name="db_conn_str" placeholder="postgresql://user:password@host:port/dbname">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Schema</label>
                            <input type="text" class="form-input" id="dbSchema" name="db_schema" placeholder="public">
                        </div>
                        <div class="form-group">
                            <label class="form-label">表名</label>
                            <input type="text" class="form-input" id="dbTable" name="db_table" placeholder="表名">
                        </div>
                        <div class="form-group">
                            <label class="form-label">字段完整性字段（用于字段存在检查，逗号分隔）</label>
                            <input type="text" class="form-input" id="fieldsExists" name="fields_exists" placeholder="如 id,name">
                        </div>
                        <div class="form-group">
                            <label class="form-label">非空字段（用于内容不能为空检查，逗号分隔）</label>
                            <input type="text" class="form-input" id="notEmptyFields" name="not_empty_fields" placeholder="如 name,geom">
                        </div>
                        <div class="form-group">
                            <label class="form-label">期望数据条数</label>
                            <input type="number" class="form-input" id="dbExpectedCount" name="db_expected_count" placeholder="期望数据条数">
                        </div>
                        <div class="form-group">
                            <label for="spatial-fields" class="form-label">空间字段（空间字段检查，逗号分隔）</label>
                            <input type="text" id="spatial-fields" name="spatial_fields" class="form-input" placeholder="如 geom,geometry">
                        </div>
                    `;
                    break;
                case 'attribute_check_db':
                    formHTML = `
                        <div class="form-group">
                            <label class="form-label">数据库连接</label>
                            <input type="text" class="form-input" id="dbConnStr" name="db_conn_str" placeholder="数据库连接字符串">
                        </div>
                        <div class="form-group">
                            <label class="form-label">表名</label>
                            <input type="text" class="form-input" id="tableName" name="table_name" placeholder="要检查的表名">
                        </div>
                        <div class="form-group">
                            <label class="form-label">数据库Schema</label>
                            <input type="text" class="form-input" id="dbSchema" name="db_schema" placeholder="数据库Schema">
                        </div>
                        <div class="form-group">
                            <label class="form-label">检查Schema (JSON)</label>
                            <textarea class="form-textarea" id="schema" name="schema" placeholder='{"id": {"required": true, "unique": true, "type": "int"}}'></textarea>
                            <small style="color: var(--warning); display: block; margin-top: 0.5rem;">格式要求：true/false 必须小写，不能用 True/False。示例：<br>{"id": {"required": true, "unique": true, "type": "int"}}</small>
                        </div>
                        <div class="form-group">
                            <label class="form-label">ID字段</label>
                            <input type="text" class="form-input" id="idField" name="id_field" placeholder="ID字段名" value="id">
                        </div>
                    `;
                    break;
                case 'topology':
                    formHTML = `
                        <div class="form-group">
                            <label class="form-label">ID字段</label>
                            <input type="text" class="form-input" id="idField" name="id_field" placeholder="ID字段名" value="id">
                        </div>
                    `;
                    break;
                case 'topology_db':
                    formHTML = `
                        <div class="form-group">
                            <label class="form-label">数据库连接</label>
                            <input type="text" class="form-input" id="dbConnStr" name="db_conn_str" value="postgresql://postgres:postgres@localhost:5432/skgeo-manager" placeholder="数据库连接字符串">
                        </div>
                        <div class="form-group">
                            <label class="form-label">表名</label>
                            <input type="text" class="form-input" id="tableName" name="table_name" value="spatial_table" placeholder="空间表名">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Schema</label>
                            <input type="text" class="form-input" id="schema" name="schema" value="geodata" placeholder="Schema">
                        </div>
                        <div class="form-group">
                            <label class="form-label">主键字段</label>
                            <input type="text" class="form-input" id="idField" name="id_field" value="id" placeholder="主键字段">
                        </div>
                        <div class="form-group">
                            <label class="form-label">几何字段</label>
                            <input type="text" class="form-input" id="geomColumn" name="geom_column" value="geometry" placeholder="几何字段">
                        </div>
                        <div class="form-group">
                            <label class="form-label">分批大小</label>
                            <input type="number" class="form-input" id="chunkSize" name="chunk_size" value="10000" placeholder="分批处理大小">
                        </div>
                    `;
                    break;
                case 'logical_consistency':
                    formHTML = `
                        <div class="form-group">
                            <label class="form-label">规则套件 (suite_rules)</label>
                            <textarea class="form-textarea" id="suiteRules" name="suite_rules" placeholder="输入检查规则，JSON数组格式"></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">数据库连接 (db_conn_str)</label>
                            <input type="text" class="form-input" id="dbConnStr" name="db_conn_str" placeholder="数据库连接字符串">
                        </div>
                        <div style="background:#181e2a;padding:1rem;border-radius:8px;margin-bottom:1rem;font-size:0.95rem;color:var(--text-secondary);">
                            <b>参数示例：</b><br>
                            <pre style="white-space:pre-wrap;word-break:break-all;">
{
  "suite_rules": [
    {
      "rule_id": "park_name_must_exist",
      "type": "ATTRIBUTE_NOT_NULL",
      "spec": {
        "layer": "attr_check_demo_v12.parks",
        "id_field": "park_id",
        "attribute": "park_name"
      }
    },
    {
      "rule_id": "playgrounds_must_be_inside_a_park",
      "type": "SPATIAL_RELATIONSHIP",
      "spec": {
        "quantifier": "all",
        "predicate": "within",
        "layer1": "attr_check_demo_v12.facilities",
        "id_field1": "facility_id",
        "where1": "facility_type == 'Playground'",
        "layer2": "public.parks",
        "id_field2": "park_id"
      }
    }
  ],
  "db_conn_str": "postgresql://postgres:postgres@localhost:5432/skgeo-manager",
}
                            </pre>
                        </div>
                    `;
                    break;
                case 'spatial_reference':
                    formHTML = `
                        <div class="form-group">
                            <label class="form-label">地理坐标系统</label>
                            <select class="form-select" id="gcs" name="gcs">
                                <option value="EPSG:4326">WGS84 (EPSG:4326)</option>
                                <option value="EPSG:4490">CGCS2000 (EPSG:4490)</option>
                                <option value="EPSG:4214">Beijing1954 (EPSG:4214)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">投影坐标系统</label>
                            <select class="form-select" id="pcs" name="pcs">
                                <option value="">无投影</option>
                                <option value="EPSG:3857">Web Mercator (EPSG:3857)</option>
                                <option value="EPSG:32648">UTM Zone 48N (EPSG:32648)</option>
                            </select>
                        </div>
                    `;
                    break;
                case 'spatial_reference_db':
                    formHTML = `
                        <div class="form-group">
                            <label class="form-label">数据库连接</label>
                            <input type="text" class="form-input" id="dbConnStr" name="db_conn_str" value="postgresql://postgres:postgres@localhost:5432/skgeo-manager" placeholder="数据库连接字符串">
                        </div>
                        <div class="form-group">
                            <label class="form-label">表名</label>
                            <input type="text" class="form-input" id="tableName" name="table_name" value="spatial_table" placeholder="空间表名">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Schema</label>
                            <input type="text" class="form-input" id="dbSchema" name="db_schema" value="geodata" placeholder="Schema">
                        </div>
                        <div class="form-group">
                            <label class="form-label">几何字段</label>
                            <input type="text" class="form-input" id="geomColumn" name="geom_column" value="geometry" placeholder="几何字段">
                        </div>
                        <div class="form-group">
                            <label class="form-label">地理坐标系统</label>
                            <select class="form-select" id="gcs" name="gcs">
                                <option value="EPSG:4326">WGS84 (EPSG:4326)</option>
                                <option value="EPSG:4490">CGCS2000 (EPSG:4490)</option>
                                <option value="EPSG:4214">Beijing1954 (EPSG:4214)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">投影坐标系统</label>
                            <select class="form-select" id="pcs" name="pcs">
                                <option value="">无投影</option>
                                <option value="EPSG:4547">CGCS2000 GK 111E (EPSG:4547)</option>
                                <option value="EPSG:3857">Web Mercator (EPSG:3857)</option>
                                <option value="EPSG:32648">UTM Zone 48N (EPSG:32648)</option>
                            </select>
                        </div>
                    `;
                    break;
                case 'integrity':
                    formHTML = `
                        <div class="form-group">
                            <label class="form-label">字段完整性字段（用于字段存在检查，逗号分隔）</label>
                            <input type="text" class="form-input" id="requiredFields" name="required_fields" placeholder="如 id,name">
                        </div>
                        <div class="form-group">
                            <label class="form-label">非空字段（用于内容不能为空检查，逗号分隔）</label>
                            <input type="text" class="form-input" id="notEmptyFields" name="not_empty_fields" placeholder="如 name,geom">
                        </div>
                    `;
                    break;
                case 'integrity_db':
                    formHTML = `
                        <div class="form-group">
                            <label class="form-label">数据库连接</label>
                            <input type="text" class="form-input" id="dbConnStr" name="db_conn_str" value="postgresql://postgres:postgres@localhost:5432/skgeo-manager" placeholder="数据库连接字符串">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Schema</label>
                            <input type="text" class="form-input" id="dbSchema" name="db_schema" value="public" placeholder="Schema">
                        </div>
                        <div class="form-group">
                            <label class="form-label">表名</label>
                            <input type="text" class="form-input" id="tableName" name="table_name" value="parks" placeholder="表名">
                        </div>
                        <div class="form-group">
                            <label class="form-label">必填字段</label>
                            <input type="text" class="form-input" id="requiredFields" name="required_fields" value="park_id,park_name" placeholder="必填字段，用逗号分隔">
                        </div>
                        <div class="form-group">
                            <label class="form-label">期望数据条数</label>
                            <input type="number" class="form-input" id="expectedCount" name="expected_count" value="3" placeholder="期望数据条数">
                        </div>
                    `;
                    break;
            }

            dynamicForm.innerHTML = formHTML;
        }

                // 表单提交
        document.getElementById('qualityForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const functionType = formData.get('functionSelect');
            
            // 仅非数据库拓扑检查时校验和上传文件
            let uploadResult = null;
            let fileId = null;
            let requestData = {
                check_type: functionType,
                params: {}
            };
            if (functionType !== 'topology_db' && functionType !== 'attribute_check_db' 
                && functionType !== 'spatial_reference_db' && functionType !== 'db_integrity'
                && functionType !== 'logical_consistency') {
                const fileInput = document.getElementById('fileInput');
                if (!fileInput.files || fileInput.files.length === 0) {
                    alert('请上传zip格式的Shapefile包');
                    return;
                }
                const file = fileInput.files[0];
                if (!file.name.endsWith('.zip')) {
                    alert('Shapefile相关功能只支持zip包上传！');
                    return;
                }
                fileId = Date.now().toString();
                const uploadFormData = new FormData();
                uploadFormData.append('file', file);
                uploadFormData.append('file_id', fileId);
                try {
                    const uploadResponse = await fetch('/glff/api/quality/upload/', {
                        method: 'POST',
                        body: uploadFormData
                    });
                    if (!uploadResponse.ok) {
                        throw new Error('文件上传失败');
                    }
                    uploadResult = await uploadResponse.json();
                } catch (error) {
                    console.error('Error:', error);
                    alert('处理失败: ' + error.message);
                    return;
                }
                requestData.file_id = uploadResult.file_id;
            }
            // 构建请求参数
            // const requestData = {
            //     check_type: functionType,
            //     file_id: functionType !== 'topology_db' ? uploadResult.file_id : undefined,
            //     params: {}
            // };
            
            // 根据功能类型添加特定参数
            if (functionType === 'attribute_check') {
                const schema = document.getElementById('schema');
                const idField = document.getElementById('idField');
                if (schema) {
                    try {
                        let parsedSchema = JSON.parse(schema.value);
                        // 递归去除所有 key 和 string value 的首尾空格
                        function trimKeys(obj) {
                            if (Array.isArray(obj)) {
                                return obj.map(trimKeys);
                            } else if (typeof obj === 'object' && obj !== null) {
                                const newObj = {};
                                for (let k in obj) {
                                    let newKey = k.trim();
                                    let v = obj[k];
                                    if (typeof v === 'string') {
                                        newObj[newKey] = v.trim();
                                    } else {
                                        newObj[newKey] = trimKeys(v);
                                    }
                                }
                                return newObj;
                            } else {
                                return obj;
                            }
                        }
                        requestData.params.schema = trimKeys(parsedSchema);
                    } catch (err) {
                        alert('Schema 格式错误，请输入合法 JSON。请将 True/False 改为 true/false。');
                        return;
                    }
                }
                if (idField) requestData.params.id_field = idField.value;
            } else if (functionType === 'attribute_check_db') {
                const dbConnStr = document.getElementById('dbConnStr');
                const tableName = document.getElementById('tableName');
                const dbSchema = document.getElementById('dbSchema');
                const schema = document.getElementById('schema');
                const idField = document.getElementById('idField');
                if (dbConnStr) requestData.params.db_conn_str = dbConnStr.value;
                if (tableName) requestData.params.table_name = tableName.value;
                if (dbSchema) requestData.params.db_schema = dbSchema.value;
                if (schema) {
                    try {
                        let parsedSchema = JSON.parse(schema.value);
                        // 递归去除所有 key 和 string value 的首尾空格
                        function trimKeys(obj) {
                            if (Array.isArray(obj)) {
                                return obj.map(trimKeys);
                            } else if (typeof obj === 'object' && obj !== null) {
                                const newObj = {};
                                for (let k in obj) {
                                    let newKey = k.trim();
                                    let v = obj[k];
                                    if (typeof v === 'string') {
                                        newObj[newKey] = v.trim();
                                    } else {
                                        newObj[newKey] = trimKeys(v);
                                    }
                                }
                                return newObj;
                            } else {
                                return obj;
                            }
                        }
                        requestData.params.schema = trimKeys(parsedSchema);
                    } catch (err) {
                        alert('Schema 格式错误，请输入合法 JSON。请将 True/False 改为 true/false。');
                        return;
                    }
                }
                if (idField) requestData.params.id_field = idField.value;
            } else if (functionType === 'topology') {
                const idField = document.getElementById('idField');
                if (idField) requestData.params.id_field = idField.value;
            } else if (functionType === 'topology_db') {
                const dbConnStr = document.getElementById('dbConnStr');
                const tableName = document.getElementById('tableName');
                const schema = document.getElementById('schema');
                const idField = document.getElementById('idField');
                const geomColumn = document.getElementById('geomColumn');
                const chunkSize = document.getElementById('chunkSize');
                if (dbConnStr) requestData.params.db_conn_str = dbConnStr.value;
                if (tableName) requestData.params.table_name = tableName.value;
                if (schema) requestData.params.schema = schema.value;
                if (idField) requestData.params.id_field = idField.value;
                if (geomColumn) requestData.params.geom_column = geomColumn.value;
                if (chunkSize) requestData.params.chunk_size = parseInt(chunkSize.value);
            } else if (functionType === 'logical_consistency') {
                const suiteRules = document.getElementById('suiteRules');
                const dbConnStr = document.getElementById('dbConnStr');
                const contextInput = document.getElementById('contextInput');
                if (suiteRules) {
                    try {
                        requestData.params.suite_rules = JSON.parse(suiteRules.value);
                    } catch (err) {
                        alert('suite_rules 格式错误，请输入合法 JSON 数组。');
                        return;
                    }
                }
                if (dbConnStr) requestData.params.db_conn_str = dbConnStr.value;
                if (contextInput) {
                    const val = contextInput.value.trim();
                    if (val) {
                        try {
                            requestData.params.context = JSON.parse(val);
                        } catch (err) {
                            alert('context 格式错误，请输入合法 JSON 对象。');
                            return;
                        }
                    } else {
                        requestData.params.context = null;
                    }
                }
            } else if (functionType === 'integrity') {
                const requiredFields = document.getElementById('requiredFields');
                const notEmptyFields = document.getElementById('notEmptyFields');
                if (requiredFields) requestData.params.required_fields = requiredFields.value.split(',').map(f => f.trim()).filter(f => f);
                if (notEmptyFields) requestData.params.not_empty_fields = notEmptyFields.value.split(',').map(f => f.trim()).filter(f => f);
            } else if (functionType === 'spatial_reference') {
                const gcs = document.getElementById('gcs');
                const pcs = document.getElementById('pcs');
                if (gcs) requestData.params.gcs = gcs.value;
                if (pcs) requestData.params.pcs = pcs.value;
            } else if (functionType === 'spatial_reference_db') {
                const dbConnStr = document.getElementById('dbConnStr');
                const tableName = document.getElementById('tableName');
                const dbSchema = document.getElementById('dbSchema');
                const geomColumn = document.getElementById('geomColumn');
                const gcs = document.getElementById('gcs');
                const pcs = document.getElementById('pcs');
                if (dbConnStr) requestData.params.db_conn_str = dbConnStr.value;
                if (tableName) requestData.params.table_name = tableName.value;
                if (dbSchema) requestData.params.db_schema = dbSchema.value;
                if (geomColumn) requestData.params.geom_column = geomColumn.value;
                if (gcs) requestData.params.gcs = gcs.value;
                if (pcs) requestData.params.pcs = pcs.value;
            } else if (functionType === 'db_integrity') {
                const dbConnStr = document.getElementById('dbConnStr');
                const dbSchema = document.getElementById('dbSchema');
                const dbTable = document.getElementById('dbTable');
                const fieldsExists = document.getElementById('fieldsExists');
                const notEmptyFields = document.getElementById('notEmptyFields');
                const dbExpectedCount = document.getElementById('dbExpectedCount');
                const spatialFields = document.getElementById('spatial-fields');
                if (dbConnStr) requestData.params.db_conn_str = dbConnStr.value;
                if (dbSchema) requestData.params.db_schema = dbSchema.value;
                if (dbTable) requestData.params.db_table = dbTable.value;
                if (fieldsExists) requestData.params.fields_exists = fieldsExists.value;
                if (notEmptyFields) requestData.params.not_empty_fields = notEmptyFields.value;
                if (dbExpectedCount && dbExpectedCount.value) requestData.params.db_expected_count = parseInt(dbExpectedCount.value);
                if (spatialFields) requestData.params.spatial_fields = spatialFields.value.split(',').map(f => f.trim()).filter(f => f);
            }
            
            // 提交处理请求
            const processResponse = await fetch('/glff/api/quality/run/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });
            
            if (!processResponse.ok) {
                throw new Error('处理请求失败');
            }
            
            const processResult = await processResponse.json();
            const taskId = processResult.task_id;
            
            // 显示进度条
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('resultContainer').style.display = 'none';

            // 模拟进度
            let progress = 0;
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            // 检查任务状态
            const checkTaskStatus = async () => {
                try {
                    const statusResponse = await fetch(`/glff/api/quality/status/?task_id=${taskId}`);
                    if (statusResponse.ok) {
                        const statusData = await statusResponse.json();
                        if (statusData.status === 'completed') {
                            clearInterval(progressInterval);
                            progressFill.style.width = '100%';
                            progressText.textContent = '检查完成!';
                            setTimeout(() => {
                                showResult(taskId);
                                saveToHistory(functionType);
                            }, 500);
                            return;
                        } else if (statusData.status === 'error') {
                            clearInterval(progressInterval);
                            throw new Error(statusData.error || '检查失败');
                        }
                    }
                } catch (error) {
                    console.error('状态检查错误:', error);
                }
            };

            // 模拟进度并检查任务状态
            const progressInterval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90; // 最多到90%，等待真实完成
                
                progressFill.style.width = progress + '%';
                progressText.textContent = `检查中... ${Math.round(progress)}%`;
                
                // 每2秒检查一次任务状态
                if (progress % 30 === 0) {
                    checkTaskStatus();
                }
            }, 200);
    });

    // 显示结果
    function showResult(taskId) {
        document.getElementById('progressContainer').style.display = 'none';
        document.getElementById('resultContainer').style.display = 'block';
        const resultContent = document.getElementById('resultContent');
        fetch(`/glff/api/quality/download/?task_id=${taskId}`)
            .then(async response => {
                if (!response.ok) {
                    let errorText = await response.text();
                    throw new Error(errorText || '下载失败');
                }
                let text = await response.text();
                text = text.replace(/\bNaN\b/g, 'null');
                let result;
                try {
                    result = JSON.parse(text);
                    // db_integrity 检查结果友好展示
                    if (result && result.checks && Array.isArray(result.checks)) {
                        let statusColor = result.status === 'passed' ? 'var(--success)' : 'var(--error)';
                        let html = `<div style="margin-bottom:1rem;"><span style="font-weight:600;font-size:1.1rem;">总状态：</span><span style="color:${statusColor};font-weight:600;">${result.status === 'passed' ? '通过' : '未通过'}</span></div>`;
                        // 去掉 result.message 的显示
                        html += '<div style="display:flex;flex-wrap:wrap;gap:1rem;">';
                        result.checks.forEach(item => {
                            let itemColor = item.status === 'passed' ? 'var(--success)' : 'var(--error)';
                            html += `<div style="background:#181e2a;padding:1rem;border-radius:8px;min-width:220px;box-shadow:0 2px 8px #0002;">
                               
                                <div style="font-weight:500;">${item.name}</div>
                                <div style="color:${itemColor};font-weight:600;">${item.status === 'passed' ? '通过' : '未通过'}</div>
                                <div style="margin-top:0.5rem;color:var(--text-secondary);font-size:0.95rem;">${item.message || ''}</div>
                            </div>`;
                        });
                        html += '</div>';
                        resultContent.innerHTML = html;
                    } else {
                        // 其他类型原样展示
                        resultContent.innerHTML = `<pre style="color: var(--text-secondary); background: #181e2a; padding: 1rem; border-radius: 8px; font-size: 0.95rem;">${JSON.stringify(result, null, 2)}</pre>`;
                    }
                } catch (err) {
                    resultContent.innerHTML = `<div style="color: var(--error);">结果解析失败: ${err.message}<br><pre>${text}</pre></div>`;
                }
            })
            .catch(error => {
                resultContent.innerHTML = `<div style="color: var(--error);">结果获取失败: ${error.message}</div>`;
            });
    }

    // 下载报告
    async function downloadReport(taskId) {
        try {
            const response = await fetch(`/glff/api/quality/download/?task_id=${taskId}`);
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || '下载失败');
            }
            
            // 获取文件名
            const contentDisposition = response.headers.get('Content-Disposition');
            let filename = 'quality_report.txt';
            if (contentDisposition) {
                const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }
            
            // 创建下载链接
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
        } catch (error) {
            console.error('下载错误:', error);
            alert('下载失败: ' + error.message);
        }
    }

    // 保存到历史记录
    function saveToHistory(functionType) {
        const history = JSON.parse(localStorage.getItem('qualityHistory') || '[]');
        const newTask = {
            function: functionType,
            timestamp: Date.now(),
            status: 'success'
        };
        
        history.unshift(newTask);
        if (history.length > 50) history.pop(); // 保留最近50条
        
        localStorage.setItem('qualityHistory', JSON.stringify(history));
    }

    // 模态框控制
    function openModal(modalId) {
        document.getElementById(modalId).classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    function closeModal(modalId) {
        document.getElementById(modalId).classList.remove('show');
        document.body.style.overflow = 'auto';
    }

    // 点击模态框外部关闭
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeModal(modal.id);
            }
        });
    });

    // 事件监听
    document.getElementById('helpBtn').addEventListener('click', () => {
        openModal('helpModal');
    });

    document.getElementById('historyBtn').addEventListener('click', () => {
        loadHistory();
        openModal('historyModal');
    });

    // 加载历史记录
    function loadHistory() {
        const history = JSON.parse(localStorage.getItem('qualityHistory') || '[]');
        const historyList = document.getElementById('historyList');
        
        historyList.innerHTML = history.length > 0 ? 
            history.slice(0, 10).map(task => `
                <div style="padding: 0.75rem; background: var(--bg-card); border-radius: var(--radius-lg);">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="font-size: 0.875rem; font-weight: 500;">${task.function}</span>
                        <span style="font-size: 0.75rem; color: var(--text-secondary);">${new Date(task.timestamp).toLocaleString()}</span>
                    </div>
                </div>
            `).join('') : '<p style="color: var(--text-secondary); font-size: 0.875rem;">暂无历史记录</p>';
    }

    // 清空历史记录
    function clearHistory() {
        localStorage.removeItem('qualityHistory');
        loadHistory();
    }

    // 点击菜单显示表单，点击按钮构建参数并发起 db_integrity 检查请求，参数与后端一致
    $(".menu-item[data-function='db_integrity']").click(function() {
        $(".quality-form").hide();
        $("#db-integrity-form").show();
    });
    $("#run-db-integrity").click(function() {
        const db_conn = $("#db-conn").val();
        const db_schema = $("#db-schema").val();
        const db_table = $("#db-table").val();
        const db_fields = $("#db-fields").val();
        const db_expected_count = $("#db-expected-count").val();
        const params = {
            check_type: "db_integrity",
            db_conn,
            db_schema,
            db_table,
            db_fields,
            db_expected_count
        };
        runQualityCheck(params);
    });

    // 质量检查请求
    function runQualityCheck(params) {
        // 这里可以添加请求逻辑
        console.log('发起质量检查请求', params);
    }

    // 修正参数构建逻辑，确保 spatial_fields 传递给后台
    function buildDbIntegrityParams() {
        const dbConnStr = document.getElementById('dbConnStr').value.trim();
        const dbSchema = document.getElementById('dbSchema').value.trim();
        const dbTable = document.getElementById('dbTable').value.trim();
        const fieldsExists = document.getElementById('fieldsExists').value.trim();
        const notEmptyFields = document.getElementById('notEmptyFields').value.trim();
        const dbExpectedCount = document.getElementById('dbExpectedCount').value.trim();
        const spatialFieldsRaw = document.getElementById('spatial-fields').value.trim();
        const spatialFields = spatialFieldsRaw ? spatialFieldsRaw.split(',').map(f => f.trim()).filter(f => f) : [];
        return {
            db_conn_str: dbConnStr,
            db_schema: dbSchema,
            db_table: dbTable,
            fields_exists: fieldsExists,
            not_empty_fields: notEmptyFields,
            db_expected_count: dbExpectedCount,
            spatial_fields: spatialFields
        };
    }
    </script>
</body>
</html>
