# -*- coding: utf-8 -*-
"""
拓扑关系检查
本模块提供了一套用于对点、线、面数据进行拓扑关系检查的专业工具。
"""

import geopandas as gpd
from shapely.geometry import Polygon, MultiPolygon, LineString, Point
from shapely import union_all
# MODIFICATION: Import GEOSException, the correct base class for this error in modern Shapely
from shapely.errors import GEOSException
from typing import Dict, Any, List, Tuple
import os
import shutil
import json
import warnings
import numpy as np
import pandas as pd
import networkx as nx


# --- 辅助类 ---
class CheckResult:
    def __init__(self, status: str, message: str, problematic_ids: List[Any] = None):
        if status not in ['PASSED', 'FAILED', 'NOT_APPLICABLE', 'ERROR']: raise ValueError(
            "状态必须是 'PASSED', 'FAILED', 'NOT_APPLICABLE', 或 'ERROR' 之一")
        self.status, self.message = status, message
        self.problematic_ids = [self._convert_to_native_type(item) for item in
                                problematic_ids] if problematic_ids else []

    def _convert_to_native_type(self, item):
        if isinstance(item, (np.integer, np.int64, np.int32)): return int(item)
        if isinstance(item, (np.floating, np.float64, np.float32)): return float(item)
        return item

    def to_dict(self) -> Dict[str, Any]:
        return {"status": self.status, "message": self.message, "failed_count": len(self.problematic_ids),
                "failed_ids_preview": self.problematic_ids[:100]}

    def __repr__(self) -> str:
        return f"CheckResult(status='{self.status}', message='{self.message}', count={len(self.problematic_ids)})"


class TopologyChecker:
    SUPPORTED_RULES = {
        'must_be_valid': {'description': '检查所有几何对象是否有效 (例如，面不自相交)。',
                          'types': ['Polygon', 'MultiPolygon', 'LineString', 'MultiLineString', 'Point', 'MultiPoint']},
        'must_not_overlap': {'description': '检查同层内的面要素之间是否互相重叠。',
                             'types': ['Polygon', 'MultiPolygon']},
        'must_not_have_gaps': {'description': '检查同层内的面要素之间是否存在缝隙。',
                               'types': ['Polygon', 'MultiPolygon']},
        'must_not_self_intersect': {'description': '检查线要素是否自相交。', 'types': ['LineString', 'MultiLineString']},
        'must_not_intersect': {'description': '检查同层内的线要素之间是否互相交叉 (端点接触除外)。',
                               'types': ['LineString', 'MultiLineString']},
        'must_not_have_dangles': {'description': '检查是否存在孤立悬挂线 (两个端点均未连接到其他线)。',
                                  'types': ['LineString', 'MultiLineString']},
        'must_be_unique_points': {'description': '检查点要素是否存在坐标完全重复的点。',
                                  'types': ['Point', 'MultiPoint']}
    }

    def __init__(self, geo_data: gpd.GeoDataFrame, id_field: str = None):
        if not isinstance(geo_data, gpd.GeoDataFrame) or geo_data.empty: raise ValueError(
            "输入的必须是一个非空的GeoDataFrame。")
        self.gdf = geo_data.copy()
        if id_field and id_field in self.gdf.columns:
            self.id_field = id_field
        else:
            self.id_field = self.gdf.index.name if self.gdf.index.name else 'index'
            if self.id_field == 'index': self.gdf['index'] = self.gdf.index
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", UserWarning);
            self.sindex = self.gdf.sindex
        self.geom_type = self.gdf.geom_type.mode()[0] if not self.gdf.empty else None

    # --- 类方法 ---
    @classmethod
    def from_file(cls, file_path: str, id_field: str = None, **kwargs):
        if not os.path.exists(file_path): raise FileNotFoundError(f"错误：找不到文件 '{file_path}'")
        try:
            gdf = gpd.read_file(file_path, **kwargs);
            return cls(gdf, id_field=id_field)
        except Exception as e:
            raise Exception(f"错误：读取或解析文件 '{file_path}' 失败。底层错误: {e}")

    @classmethod
    def from_geojson_string(cls, geojson_string: str, id_field: str = None):
        if not geojson_string or not isinstance(geojson_string, str): raise ValueError(
            "错误：输入的GeoJSON字符串不能为空。")
        try:
            gdf = gpd.read_file(geojson_string);
            return cls(gdf, id_field=id_field)
        except Exception as e:
            raise Exception(f"错误：解析GeoJSON字符串失败。请确保格式正确。底层错误: {e}")

    @classmethod
    def from_database(cls, connection_string: str, table_name: str, schema: str = None, geom_column: str = 'geometry',
                      id_field: str = None, where: str = None, chunk_size: int = None):
        from sqlalchemy import create_engine
        engine = create_engine(connection_string)
        full_table = f'{schema}.{table_name}' if schema else table_name
        sql = f"SELECT * FROM {full_table}"
        if where:
            sql += f" WHERE {where}"
        if chunk_size:
            chunks = gpd.read_postgis(sql, engine, geom_col=geom_column, chunksize=chunk_size)
            return cls._from_db_chunks(chunks, id_field)
        else:
            gdf = gpd.read_postgis(sql, engine, geom_col=geom_column)
            return cls(gdf, id_field=id_field)

    @classmethod
    def _from_db_chunks(cls, chunks, id_field):
        obj = cls.__new__(cls)
        obj.gdf = None
        obj.id_field = id_field
        obj._db_chunks = chunks
        obj._is_streaming = True
        return obj

    def run_checks(self, rules: List[str] = None) -> Tuple[Dict[str, Any], Dict[str, gpd.GeoDataFrame]]:
        if rules is None: rules = [name for name, info in self.SUPPORTED_RULES.items() if
                                   self.geom_type in info['types'] or self.gdf.geom_type.isin(info['types']).any()]
        final_report = {"overall_status": "通过", "checks": {}}
        failed_gdfs_by_rule = {}
        for rule_name in rules:
            result = self._execute_single_check(rule_name)
            report_item = result.to_dict()
            applicable_gdf = self._get_applicable_gdf(rule_name)
            total_applicable = len(applicable_gdf)
            report_item['total_applicable'] = total_applicable
            report_item['passed_count'] = total_applicable - report_item['failed_count']
            final_report["checks"][rule_name] = report_item
            if result.status == 'FAILED':
                failed_gdf = applicable_gdf[applicable_gdf[self.id_field].isin(result.problematic_ids)].copy()
                failed_gdfs_by_rule[rule_name] = failed_gdf
        if failed_gdfs_by_rule:
            final_report['overall_status'] = '失败'
        elif any(r['status'] == 'ERROR' for r in final_report["checks"].values()):
            final_report['overall_status'] = '错误'
        return final_report, failed_gdfs_by_rule

    def run_checks_streaming(self, rules: List[str] = None):
        if not getattr(self, '_is_streaming', False):
            raise RuntimeError('当前对象不是分批数据库模式。')
        overall_report = {"overall_status": "通过", "checks": {}}
        failed_gdfs_by_rule = {}
        total_counts = {}
        passed_counts = {}
        failed_ids = {}
        for chunk in self._db_chunks:
            checker = TopologyChecker(chunk, id_field=self.id_field)
            report, failed_gdfs = checker.run_checks(rules)
            for rule, item in report['checks'].items():
                if rule not in overall_report['checks']:
                    overall_report['checks'][rule] = item.copy()
                    total_counts[rule] = item['total_applicable']
                    passed_counts[rule] = item['passed_count']
                    failed_ids[rule] = set(item.get('failed_ids_preview', []))
                else:
                    total_counts[rule] += item['total_applicable']
                    passed_counts[rule] += item['passed_count']
                    failed_ids[rule].update(item.get('failed_ids_preview', []))
            for rule, gdf in failed_gdfs.items():
                if rule not in failed_gdfs_by_rule:
                    failed_gdfs_by_rule[rule] = gdf.copy()
                else:
                    failed_gdfs_by_rule[rule] = pd.concat([failed_gdfs_by_rule[rule], gdf], ignore_index=True)
        for rule in overall_report['checks']:
            overall_report['checks'][rule]['total_applicable'] = total_counts[rule]
            overall_report['checks'][rule]['passed_count'] = passed_counts[rule]
            overall_report['checks'][rule]['failed_count'] = total_counts[rule] - passed_counts[rule]
            overall_report['checks'][rule]['failed_ids_preview'] = list(failed_ids[rule])[:100]
        if failed_gdfs_by_rule:
            overall_report['overall_status'] = '失败'
        elif any(r['status'] == 'ERROR' for r in overall_report["checks"].values()):
            overall_report['overall_status'] = '错误'
        return overall_report, failed_gdfs_by_rule

    # --- 内部辅助方法 ---
    def _get_applicable_gdf(self, rule_name: str) -> gpd.GeoDataFrame:
        rule_info = self.SUPPORTED_RULES.get(rule_name, {})
        applicable_types = rule_info.get('types', [])
        return self.gdf[self.gdf.geom_type.isin(applicable_types)]

    def _execute_single_check(self, rule_name: str) -> CheckResult:
        applicable_gdf = self._get_applicable_gdf(rule_name)
        if applicable_gdf.empty:
            return CheckResult('NOT_APPLICABLE', f"规则 '{rule_name}' 不适用于当前数据集中的任何几何类型。")
        check_method = getattr(self, f"_check_{rule_name}", None)
        if check_method:
            try:
                return check_method(applicable_gdf)
            except Exception as e:
                return CheckResult('ERROR', f"规则 '{rule_name}' 执行时发生未知严重错误: {e}")
        else:
            return CheckResult('ERROR', f"未找到规则 '{rule_name}' 的内部实现方法。")

    def _check_must_be_valid(self, polygons: gpd.GeoDataFrame) -> CheckResult:
        invalid_mask = ~polygons.geometry.is_valid
        problematic_ids = polygons[invalid_mask][self.id_field].tolist()
        return CheckResult('FAILED', '发现无效的几何对象。', problematic_ids) if problematic_ids else CheckResult(
            'PASSED', '所有几何对象均有效。')

    def _check_must_not_overlap(self, polygons: gpd.GeoDataFrame) -> CheckResult:
        problematic_ids = set()
        if len(polygons) < 2: return CheckResult('NOT_APPLICABLE', '面要素少于2个，无法进行重叠检查。')
        sindex = polygons.sindex
        left_indices, right_indices = sindex.query(polygons.geometry, predicate='intersects')
        matches = left_indices < right_indices
        left_indices, right_indices = left_indices[matches], right_indices[matches]
        for i, j in zip(left_indices, right_indices):
            geom_i, geom_j = polygons.geometry.iloc[i], polygons.geometry.iloc[j]
            try:
                if geom_i.overlaps(geom_j):
                    problematic_ids.add(polygons[self.id_field].iloc[i]);
                    problematic_ids.add(polygons[self.id_field].iloc[j])
            # MODIFICATION: Catch the correct, more specific GEOSException
            except GEOSException:
                problematic_ids.add(polygons[self.id_field].iloc[i]);
                problematic_ids.add(polygons[self.id_field].iloc[j])
        return CheckResult('FAILED', '发现互相重叠的面要素或存在拓扑错误。',
                           sorted(list(problematic_ids))) if problematic_ids else CheckResult('PASSED',
                                                                                              '面要素之间无重叠。')

    # --- SURGICAL FIX in place, now with correct Exception handling ---
    def _check_must_not_have_gaps(self, polygons: gpd.GeoDataFrame) -> CheckResult:
        all_problematic_ids = set()
        message_parts = []

        valid_polygons = polygons[polygons.geometry.is_valid].copy()
        if len(valid_polygons) < 2:
            return CheckResult('NOT_APPLICABLE', '有效的面要素少于2个，无法进行缝隙检查。')

        cleaned_data = []
        for _, row in valid_polygons.iterrows():
            try:
                cleaned_geom = row.geometry.buffer(0)
                if isinstance(cleaned_geom, (Polygon, MultiPolygon)) and not cleaned_geom.is_empty:
                    cleaned_data.append(
                        {'geometry': cleaned_geom, self.id_field: row[self.id_field], 'original_index': row.name})
                else:
                    all_problematic_ids.add(row[self.id_field])
            # MODIFICATION: Catch the correct, more specific GEOSException
            except (GEOSException, ValueError):
                all_problematic_ids.add(row[self.id_field])

        if not cleaned_data:
            return CheckResult('FAILED', '所有几何体都存在退化或无法修复的拓扑错误。',
                               sorted(list(all_problematic_ids))) if all_problematic_ids else CheckResult('PASSED',
                                                                                                          '所有面要素组内部均未发现缝隙。')

        cleaned_gdf = gpd.GeoDataFrame(cleaned_data, crs=polygons.crs).set_index('original_index')

        sindex = cleaned_gdf.sindex
        good_indices = []
        unqueryable_ids = set()
        for index, row in cleaned_gdf.iterrows():
            try:
                sindex.query(row.geometry.boundary, predicate='intersects')
                good_indices.append(index)
            # MODIFICATION: Catch the correct, more specific GEOSException
            except GEOSException:
                unqueryable_ids.add(row[self.id_field])

        if unqueryable_ids:
            message_parts.append(f"发现 {len(unqueryable_ids)} 个几何体存在严重拓扑错误, 导致无法进行空间查询")
            all_problematic_ids.update(unqueryable_ids)
            cleaned_gdf = cleaned_gdf.loc[good_indices]

        if len(cleaned_gdf) < 2:
            if all_problematic_ids:
                return CheckResult('FAILED', "。".join(message_parts) + '。', sorted(list(all_problematic_ids)))
            else:
                return CheckResult('PASSED', '所有面要素组内部均未发现缝隙。')

        sindex = cleaned_gdf.sindex
        left_indices, right_indices = sindex.query(cleaned_gdf.geometry, predicate='touches')

        graph = nx.Graph()
        graph.add_nodes_from(cleaned_gdf.index)
        edges = [(cleaned_gdf.index[i], cleaned_gdf.index[j]) for i, j in zip(left_indices, right_indices) if i != j]
        graph.add_edges_from(edges)

        components = [list(c) for c in nx.connected_components(graph) if len(c) > 1]

        gap_ids = set()
        for component_indices in components:
            component_gdf = cleaned_gdf.loc[component_indices]
            try:
                unified_polygon = union_all(component_gdf.geometry.tolist())
                if unified_polygon.is_empty: continue

                coverage_hull = unified_polygon.convex_hull
                gaps = coverage_hull.difference(unified_polygon)

                if not gaps.is_empty:
                    gap_geoms = list(gaps.geoms) if hasattr(gaps, 'geoms') else [gaps]
                    component_sindex = component_gdf.sindex
                    for gap_poly in gap_geoms:
                        if isinstance(gap_poly, Polygon) and gap_poly.area > 1e-9:
                            touching_indices = component_sindex.query(gap_poly, predicate='touches')
                            if len(touching_indices) > 0:
                                bordering_indices = component_gdf.iloc[touching_indices].index.tolist()
                                bordering_ids = cleaned_gdf.loc[bordering_indices][self.id_field].tolist()
                                gap_ids.update(bordering_ids)
            # MODIFICATION: Catch the correct, more specific GEOSException
            except (GEOSException, ValueError):
                gap_ids.update(component_gdf[self.id_field].tolist())

        if gap_ids:
            message_parts.append(f"在部分几何体周围发现缝隙或拓扑错误")
            all_problematic_ids.update(gap_ids)

        if all_problematic_ids:
            final_message = "。".join(message_parts) + '。' if message_parts else '发现缝隙或拓扑错误。'
            return CheckResult('FAILED', final_message, sorted(list(all_problematic_ids)))
        else:
            return CheckResult('PASSED', '所有面要素组内部均未发现缝隙。')

    def _check_must_not_self_intersect(self, lines: gpd.GeoDataFrame) -> CheckResult:
        non_simple_mask = ~lines.geometry.is_simple
        problematic_ids = lines[non_simple_mask][self.id_field].tolist()
        return CheckResult('FAILED', '发现自相交的线要素。', problematic_ids) if problematic_ids else CheckResult(
            'PASSED', '所有线要素均未自相交。')

    def _check_must_not_intersect(self, lines: gpd.GeoDataFrame) -> CheckResult:
        problematic_ids = set()
        if len(lines) < 2: return CheckResult('NOT_APPLICABLE', '线要素少于2个，无法进行交叉检查。')
        sindex = lines.sindex
        left_indices, right_indices = sindex.query(lines.geometry, predicate='intersects')
        matches = left_indices < right_indices
        left_indices, right_indices = left_indices[matches], right_indices[matches]
        for i, j in zip(left_indices, right_indices):
            try:
                if lines.geometry.iloc[i].crosses(lines.geometry.iloc[j]):
                    problematic_ids.add(lines[self.id_field].iloc[i]);
                    problematic_ids.add(lines[self.id_field].iloc[j])
            # MODIFICATION: Catch the correct, more specific GEOSException
            except GEOSException:
                problematic_ids.add(lines[self.id_field].iloc[i]);
                problematic_ids.add(lines[self.id_field].iloc[j])
        return CheckResult('FAILED', '发现互相交叉的线要素或存在拓扑错误。',
                           sorted(list(problematic_ids))) if problematic_ids else CheckResult('PASSED',
                                                                                              '线要素之间无交叉。')

    def _check_must_not_have_dangles(self, lines: gpd.GeoDataFrame) -> CheckResult:
        if len(lines) < 2: return CheckResult('NOT_APPLICABLE', '线要素少于2个，无法进行悬挂检查。')
        endpoints_data = []
        for idx, row in lines.iterrows():
            geom = row.geometry
            line_parts = geom.geoms if geom.geom_type == 'MultiLineString' else [geom]
            for part in line_parts:
                if part.is_empty or len(part.coords) < 2: continue
                endpoints_data.append({'line_idx': idx, 'geometry': Point(part.coords[0])})
                endpoints_data.append({'line_idx': idx, 'geometry': Point(part.coords[-1])})

        if not endpoints_data: return CheckResult('PASSED', '未发现可处理的线要素端点。')
        endpoints_gdf = gpd.GeoDataFrame(endpoints_data, crs=lines.crs)
        endpoints_gdf['wkb'] = endpoints_gdf.geometry.apply(lambda g: g.wkb)

        potential_connections_gdf = endpoints_gdf[endpoints_gdf.wkb.duplicated(keep=False)]
        if potential_connections_gdf.empty:
            connected_endpoints_wkb = set()
        else:
            connection_counts = potential_connections_gdf.groupby('wkb')['line_idx'].nunique()
            true_connection_wkb = connection_counts[connection_counts > 1].index.tolist()
            connected_endpoints_wkb = set(true_connection_wkb)

        problematic_ids = []
        for idx, row in lines.iterrows():
            geom = row.geometry
            line_parts = geom.geoms if geom.geom_type == 'MultiLineString' else [geom]
            is_dangle = True
            endpoints_of_this_line = []
            for part in line_parts:
                if part.is_empty or len(part.coords) < 2: continue
                endpoints_of_this_line.extend([Point(part.coords[0]), Point(part.coords[-1])])

            if not endpoints_of_this_line: continue
            for pt in endpoints_of_this_line:
                if pt.wkb in connected_endpoints_wkb: is_dangle = False; break
            if is_dangle: problematic_ids.append(row[self.id_field])
        return CheckResult('FAILED', '发现孤立悬挂线 (所有端点均未连接到其他线)。',
                           sorted(problematic_ids)) if problematic_ids else CheckResult('PASSED', '未发现孤立悬挂线。')

    def _check_must_be_unique_points(self, points: gpd.GeoDataFrame) -> CheckResult:
        duplicate_mask = points.geometry.apply(lambda g: g.wkb).duplicated(keep=False)
        problematic_ids = points[duplicate_mask][self.id_field].tolist()
        return CheckResult('FAILED', '发现坐标重复的点要素。', problematic_ids) if problematic_ids else CheckResult(
            'PASSED', '所有点要素坐标唯一。')


# --- 测试与演示部分 (保持不变) ---
def setup_test_environment(version="v4.1"):
    TEST_DIR = f"test_topology_temp_{version}"
    if os.path.exists(TEST_DIR): shutil.rmtree(TEST_DIR)
    os.makedirs(TEST_DIR)
    return TEST_DIR


def setup_polygon_data():
    data = [
        {'id': 1, 'desc': 'overlap_A', 'geometry': Polygon([(0, 0), (0, 2), (2, 2), (2, 0)])},
        {'id': 2, 'desc': 'overlap_B', 'geometry': Polygon([(1, 1), (1, 3), (3, 3), (3, 1)])},
        {'id': 3, 'desc': 'valid_poly', 'geometry': Polygon([(10, 10), (10, 12), (12, 12), (12, 10)])},
        {'id': 4, 'desc': 'invalid_bowtie', 'geometry': Polygon([(20, 20), (22, 22), (20, 22), (22, 20), (20, 20)])},
        {'id': 5, 'desc': 'gap_former_A', 'geometry': Polygon([(30, 31), (30, 32), (31, 32), (31, 31)])},
        {'id': 6, 'desc': 'gap_former_B', 'geometry': Polygon([(32, 31), (32, 32), (33, 32), (33, 31)])},
        {'id': 7, 'desc': 'island_poly', 'geometry': Polygon([(40, 40), (40, 41), (41, 41), (41, 40)])},
        {'id': 8, 'desc': 'gap_bridge', 'geometry': Polygon([(30, 30), (33, 30), (33, 31), (30, 31)])}, ]
    return gpd.GeoDataFrame(data, crs="EPSG:3857")


def setup_line_data():
    data = [
        {'id': 10, 'desc': 'self_intersect', 'geometry': LineString([(0, 0), (2, 2), (0, 2), (2, 0)])},
        {'id': 11, 'desc': 'intersect_A', 'geometry': LineString([(5, 5), (7, 7)])},
        {'id': 12, 'desc': 'intersect_B', 'geometry': LineString([(5, 7), (7, 5)])},
        {'id': 13, 'desc': 'dangle_line_1', 'geometry': LineString([(10, 10), (11, 11)])},
        {'id': 14, 'desc': 'connected_A', 'geometry': LineString([(15, 15), (16, 16)])},
        {'id': 15, 'desc': 'connected_B', 'geometry': LineString([(16, 16), (17, 15)])},
        {'id': 16, 'desc': 'dangle_line_2', 'geometry': LineString([(20, 20), (21, 21)])}, ]
    return gpd.GeoDataFrame(data, crs="EPSG:3857")


def setup_point_data():
    data = [
        {'id': 20, 'desc': 'duplicate_A', 'geometry': Point(0, 0)},
        {'id': 21, 'desc': 'duplicate_B', 'geometry': Point(0, 0)},
        {'id': 22, 'desc': 'unique_point', 'geometry': Point(5, 5)},
        {'id': 23, 'desc': 'duplicate_C', 'geometry': Point(1, 1)},
        {'id': 24, 'desc': 'duplicate_D', 'geometry': Point(1, 1)}, ]
    return gpd.GeoDataFrame(data, crs="EPSG:3857")


def run_demonstration_and_tests():
    print("=" * 60)
    print("  高性能拓扑关系检查器 v4.1 - 演示与自动化测试")
    print("=" * 60)
    TEST_DIR = setup_test_environment()

    # --- 案例 1: 面数据检查 ---
    print("\n" + "#" * 15 + " 案例 1: 面数据检查 (Polygons) " + "#" * 15)
    poly_gdf = setup_polygon_data()
    poly_shp_path = os.path.join(TEST_DIR, 'polygons.shp')
    poly_gdf.to_file(poly_shp_path, driver='ESRI Shapefile', encoding='utf-8')
    checker_poly = TopologyChecker.from_file(poly_shp_path, id_field='id')
    report_poly, failed_poly_gdfs = checker_poly.run_checks()

    print("--> 1.1 总报告:")
    print(json.dumps(report_poly, indent=4, ensure_ascii=False))

    print("\n--> 1.2 按规则分类的失败要素:")
    for rule, gdf in failed_poly_gdfs.items():
        print(f"    - 规则 '{rule}' 失败: {len(gdf)} 个要素, IDs: {sorted(gdf['id'].tolist())}")

    # --- 案例 2: 线数据检查 ---
    print("\n" + "#" * 15 + " 案例 2: 线数据检查 (Lines) " + "#" * 15)
    line_gdf = setup_line_data()
    line_geojson_path = os.path.join(TEST_DIR, 'lines.geojson')
    line_gdf.to_file(line_geojson_path, driver='GeoJSON')
    checker_line = TopologyChecker.from_file(line_geojson_path, id_field='id')
    report_line, failed_line_gdfs = checker_line.run_checks()

    print("--> 2.1 总报告:")
    print(json.dumps(report_line, indent=4, ensure_ascii=False))

    print("\n--> 2.2 按规则分类的失败要素:")
    for rule, gdf in failed_line_gdfs.items():
        print(f"    - 规则 '{rule}' 失败: {len(gdf)} 个要素, IDs: {sorted(gdf['id'].tolist())}")

    # --- 案例 3: 点数据检查 ---
    print("\n" + "#" * 15 + " 案例 3: 点数据检查 (Points) " + "#" * 15)
    point_gdf = setup_point_data()
    point_geojson_str = point_gdf.to_json()
    checker_point = TopologyChecker.from_geojson_string(point_geojson_str, id_field='id')
    report_point, failed_point_gdfs = checker_point.run_checks()

    print("--> 3.1 总报告:")
    print(json.dumps(report_point, indent=4, ensure_ascii=False))

    print("\n--> 3.2 按规则分类的失败要素:")
    for rule, gdf in failed_point_gdfs.items():
        print(f"    - 规则 '{rule}' 失败: {len(gdf)} 个要素, IDs: {sorted(gdf['id'].tolist())}")

    # --- 案例 4: 数据库空间表检查 ---
    postgis_conn = "postgresql://postgres:postgres@localhost:5432/skgeo-manager"
    table = "spatial_table"
    schema = 'geodata'
    chunk_size = 10000
    try:
        checker_db = TopologyChecker.from_database(postgis_conn, table, schema, geom_column='geometry', id_field='id',
                                                   chunk_size=chunk_size)
        if getattr(checker_db, '_is_streaming', False):
            report_db, failed_db_gdfs = checker_db.run_checks_streaming()
        else:
            report_db, failed_db_gdfs = checker_db.run_checks()
        print("\n" + "#" * 15 + " 案例 4: 数据库空间表检查 " + "#" * 15)
        print("--> 4.1 数据库空间表检查报告:")
        print(json.dumps(report_db, indent=4, ensure_ascii=False))
        for rule, gdf in failed_db_gdfs.items():
            print(f"    - 规则 '{rule}' 失败: {len(gdf)} 个要素, IDs: {sorted(gdf['id'].tolist())}")
    except Exception as e:
        print("\n" + "#" * 15 + " 案例 4: 数据库空间表检查 (跳过) " + "#" * 15)
        print(f"--> 跳过数据库检查，因为连接失败或表不存在: {e}")

    # --- 清理 ---
    print("\n[清理] 删除临时测试文件和目录...")
    shutil.rmtree(TEST_DIR)
    print("清理完成。")

    print("\n" + "=" * 60)
    print("🎉 所有演示与自动化测试全部通过！")
    print("=" * 60)


if __name__ == '__main__':
    run_demonstration_and_tests()
