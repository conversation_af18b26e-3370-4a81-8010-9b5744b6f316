## 一、docker部署
### 1.构建镜像
    docker build -t encrypted-django-app .
### 2.docker启动
    docker run -d -p 8000:8000 encrypted-django-app



## 二、加密编译部署
###  加密
     pyarmor gen  -O dist3 -r   ./
### 运行加密后的程序
    进入dist下执行python310 .\manage.py runserver


## 三、生成和更新requirements.txt
    pip3.10 freeze > requirements.txt  
    生成完后记得修改下GDAl和numpy的引用地址，这里执行完命令后是指向本地的
    pip3.10 download -r requirements.txt -d ./packages
    下载依赖包到本地目录
    pip3.10 install --no-index --find-links=./packages -r requirements.txt
    在离线环境中安装依赖
    