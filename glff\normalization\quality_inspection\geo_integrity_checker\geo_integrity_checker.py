# -*- coding: utf-8 -*-
"""
Shapefile 数据完整性检查器 (SHP Integrity Checker)

本模块提供了一套专门用于检查 Shapefile 数据完整性的工具。
它不处理空间参考，只专注于文件结构、内容和属性的完整性。

核心功能:
1. 内容完整性检查:
   - 检查核心文件 (.shp, .shx, .dbf) 是否齐全。
   - 检查数据集是否为空。
   - 检查是否存在空几何或无效几何。
2. 属性完整性检查:
   - 动态检查是否存在用户通过列表指定的一组必填字段。

作者: GQE (Geo-Quality Engine) 专家团队
版本: 1.2 (Fixed test creation for pyogrio engine compatibility)
"""

import geopandas as gpd
from shapely.geometry import Polygon, Point
from typing import Dict, Any, Optional, List
import os
import shutil


# --- 辅助类 (用于标准化报告) ---
class CheckResult:
    """一个用于封装单项检查结果的标准化数据结构。"""

    def __init__(self, status: str, message: str, details: Optional[Dict[str, Any]] = None):
        if status not in ['PASSED', 'FAILED', 'ERROR']:
            raise ValueError("状态必须是 'PASSED', 'FAILED', 或 'ERROR' 之一")
        self.status = status
        self.message = message
        self.details = details or {}

    def to_dict(self) -> Dict[str, Any]:
        """将结果对象转换为字典，便于序列化。"""
        return {"status": self.status, "message": self.message, "details": self.details}

    def __repr__(self) -> str:
        """提供一个易于调试的字符串表示。"""
        return f"CheckResult(status='{self.status}', message='{self.message}')"


# --- 主检查器类 ---
class ShpIntegrityChecker:
    """
    一个专门用于执行Shapefile数据完整性检查的类。
    """

    def check(self, filepath: str, required_fields: List[str], not_empty_fields: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        对指定的Shapefile执行全面的数据完整性检查。
        新增支持 not_empty_fields 非空字段检查。
        """
        report = {
            "filepath": filepath,
            "overall_status": "通过",
            "checks": {}
        }

        if not filepath.lower().endswith('.shp'):
            report['overall_status'] = '错误'
            report['checks']['file_type_check'] = CheckResult('ERROR',
                                                              '输入文件不是一个Shapefile (.shp) 文件。').to_dict()
            return report

        file_completeness_result = self._check_files_completeness(filepath)
        report['checks']['content_file_completeness'] = file_completeness_result.to_dict()
        if file_completeness_result.status != 'PASSED':
            report['overall_status'] = '失败'
            return report

        try:
            gdf = gpd.read_file(filepath)
        except Exception as e:
            report['overall_status'] = '错误'
            report['checks']['content_file_readability'] = CheckResult('ERROR', f'读取Shapefile失败: {e}').to_dict()
            return report

        empty_dataset_result = self._check_is_empty_dataset(gdf)
        report['checks']['content_empty_dataset'] = empty_dataset_result.to_dict()
        if empty_dataset_result.status != 'PASSED':
            report['overall_status'] = '失败'
            return report

        geometry_validity_result = self._check_geometries(gdf)
        report['checks']['content_geometry_validity'] = geometry_validity_result.to_dict()

        attribute_fields_result = self._check_required_fields(gdf, required_fields)
        report['checks']['attribute_required_fields'] = attribute_fields_result.to_dict()

        not_empty_fields = not_empty_fields or []
        attribute_not_empty_result = self._check_not_empty_fields(gdf, not_empty_fields)
        report['checks']['attribute_not_empty_fields'] = attribute_not_empty_result.to_dict()

        all_integrity_results = [
            file_completeness_result, empty_dataset_result,
            geometry_validity_result, attribute_fields_result, attribute_not_empty_result
        ]
        if any(r.status == 'FAILED' for r in all_integrity_results):
            report['overall_status'] = '失败'

        return report

    def _check_files_completeness(self, filepath: str) -> CheckResult:
        base_path = os.path.splitext(filepath)[0]
        required_exts = ['.shp', '.shx', '.dbf']
        missing_files = []
        for ext in required_exts:
            if not os.path.exists(base_path + ext):
                missing_files.append(os.path.basename(base_path + ext))
        if missing_files:
            return CheckResult('FAILED', 'Shapefile文件不完整，缺少核心组件。', {'missing_files': missing_files})
        found_files = [os.path.basename(base_path + ext) for ext in required_exts]
        prj_path = base_path + '.prj'
        if not os.path.exists(prj_path):
            message = '核心文件齐全，但缺少推荐的投影文件(.prj)。'
            details = {'found_files': found_files, 'warning': '缺少.prj文件可能导致空间参考信息丢失或不明确。'}
        else:
            message = 'Shapefile核心文件齐全。'
            found_files.append(os.path.basename(prj_path))
            details = {'found_files': found_files}
        return CheckResult('PASSED', message, details)

    def _check_is_empty_dataset(self, gdf: gpd.GeoDataFrame) -> CheckResult:
        if gdf.empty:
            return CheckResult('FAILED', '数据集为空，不包含任何要素。')
        return CheckResult('PASSED', f'数据集包含 {len(gdf)} 个要素。')

    def _check_geometries(self, gdf: gpd.GeoDataFrame) -> CheckResult:
        if not hasattr(gdf, 'geometry') or not isinstance(gdf.geometry, gpd.GeoSeries):
            return CheckResult('ERROR', "GeoDataFrame中缺少有效的几何列。")
        empty_geoms = gdf.geometry.is_empty
        invalid_geoms = ~gdf.geometry.is_valid
        empty_indices = gdf[empty_geoms].index.tolist()
        invalid_indices = gdf[invalid_geoms & ~empty_geoms].index.tolist()
        if not empty_indices and not invalid_indices:
            return CheckResult('PASSED', '所有几何要素均非空且有效。')
        details = {}
        message_parts = []
        if empty_indices:
            details['empty_geometry_indices'] = empty_indices[:10]
            details['empty_geometry_count'] = len(empty_indices)
            message_parts.append(f"发现 {len(empty_indices)} 个空几何")
        if invalid_indices:
            details['invalid_geometry_indices'] = invalid_indices  # 返回全部无效几何索引
            details['invalid_geometry_count'] = len(invalid_indices)
            message_parts.append(f"发现 {len(invalid_indices)} 个无效几何")
        message = "数据集中存在问题几何： " + "； ".join(message_parts) + "。"
        return CheckResult('FAILED', message, details)

    def _check_required_fields(self, gdf: gpd.GeoDataFrame, required_fields: List[str]) -> CheckResult:
        if not required_fields:
            return CheckResult('PASSED', '未指定需要检查的必填字段，跳过此项检查。')
        actual_required_cols = [f for f in required_fields if f.lower() != 'geometry']
        data_columns = set(gdf.columns)
        missing_fields = [f for f in actual_required_cols if f not in data_columns]
        geometry_required = 'geometry' in [f.lower() for f in required_fields]
        if not missing_fields:
            message = '所有指定的必填属性字段都存在。'
            if geometry_required:
                message += f" 要求的几何列 '{gdf.geometry.name}' 也存在。"
            return CheckResult('PASSED', message)
        else:
            return CheckResult('FAILED', '属性表缺少部分必填字段。',
                               {'missing_fields': missing_fields, 'required_fields': required_fields,
                                'all_data_fields': list(gdf.columns)})

    def _check_not_empty_fields(self, gdf: gpd.GeoDataFrame, not_empty_fields: List[str]) -> CheckResult:
        """
        检查指定字段内容是否全部非空（None、空字符串、NaN 均视为空）。
        """
        import numpy as np
        if not not_empty_fields:
            return CheckResult('PASSED', '未指定需要检查的非空字段，跳过此项检查。')
        missing_fields = [f for f in not_empty_fields if f not in gdf.columns]
        if missing_fields:
            return CheckResult('FAILED', '部分非空字段在属性表中不存在。', {'missing_fields': missing_fields})
        empty_info = {}
        for field in not_empty_fields:
            # 判断空值：None、空字符串、NaN
            empty_mask = gdf[field].isnull() | (gdf[field] == '')
            if hasattr(np, 'nan'):
                empty_mask = empty_mask | (gdf[field] == np.nan)
            empty_indices = gdf[empty_mask].index.tolist()
            if empty_indices:
                empty_info[field] = {'empty_count': len(empty_indices), 'empty_indices': empty_indices[:10]}
        if not empty_info:
            return CheckResult('PASSED', '所有指定的非空字段内容均非空。')
        else:
            return CheckResult('FAILED', '部分非空字段存在空值。', {'empty_fields': empty_info})


# --- 测试与演示部分 ---

def _create_test_dir():
    TEST_DIR = "test_shp_integrity_temp"
    if os.path.exists(TEST_DIR): shutil.rmtree(TEST_DIR)
    os.makedirs(TEST_DIR)
    return TEST_DIR


# --- [最终修正版] ---
def _create_test_shp(filepath: str, data: Optional[List[Dict]], geom_type: Optional[str] = None, create_prj=True,
                     skip_files=None):
    """
    创建测试Shapefile的辅助函数，兼容pyogrio和fiona引擎。
    """
    kwargs = {'driver': 'ESRI Shapefile', 'encoding': 'utf-8'}

    if not data:  # 创建空数据集
        if geom_type is None:
            raise ValueError("创建空Shapefile时必须指定'geom_type'。")

        # 为pyogrio引擎准备参数
        kwargs['geometry_type'] = geom_type

        gdf = gpd.GeoDataFrame({'geometry': []}, geometry='geometry', crs="EPSG:4326")

        # 兼容旧的fiona引擎，它需要schema
        try:
            gdf.to_file(filepath, **kwargs)
        except ValueError as e:
            if "schema' argument is not supported" in str(e):  # pyogrio error
                # 这是预期的，pyogrio不需要schema，但我们已经提供了geometry_type
                # 实际上，上面那个调用已经成功了，这里只是理论上的回退
                # 为了代码更清晰，我们直接依赖pyogrio的方式
                pass
            elif "does not support empty" in str(e):  # fiona error
                # fiona引擎需要schema
                del kwargs['geometry_type']  # fiona不认识这个参数
                schema = gpd.io.file.infer_schema(gdf)
                schema['geometry'] = geom_type
                kwargs['schema'] = schema
                gdf.to_file(filepath, **kwargs)
            else:
                raise e

    else:  # 创建有数据的SHP
        gdf = gpd.GeoDataFrame(data)
        gdf = gdf.set_geometry('geometry', crs="EPSG:4326")
        # 对于有数据的情况，geopandas可以自动推断，不需要schema或geometry_type
        gdf.to_file(filepath, **kwargs)

    base_path = os.path.splitext(filepath)[0]
    if not create_prj and os.path.exists(base_path + '.prj'):
        os.remove(base_path + '.prj')

    if skip_files:
        for ext in skip_files:
            file_to_remove = base_path + ext
            if os.path.exists(file_to_remove): os.remove(file_to_remove)


def run_demonstration_and_tests():
    """运行一系列演示和测试用例，展示如何使用 ShpIntegrityChecker。"""
    print("=" * 40)
    print("  Shapefile 数据完整性检查器 v1.2 - 演示与测试")
    print("=" * 40)

    TEST_DIR = _create_test_dir()
    REQUIRED_FIELDS = ['id', 'width', 'length', 'area', 'geometry']

    checker = ShpIntegrityChecker()
    print("[初始化] 检查器实例已创建。")
    print(f"[配置] 本次测试要求的必填字段为: {REQUIRED_FIELDS}")

    # 案例 1: 一个完美的点SHP文件
    print("\n--- 案例 1: 检查一个完美的点 Shapefile ---")
    perfect_shp_path = os.path.join(TEST_DIR, "perfect_point.shp")
    perfect_data = [{'id': 1, 'width': 10, 'length': 10, 'area': 100.0, 'geometry': Point(1, 1)}]
    _create_test_shp(perfect_shp_path, perfect_data)
    report1 = checker.check(perfect_shp_path, required_fields=REQUIRED_FIELDS)
    print(str(report1))
    assert report1['overall_status'] == '通过'

    # 案例 2: 文件不完整的SHP (.shx丢失)
    print("\n--- 案例 2: 检查一个文件不完整的 Shapefile (缺少 .shx) ---")
    incomplete_shp_path = os.path.join(TEST_DIR, "incomplete.shp")
    _create_test_shp(incomplete_shp_path, perfect_data, skip_files=['.shx'])
    report2 = checker.check(incomplete_shp_path, required_fields=REQUIRED_FIELDS)
    print(str(report2))
    assert report2['overall_status'] == '失败'
    assert report2['checks']['content_file_completeness']['status'] == 'FAILED'

    # 案例 3: 空数据集 (面类型)
    print("\n--- 案例 3: 检查一个空的 Polygon Shapefile ---")
    empty_shp_path = os.path.join(TEST_DIR, "empty.shp")
    _create_test_shp(empty_shp_path, data=None, geom_type='Polygon')
    report3 = checker.check(empty_shp_path, required_fields=REQUIRED_FIELDS)
    print(str(report3))
    assert report3['overall_status'] == '失败'
    assert report3['checks']['content_empty_dataset']['status'] == 'FAILED'

    # 案例 4: 包含无效和空几何的SHP (面类型)
    print("\n--- 案例 4: 检查一个包含无效和空几何的 Polygon Shapefile ---")
    bad_geom_shp_path = os.path.join(TEST_DIR, "bad_geom.shp")
    invalid_poly = Polygon([(0, 0), (2, 2), (0, 2), (2, 0), (0, 0)])
    bad_geom_data = [
        {'id': 1, 'width': 1, 'length': 1, 'area': 1.0, 'geometry': Point(0, 0).buffer(1)},
        {'id': 2, 'width': 0, 'length': 0, 'area': 0.0, 'geometry': invalid_poly},
        {'id': 3, 'width': 0, 'length': 0, 'area': 0.0, 'geometry': Polygon()}
    ]
    _create_test_shp(bad_geom_shp_path, bad_geom_data)
    report4 = checker.check(bad_geom_shp_path, required_fields=REQUIRED_FIELDS)
    print(str(report4))
    assert report4['overall_status'] == '失败'
    assert report4['checks']['content_geometry_validity']['status'] == 'FAILED'

    # 案例 5: 缺少必填字段的SHP
    print("\n--- 案例 5: 检查一个缺少必填字段 ('area', 'width') 的 Shapefile ---")
    missing_field_shp_path = os.path.join(TEST_DIR, "missing_field.shp")
    missing_field_data = [{'id': 1, 'length': 5, 'geometry': Point(2, 2)}]
    _create_test_shp(missing_field_shp_path, missing_field_data)
    report5 = checker.check(missing_field_shp_path, required_fields=REQUIRED_FIELDS)
    print(str(report5))
    assert report5['overall_status'] == '失败'
    assert report5['checks']['attribute_required_fields']['status'] == 'FAILED'

    print("\n[清理] 删除临时测试文件和目录...")
    shutil.rmtree(TEST_DIR)
    print("清理完成。")

    print("\n" + "=" * 40)
    print("🎉 所有演示与自动化测试全部通过！")
    print("=" * 40)


if __name__ == '__main__':
    run_demonstration_and_tests()
