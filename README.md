# 南科地理信息系统 (Nank-GIS)

## 项目概述
南科地理信息系统是一个专业的GIS数据处理平台，专注于地理空间数据的存储、处理和分析。系统提供以下核心能力：
- 高性能TIF/GeoTIFF影像处理
- 3D建筑模型转换与可视化
- 动态瓦片地图服务
- 分布式存储解决方案

## 系统架构
```mermaid
graph TD
    A[客户端] --> B[Nginx]
    B --> C[Django应用]
    C --> D[PostgreSQL+PostGIS]
    C --> E[Redis]
    C --> F[JuiceFS/S3]
    C --> G[Celery Worker]
```

## 技术栈详情
### 核心框架
- Django 5.1.6 - 基础Web框架
- Django REST Framework 3.15.2 - REST API支持
- Django CORS Headers 4.7.0 - 跨域支持

### 地理数据处理
- GDAL 3.9.2+ - 地理空间数据转换
- GeoPandas 1.0.1 - 地理数据处理
- Rasterio 1.4.3 - 栅格数据处理
- PyProj 3.6.1 - 坐标转换

### 存储系统
- boto3 1.36.20 - AWS S3访问
- django-storages 1.14.4 - 存储后端抽象
- JuiceFS - 分布式文件系统

## 核心功能详解
### 1. TIF文件处理
```python
# 示例：生成瓦片地图
POST /api/tif/tiles/generate/
{
    "file_path": "/data/example.tif",
    "zoom_levels": "10-18",
    "output_dir": "/tiles/output/"
}
```

### 2. 3D模型处理
支持格式：
- OBJ
- FBX
- CityJSON
- GeoJSON

处理流程：
1. 上传模型文件
2. 系统转换并存储
3. 通过API获取处理结果

### 3. 存储系统
配置示例：
```python
# settings.py
DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY')
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_KEY')
AWS_STORAGE_BUCKET_NAME = 'nank-gis-bucket'
```

## 完整API文档
### 瓦片服务
| 端点 | 方法 | 描述 |
|------|------|------|
| `/tif/tiles/view/{z}/{x}/{y}` | GET | 获取标准瓦片 |
| `/tif/tiles/dy-view/{fileid}/{epsg}/{z}/{x}/{y}` | GET | 动态投影瓦片 |

### 3D模型服务
| 端点 | 方法 | 描述 |
|------|------|------|
| `/api/import/` | POST | 导入3D模型 |
| `/api/export/` | GET | 导出GeoJSON |

## 详细部署指南
### 环境变量配置
创建`.env`文件：
```ini
# 数据库配置
DB_NAME=nank_gis
DB_USER=postgres
DB_PASSWORD=yourpassword
DB_HOST=localhost

# S3配置
AWS_ACCESS_KEY_ID=your_key
AWS_SECRET_ACCESS_KEY=your_secret
```

### Docker部署
1. 构建镜像：
```bash
docker-compose build
```

2. 初始化数据库：
```bash
docker-compose run web python manage.py migrate
```

3. 启动服务：
```bash
docker-compose up -d
```

## 开发工作流
1. 设置虚拟环境：
```bash
python -m venv venv
source venv/bin/activate  # Linux
venv\Scripts\activate     # Windows
```

2. 安装依赖：
```bash
pip install -r requirements.txt
```

3. 运行开发服务器：
```bash
python manage.py runserver
```

## 测试指南
运行测试套件：
```bash
python manage.py test glff
```

测试覆盖率报告：
```bash
coverage run manage.py test glff
coverage report
```

## 常见问题
### Q: 如何处理大尺寸TIF文件？
A: 建议：
1. 使用分块处理
2. 增加Celery worker数量
3. 优化GDAL配置

### Q: 3D模型导入失败？
A: 检查：
1. 文件格式支持
2. 模型完整性
3. 系统日志错误信息

## 贡献指南
1. 代码风格：
   - 遵循PEP8
   - 类型注解
   - 文档字符串

2. 提交规范：
   - 清晰的commit message
   - 关联issue编号
   - 通过CI测试
