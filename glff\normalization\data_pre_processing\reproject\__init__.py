"""地理数据重投影模块

提供栅格和矢量数据的坐标转换功能，支持：
- 栅格数据(TIF/GeoTIFF)重投影
- 矢量数据(GeoJSON/Shapefile)重投影
- 多种坐标参考系统转换
- 批量转换和单文件转换

主要功能类:
1. Reprojector: 主重投影类，提供统一的接口
2. CompatibleGeoTiffTransformer: 栅格数据转换类
3. 各种矢量数据转换函数

使用示例:
    >>> from glff.normalization.data_pre_processing.reproject import Reprojector
    >>> # 栅格数据转换
    >>> reprojector = Reprojector()
    >>> reprojector.reproject_tif("input.tif", "output.tif", "EPSG:32648", "EPSG:4326")

    >>> # 矢量数据转换
    >>> reprojector.reproject_vector("input.geojson", "output.geojson", "EPSG:32648", "EPSG:4326")

支持的坐标参考系统:
- EPSG代码(如4326)
- EPSG字符串(如"EPSG:4326")
- WKT字符串
- PROJ字符串
"""

from .reproject import Reprojector
from .tif_reproject import CompatibleGeoTiffTransformer
from .vector_reproject import (
    transform_single_coord,
    transform_geometry_coords,
    convert_geojson_data,
    convert_geojson_file,
    convert_geojson_text,
    convert_geojson_shp
)

__all__ = [
    'Reprojector',
    'CompatibleGeoTiffTransformer',
    'transform_single_coord',
    'transform_geometry_coords',
    'convert_geojson_data',
    'convert_geojson_file',
    'convert_geojson_text',
    'convert_geojson_shp'
]
