#### 
    数据完整性检查
# Shapefile 数据完整性检查器 (SHP Integrity Checker) v1.2

一个轻量级、专注且高效的Python工具，用于对 **ESRI Shapefile** 进行全面的数据完整性验证。它不关心数据的空间参考，只专注于文件结构、内容有效性和属性完整性，是数据入库前进行预检的理想选择。

## ✨ 核心功能

- **文件完整性检查**:
  - 自动验证核心文件 `.shp`, `.shx`, `.dbf` 是否齐全。
  - 友好提示推荐的 `.prj` 投影文件是否存在。

- **内容完整性检查**:
  - **空数据集检测**: 检查Shapefile是否包含任何要素。
  - **几何有效性检测**: 识别并报告**空几何** (Empty) 和**无效几何** (Invalid, 如自相交的多边形)。

- **属性完整性检查**:
  - **动态字段验证**: 支持在调用时动态传入一个**必填字段列表**，检查属性表中是否存在这些字段。
  - **智能处理**: 自动区分属性字段和'geometry'列。

- **标准化报告**:
  - 返回结构化的 **JSON** 格式报告，包含总体状态和各检查项的详细结果。
  - 所有诊断信息均为**中文**，清晰易懂。
  - 在报告中提供问题详情，如缺失的文件列表、无效几何的数量和索引等。

- **健壮且兼容**:
  - 基于 `geopandas` 和 `shapely` 构建，稳定可靠。
  - 测试创建代码已兼容新旧版本的`geopandas`后端引擎 (`pyogrio` 和 `fiona`)。

## ⚙️ 环境与安装

#### 1. 环境要求
- Python 3.8 或更高版本
- 推荐在虚拟环境中安装 (如 `venv` 或 `conda`)

#### 2. 安装依赖
将以下内容保存到 `requirements.txt` 文件中，然后运行 `pip install -r requirements.txt`。

```text
geopandas
shapely
numpy
```

*注意: 安装 `geopandas` 可能会涉及较多的地理空间库依赖 (如 GDAL, Fiona, PyGEOS/Shapely 2.0)。强烈建议使用 `conda` 进行安装以简化流程: `conda install -c conda-forge geopandas`*

## 🚀 快速上手

使用 `ShpIntegrityChecker` 非常简单，只需三步：

#### 步骤 1: 导入并实例化检查器

```python
from shp_integrity_checker_v1_2 import ShpIntegrityChecker

# 实例化检查器，它没有初始化参数
checker = ShpIntegrityChecker()
```

#### 步骤 2: 定义必填字段并执行检查

```python
# 定义你项目所要求的必填字段列表
# 'geometry' 是可选的，但如果包含，会确保几何列存在
required_fields_for_my_project = ['ID', 'NAME', 'AREA_SQM', 'geometry']

# 指定要检查的Shapefile路径
shp_path = "path/to/your/data.shp"

# 调用 check 方法
report = checker.check(filepath=shp_path, required_fields=required_fields_for_my_project)
```

#### 步骤 3: 分析报告

检查报告是一个易于处理的Python字典。

```python
import json

# 以美化的JSON格式打印报告
print(json.dumps(report, indent=2, ensure_ascii=False))

# 基于总体状态做决策
if report['overall_status'] == '通过':
    print(f"文件 '{shp_path}' 的数据完整性检查通过，可以进行下一步处理。")
else:
    print(f"文件 '{shp_path}' 存在完整性问题，请查看报告详情！")

```

## 📋 理解检查报告

报告结构清晰，便于自动化系统集成。

```json
{
  "filepath": "test_shp_integrity_temp/bad_geom.shp",
  "overall_status": "失败",
  "checks": {
    "content_file_completeness": {
      "status": "PASSED",
      "message": "Shapefile核心文件齐全。",
      "details": {
        "found_files": [ "bad_geom.shp", "bad_geom.shx", "bad_geom.dbf", "bad_geom.prj" ]
      }
    },
    "content_empty_dataset": {
      "status": "PASSED",
      "message": "数据集包含 3 个要素。",
      "details": {}
    },
    "content_geometry_validity": {
      "status": "FAILED",
      "message": "数据集中存在问题几何： 发现 1 个空几何； 发现 1 个无效几何。",
      "details": {
        "empty_geometry_indices": [ 2 ],
        "empty_geometry_count": 1,
        "invalid_geometry_indices": [ 1 ],
        "invalid_geometry_count": 1
      }
    },
    "attribute_required_fields": {
      "status": "PASSED",
      "message": "所有指定的必填属性字段都存在。 要求的几何列 'geometry' 也存在。",
      "details": {}
    }
  }
}
```

- **`overall_status`**: 最终结论 (`通过`, `失败`, `错误`)。
- **`checks`**: 包含所有子检查项结果的字典。
  - `content_file_completeness`: 文件完整性
  - `content_empty_dataset`: 空数据集
  - `content_geometry_validity`: 几何有效性
  - `attribute_required_fields`: 必填字段
- **`status`**: 单项检查的状态 (`PASSED`, `FAILED`, `ERROR`)。
- **`message`**: 对该项检查结果的中文摘要。
- **`details`**: 包含额外上下文信息的字典，如缺失的字段名、问题几何的索引等，帮助快速定位问题。

## 🔬 API 参考

### `ShpIntegrityChecker()`
- 这是一个无参构造函数，直接实例化即可。

### `checker.check(filepath, required_fields)`
- **`filepath` (str)**: 待检查的 `.shp` 文件路径。
- **`required_fields` (List[str])**: 一个字符串列表，定义了属性表中必须存在的字段名称。
- **返回 (Returns)**: `Dict[str, Any]`，一个包含完整检查结果的字典。

## ✅ 运行测试

脚本内建了一个自验证的测试套件，覆盖了所有核心检查功能。要运行它，请在终端中直接执行此Python文件：
```bash
python shp_integrity_checker_v1.2.py
```
如果所有测试用例都成功通过，您将在最后看到 `🎉 所有演示与自动化测试全部通过！` 的消息，证明模块功能正常。

## 📄 许可证

建议使用 [MIT](https://opensource.org/licenses/MIT) 许可证。

