<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据规范化处理子系统</title>
    <link href="lib/css/googleapis.css" rel="stylesheet">
    <link rel="stylesheet" href="lib/css/all.min.css">
    <style>
        /* 设计系统 - 现代化科技蓝主题 */
        :root {
            /* 主色调 */
            --primary-50: #e6f7ff;
            --primary-100: #b3e0ff;
            --primary-200: #80c9ff;
            --primary-300: #4db3ff;
            --primary-400: #1a9cff;
            --primary-500: #0085ff;
            --primary-600: #0066cc;
            --primary-700: #004799;
            --primary-800: #002866;
            --primary-900: #000933;
            
            /* 中性色 */
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            
            /* 背景色 */
            --bg-primary: #0a0f1c;
            --bg-secondary: #111827;
            --bg-tertiary: #1f2937;
            --bg-card: rgba(31, 41, 55, 0.8);
            --bg-overlay: rgba(10, 15, 28, 0.95);
            
            /* 文字色 */
            --text-primary: #ffffff;
            --text-secondary: #94a3b8;
            --text-tertiary: #64748b;
            
            /* 边框色 */
            --border-primary: #374151;
            --border-secondary: #4b5563;
            --border-accent: #3b82f6;
            
            /* 状态色 */
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --info: #3b82f6;
            
            /* 阴影 */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            
            /* 动画 */
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
            
            /* 间距 */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
            --space-24: 6rem;
            
            /* 圆角 */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
            --radius-3xl: 2rem;
        }

        /* 重置样式 */
        *,
        *::before,
        *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            color: var(--text-primary);
            line-height: 1.6;
            font-size: 16px;
            font-weight: 400;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* 工具类 */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 var(--space-6);
        }

        .flex {
            display: flex;
        }

        .flex-col {
            flex-direction: column;
        }

        .items-center {
            align-items: center;
        }

        .justify-between {
            justify-content: space-between;
        }

        .justify-center {
            justify-content: center;
        }

        .text-center {
            text-align: center;
        }

        .gap-4 {
            gap: var(--space-4);
        }

        .gap-6 {
            gap: var(--space-6);
        }

        .gap-8 {
            gap: var(--space-8);
        }

        .mb-4 {
            margin-bottom: var(--space-4);
        }

        .mb-6 {
            margin-bottom: var(--space-6);
        }

        .mb-8 {
            margin-bottom: var(--space-8);
        }

        .mb-12 {
            margin-bottom: var(--space-12);
        }

        .mt-8 {
            margin-top: var(--space-8);
        }

        .mt-12 {
            margin-top: var(--space-12);
        }

        .pt-20 {
            padding-top: var(--space-20);
        }

        .pb-20 {
            padding-bottom: var(--space-20);
        }

        .px-6 {
            padding-left: var(--space-6);
            padding-right: var(--space-6);
        }

        .py-8 {
            padding-top: var(--space-8);
            padding-bottom: var(--space-8);
        }

        .py-12 {
            padding-top: var(--space-12);
            padding-bottom: var(--space-12);
        }

        .py-16 {
            padding-top: var(--space-16);
            padding-bottom: var(--space-16);
        }

        /* 导航栏 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(10, 15, 28, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-primary);
            transition: var(--transition-normal);
        }

        .navbar.scrolled {
            background: rgba(10, 15, 28, 0.98);
            box-shadow: var(--shadow-lg);
        }

        .navbar-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-4) 0;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            text-decoration: none;
        }

        .navbar-brand-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary-400), var(--primary-600));
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            box-shadow: var(--shadow-md);
        }

        .navbar-nav {
            display: flex;
            align-items: center;
            gap: var(--space-6);
        }

        .nav-item {
            position: relative;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-3) var(--space-4);
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--radius-md);
            transition: var(--transition-fast);
            font-weight: 500;
        }

        .nav-link:hover {
            color: var(--text-primary);
            background: rgba(55, 65, 81, 0.3);
        }

        .nav-link.active {
            color: var(--primary-400);
            background: rgba(59, 130, 246, 0.1);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            min-width: 280px;
            background: var(--bg-overlay);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-xl);
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: var(--transition-normal);
            z-index: 1001;
        }

        .nav-item:hover .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-4);
            color: var(--text-secondary);
            text-decoration: none;
            transition: var(--transition-fast);
            border-bottom: 1px solid var(--border-primary);
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item:hover {
            background: rgba(55, 65, 81, 0.3);
            color: var(--text-primary);
        }

        .dropdown-item-icon {
            width: 32px;
            height: 32px;
            background: rgba(59, 130, 246, 0.1);
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-400);
        }

        .dropdown-item-content {
            flex: 1;
        }

        .dropdown-item-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--space-1);
        }

        .dropdown-item-desc {
            font-size: 0.875rem;
            color: var(--text-tertiary);
        }

        .navbar-actions {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        /* 按钮组件 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
            padding: var(--space-3) var(--space-6);
            border: none;
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: var(--transition-fast);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: var(--transition-normal);
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: rgba(55, 65, 81, 0.8);
            color: var(--text-primary);
            border: 1px solid var(--border-primary);
        }

        .btn-secondary:hover {
            background: rgba(55, 65, 81, 0.9);
            border-color: var(--primary-400);
            color: var(--primary-400);
        }

        .btn-ghost {
            background: transparent;
            color: var(--text-secondary);
            border: 1px solid transparent;
        }

        .btn-ghost:hover {
            background: rgba(55, 65, 81, 0.5);
            color: var(--text-primary);
        }

        /* 主内容区 */
        .main-content {
            padding-top: 100px;
            min-height: 100vh;
        }

        /* 英雄区域 */
        .hero-section {
            position: relative;
            padding: var(--space-24) 0;
            overflow: hidden;
        }

        .hero-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(147, 51, 234, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 800;
            line-height: 1.1;
            margin-bottom: var(--space-6);
            background: linear-gradient(135deg, var(--primary-400), var(--primary-600), #60a5fa);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: clamp(1.125rem, 2vw, 1.25rem);
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto var(--space-12);
            line-height: 1.6;
        }

        /* 功能分类区域 */
        .features-section {
            padding: var(--space-20) 0;
        }

        .section-header {
            text-align: center;
            margin-bottom: var(--space-16);
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--space-4);
        }

        .section-subtitle {
            font-size: 1.125rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: var(--space-8);
            margin-top: var(--space-12);
        }

        .feature-card {
            background: var(--bg-card);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-2xl);
            padding: var(--space-10);
            text-align: center;
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-400), var(--primary-600), var(--primary-400));
            background-size: 200% 100%;
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-2xl);
            border-color: var(--primary-400);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto var(--space-6);
            background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            box-shadow: var(--shadow-lg);
            transition: var(--transition-normal);
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1);
            box-shadow: var(--shadow-xl);
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--space-4);
        }

        .feature-description {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: var(--space-6);
        }

        .feature-tags {
            display: flex;
            justify-content: center;
            gap: var(--space-3);
            margin-bottom: var(--space-6);
            flex-wrap: wrap;
        }

        .feature-tag {
            padding: var(--space-1) var(--space-3);
            background: rgba(59, 130, 246, 0.1);
            color: var(--primary-400);
            border-radius: var(--radius-md);
            font-size: 0.75rem;
            font-weight: 500;
        }

        /* 模态框 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            z-index: 2000;
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: var(--bg-overlay);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-xl);
            padding: var(--space-8);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-6);
            padding-bottom: var(--space-4);
            border-bottom: 1px solid var(--border-primary);
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .modal-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.5rem;
            cursor: pointer;
            padding: var(--space-2);
            border-radius: var(--radius-md);
            transition: var(--transition-fast);
        }

        .modal-close:hover {
            background: rgba(55, 65, 81, 0.5);
            color: var(--text-primary);
        }

        .modal-body {
            color: var(--text-secondary);
        }

        .modal-body h4 {
            color: var(--text-primary);
            margin-bottom: var(--space-3);
            font-weight: 600;
        }

        .modal-body ul {
            margin-left: var(--space-4);
            margin-bottom: var(--space-6);
        }

        .modal-body li {
            margin-bottom: var(--space-1);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 var(--space-4);
            }

            .navbar-nav {
                display: none;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: var(--space-6);
            }

            .feature-card {
                padding: var(--space-8);
            }

            .navbar-actions {
                gap: var(--space-2);
            }

            .btn {
                padding: var(--space-2) var(--space-4);
                font-size: 0.8rem;
            }

            .dropdown-menu {
                position: static;
                opacity: 1;
                visibility: visible;
                transform: none;
                box-shadow: none;
                border: none;
                background: transparent;
            }

            .nav-item:hover .dropdown-menu {
                display: none;
            }
        }

        /* 动画 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        .animate-fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.8s ease-out;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-secondary);
            border-radius: var(--radius-md);
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--border-primary);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar" id="navbar">
        <div class="container">
            <div class="navbar-content">
                <a href="/" class="navbar-brand">
                    <div class="navbar-brand-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <span>数据规范化处理子系统</span>
                </a>
                
{#                <div class="navbar-nav">#}
{#                    <div class="nav-item">#}
{#                        <a href="#" class="nav-link">#}
{#                            <i class="fas fa-gears"></i>#}
{#                            <span>数据预处理</span>#}
{#                            <i class="fas fa-chevron-down" style="font-size: 0.75rem;"></i>#}
{#                        </a>#}
{#                        <div class="dropdown-menu">#}
{#                            <a href="/preprocess/" class="dropdown-item">#}
{#                                <div class="dropdown-item-icon">#}
{#                                    <i class="fas fa-file-text"></i>#}
{#                                </div>#}
{#                                <div class="dropdown-item-content">#}
{#                                    <div class="dropdown-item-title">文本提取</div>#}
{#                                    <div class="dropdown-item-desc">从各种文档格式中提取文本内容</div>#}
{#                                </div>#}
{#                            </a>#}
{#                            <a href="/preprocess/" class="dropdown-item">#}
{#                                <div class="dropdown-item-icon">#}
{#                                    <i class="fas fa-file-upload"></i>#}
{#                                </div>#}
{#                                <div class="dropdown-item-content">#}
{#                                    <div class="dropdown-item-title">文件预处理</div>#}
{#                                    <div class="dropdown-item-desc">文件格式转换和基础处理</div>#}
{#                                </div>#}
{#                            </a>#}
{#                            <a href="/preprocess/" class="dropdown-item">#}
{#                                <div class="dropdown-item-icon">#}
{#                                    <i class="fas fa-exchange-alt"></i>#}
{#                                </div>#}
{#                                <div class="dropdown-item-content">#}
{#                                    <div class="dropdown-item-title">坐标重投影</div>#}
{#                                    <div class="dropdown-item-desc">单文件和批量坐标系统转换</div>#}
{#                                </div>#}
{#                            </a>#}
{#                            <a href="/preprocess/" class="dropdown-item">#}
{#                                <div class="dropdown-item-icon">#}
{#                                    <i class="fas fa-map-marker-alt"></i>#}
{#                                </div>#}
{#                                <div class="dropdown-item-content">#}
{#                                    <div class="dropdown-item-title">坐标转换</div>#}
{#                                    <div class="dropdown-item-desc">单点坐标和批量坐标转换</div>#}
{#                                </div>#}
{#                            </a>#}
{#                            <a href="/preprocess/" class="dropdown-item">#}
{#                                <div class="dropdown-item-icon">#}
{#                                    <i class="fas fa-code"></i>#}
{#                                </div>#}
{#                                <div class="dropdown-item-content">#}
{#                                    <div class="dropdown-item-title">GeoJSON转换</div>#}
{#                                    <div class="dropdown-item-desc">GeoJSON格式转换和坐标变换</div>#}
{#                                </div>#}
{#                            </a>#}
{#                            <a href="/preprocess/" class="dropdown-item">#}
{#                                <div class="dropdown-item-icon">#}
{#                                    <i class="fas fa-shapes"></i>#}
{#                                </div>#}
{#                                <div class="dropdown-item-content">#}
{#                                    <div class="dropdown-item-title">Shapefile转换</div>#}
{#                                    <div class="dropdown-item-desc">Shapefile格式转换和坐标变换</div>#}
{#                                </div>#}
{#                            </a>#}
{#                            <a href="/preprocess/" class="dropdown-item">#}
{#                                <div class="dropdown-item-icon">#}
{#                                    <i class="fas fa-info-circle"></i>#}
{#                                </div>#}
{#                                <div class="dropdown-item-content">#}
{#                                    <div class="dropdown-item-title">空间信息提取</div>#}
{#                                    <div class="dropdown-item-desc">Shapefile和TIF文件信息提取</div>#}
{#                                </div>#}
{#                            </a>#}
{#                            <a href="/preprocess/" class="dropdown-item">#}
{#                                <div class="dropdown-item-icon">#}
{#                                    <i class="fas fa-image"></i>#}
{#                                </div>#}
{#                                <div class="dropdown-item-content">#}
{#                                    <div class="dropdown-item-title">影像处理</div>#}
{#                                    <div class="dropdown-item-desc">影像增强、重采样等处理</div>#}
{#                                </div>#}
{#                            </a>#}
{#                            <a href="/preprocess/" class="dropdown-item">#}
{#                                <div class="dropdown-item-icon">#}
{#                                    <i class="fas fa-puzzle-piece"></i>#}
{#                                </div>#}
{#                                <div class="dropdown-item-content">#}
{#                                    <div class="dropdown-item-title">影像拼接</div>#}
{#                                    <div class="dropdown-item-desc">多影像拼接和镶嵌处理</div>#}
{#                                </div>#}
{#                            </a>#}
{#                        </div>#}
{#                    </div>#}
{#                    #}
{#                    <div class="nav-item">#}
{#                        <a href="#" class="nav-link">#}
{#                            <i class="fas fa-shield-alt"></i>#}
{#                            <span>质量检查</span>#}
{#                            <i class="fas fa-chevron-down" style="font-size: 0.75rem;"></i>#}
{#                        </a>#}
{#                        <div class="dropdown-menu">#}
{#                            <a href="/quality/" class="dropdown-item">#}
{#                                <div class="dropdown-item-icon">#}
{#                                    <i class="fas fa-list-check"></i>#}
{#                                </div>#}
{#                                <div class="dropdown-item-content">#}
{#                                    <div class="dropdown-item-title">属性检查</div>#}
{#                                    <div class="dropdown-item-desc">验证数据字段结构和类型</div>#}
{#                                </div>#}
{#                            </a>#}
{#                            <a href="/quality/" class="dropdown-item">#}
{#                                <div class="dropdown-item-icon">#}
{#                                    <i class="fas fa-database"></i>#}
{#                                </div>#}
{#                                <div class="dropdown-item-content">#}
{#                                    <div class="dropdown-item-title">数据库属性检查</div>#}
{#                                    <div class="dropdown-item-desc">数据库表结构和属性验证</div>#}
{#                                </div>#}
{#                            </a>#}
{#                            <a href="/quality/" class="dropdown-item">#}
{#                                <div class="dropdown-item-icon">#}
{#                                    <i class="fas fa-brain"></i>#}
{#                                </div>#}
{#                                <div class="dropdown-item-content">#}
{#                                    <div class="dropdown-item-title">逻辑一致性</div>#}
{#                                    <div class="dropdown-item-desc">数据逻辑关系和一致性检查</div>#}
{#                                </div>#}
{#                            </a>#}
{#                            <a href="/quality/" class="dropdown-item">#}
{#                                <div class="dropdown-item-icon">#}
{#                                    <i class="fas fa-project-diagram"></i>#}
{#                                </div>#}
{#                                <div class="dropdown-item-content">#}
{#                                    <div class="dropdown-item-title">拓扑关系</div>#}
{#                                    <div class="dropdown-item-desc">空间几何拓扑关系验证</div>#}
{#                                </div>#}
{#                            </a>#}
{#                            <a href="/quality/" class="dropdown-item">#}
{#                                <div class="dropdown-item-icon">#}
{#                                    <i class="fas fa-check-circle"></i>#}
{#                                </div>#}
{#                                <div class="dropdown-item-content">#}
{#                                    <div class="dropdown-item-title">数据完整性</div>#}
{#                                    <div class="dropdown-item-desc">Shapefile文件完整性检查</div>#}
{#                                </div>#}
{#                            </a>#}
{#                            <a href="/quality/" class="dropdown-item">#}
{#                                <div class="dropdown-item-icon">#}
{#                                    <i class="fas fa-compass"></i>#}
{#                                </div>#}
{#                                <div class="dropdown-item-content">#}
{#                                    <div class="dropdown-item-title">空间参考</div>#}
{#                                    <div class="dropdown-item-desc">坐标系统和空间参考验证</div>#}
{#                                </div>#}
{#                            </a>#}
{#                        </div>#}
{#                    </div>#}
{#                </div>#}
                
                <div class="navbar-actions">
                    <button class="btn btn-ghost" id="helpBtn">
                        <i class="fas fa-question-circle"></i>
                        <span>帮助</span>
                    </button>
                    <button class="btn btn-ghost" id="historyBtn">
                        <i class="fas fa-history"></i>
                        <span>历史</span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容 -->
    <main class="main-content">
        <!-- 英雄区域 -->
        <section class="hero-section">
            <div class="hero-background"></div>
            <div class="container">
                <div class="hero-content text-center">
                    <h1 class="hero-title animate-fade-in-up">数据规范化处理子系统</h1>
                    <p class="hero-subtitle animate-fade-in-up">
                        地理数据处理与质量检查工具，支持多种格式转换、坐标变换、数据验证等功能
                    </p>
                </div>
            </div>
        </section>

        <!-- 功能区域 -->
        <section class="features-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">核心功能模块</h2>
                    <p class="section-subtitle">提供地理数据处理和质量检查解决方案</p>
                </div>
                
                <div class="features-grid">
                    <!-- 数据预处理卡片 -->
                    <div class="feature-card animate-fade-in-up" onclick="window.location.href='/preprocess/'">
                        <div class="feature-icon">
                            <i class="fas fa-gears"></i>
                        </div>
                        <h3 class="feature-title">数据预处理</h3>
                        <p class="feature-description">
                            支持文档格式转换、坐标变换、影像处理、数据拼接等多种预处理功能，
                            为后续分析提供高质量的数据基础。
                        </p>
                        <div class="feature-tags">
                            <span class="feature-tag">文本提取</span>
                            <span class="feature-tag">坐标转换</span>
                            <span class="feature-tag">影像处理</span>
                            <span class="feature-tag">格式转换</span>
                        </div>
                        <button class="btn btn-primary">
                            <i class="fas fa-arrow-right"></i>
                            <span>开始预处理</span>
                        </button>
                    </div>

                    <!-- 数据质量检查卡片 -->
                    <div class="feature-card animate-fade-in-up" onclick="window.location.href='/quality/'">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3 class="feature-title">数据质量检查</h3>
                        <p class="feature-description">
                            提供数据质量检查工具，包括属性结构验证、拓扑关系检查、
                            逻辑一致性分析等，确保数据准确性和完整性。
                        </p>
                        <div class="feature-tags">
                            <span class="feature-tag">属性检查</span>
                            <span class="feature-tag">拓扑验证</span>
                            <span class="feature-tag">完整性分析</span>
                            <span class="feature-tag">逻辑验证</span>
                        </div>
                        <button class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            <span>开始检查</span>
                        </button>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 帮助模态框 -->
    <div class="modal" id="helpModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">平台使用帮助</h3>
                <button class="modal-close" onclick="closeModal('helpModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="mb-6">
                    <h4>
                        <i class="fas fa-gears" style="margin-right: 0.5rem;"></i>
                        数据预处理功能
                    </h4>
                    <ul>
                        <li>• 文本提取：从各种文档格式中提取文本内容</li>
                        <li>• 文件预处理：文件格式转换和基础处理</li>
                        <li>• 坐标重投影：单文件和批量坐标系统转换</li>
                        <li>• 坐标转换：单点坐标和批量坐标转换</li>
                        <li>• GeoJSON转换：GeoJSON格式转换和坐标变换</li>
                        <li>• Shapefile转换：Shapefile格式转换和坐标变换</li>
                        <li>• 空间信息提取：Shapefile和TIF文件信息提取</li>
                        <li>• 影像处理：影像增强、重采样等处理</li>
                        <li>• 影像拼接：多影像拼接和镶嵌处理</li>
                    </ul>
                </div>
                
                <div class="mb-6">
                    <h4>
                        <i class="fas fa-shield-alt" style="margin-right: 0.5rem;"></i>
                        数据质量检查功能
                    </h4>
                    <ul>
                        <li>• 属性检查：验证数据字段结构和类型</li>
                        <li>• 数据库属性检查：数据库表结构和属性验证</li>
                        <li>• 逻辑一致性：数据逻辑关系和一致性检查</li>
                        <li>• 拓扑关系：空间几何拓扑关系验证</li>
                        <li>• 数据完整性：Shapefile文件完整性检查</li>
                        <li>• 空间参考：坐标系统和空间参考验证</li>
                    </ul>
                </div>
                
                <div>
                    <h4>
                        <i class="fas fa-lightbulb" style="margin-right: 0.5rem;"></i>
                        使用建议
                    </h4>
                    <ul>
                        <li>• 建议先进行数据预处理，再进行质量检查</li>
                        <li>• 支持批量处理，提高工作效率</li>
                        <li>• 所有操作都有详细的结果报告</li>
                        <li>• 支持多种常见地理数据格式</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 历史记录模态框 -->
    <div class="modal" id="historyModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">历史任务记录</h3>
                <button class="modal-close" onclick="closeModal('historyModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h4>预处理历史</h4>
                        <button class="btn btn-secondary" onclick="clearHistory('preprocessHistory')">
                            <i class="fas fa-trash" style="margin-right: 0.25rem;"></i>
                            清空
                        </button>
                    </div>
                    <div id="preprocessHistory" class="space-y-2 max-h-40 overflow-y-auto"></div>
                </div>
                
                <hr style="border-color: var(--border-primary); margin: var(--space-6) 0;">
                
                <div>
                    <div class="flex justify-between items-center mb-4">
                        <h4>质量检查历史</h4>
                        <button class="btn btn-secondary" onclick="clearHistory('qualityHistory')">
                            <i class="fas fa-trash" style="margin-right: 0.25rem;"></i>
                            清空
                        </button>
                    </div>
                    <div id="qualityHistory" class="space-y-2 max-h-40 overflow-y-auto"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 导航栏滚动效果
        window.addEventListener('scroll', () => {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // 模态框控制
        function openModal(modalId) {
            document.getElementById(modalId).classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
            document.body.style.overflow = 'auto';
        }

        // 点击模态框外部关闭
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeModal(modal.id);
                }
            });
        });

        // 事件监听
        document.getElementById('helpBtn').addEventListener('click', () => {
            openModal('helpModal');
        });

        document.getElementById('historyBtn').addEventListener('click', () => {
            loadHistory();
            openModal('historyModal');
        });

        // 历史记录管理
        function loadHistory() {
            const preprocessHistory = JSON.parse(localStorage.getItem('preprocessHistory') || '[]');
            const qualityHistory = JSON.parse(localStorage.getItem('qualityHistory') || '[]');
            
            const preprocessContainer = document.getElementById('preprocessHistory');
            const qualityContainer = document.getElementById('qualityHistory');
            
            preprocessContainer.innerHTML = preprocessHistory.length > 0 ? 
                preprocessHistory.slice(0, 10).map(task => `
                    <div style="padding: 0.75rem; background: var(--bg-card); border-radius: var(--radius-lg);">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-size: 0.875rem; font-weight: 500;">${task.function}</span>
                            <span style="font-size: 0.75rem; color: var(--text-secondary);">${new Date(task.timestamp).toLocaleString()}</span>
                        </div>
                    </div>
                `).join('') : '<p style="color: var(--text-secondary); font-size: 0.875rem;">暂无历史记录</p>';
            
            qualityContainer.innerHTML = qualityHistory.length > 0 ? 
                qualityHistory.slice(0, 10).map(task => `
                    <div style="padding: 0.75rem; background: var(--bg-card); border-radius: var(--radius-lg);">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-size: 0.875rem; font-weight: 500;">${task.function}</span>
                            <span style="font-size: 0.75rem; color: var(--text-secondary);">${new Date(task.timestamp).toLocaleString()}</span>
                        </div>
                    </div>
                `).join('') : '<p style="color: var(--text-secondary); font-size: 0.875rem;">暂无历史记录</p>';
        }

        function clearHistory(type) {
            localStorage.removeItem(type);
            loadHistory();
        }
    </script>
</body>
</html> 