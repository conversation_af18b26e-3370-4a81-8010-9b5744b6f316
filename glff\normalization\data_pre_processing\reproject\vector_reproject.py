import json

from pathlib import Path
from pyproj import Transformer
import geopandas as gpd

"""矢量数据坐标转换模块

提供多种矢量数据格式的坐标转换功能，支持：
- GeoJSON文件/文本的坐标转换
- Shapefile文件的坐标转换
- 各种几何类型的坐标转换(点、线、面等)

主要功能：
1. transform_single_coord: 转换单个坐标点
2. transform_geometry_coords: 转换几何图形坐标
3. convert_geojson_data: 转换GeoJSON数据
4. convert_geojson_file: 转换GeoJSON文件
5. convert_geojson_text: 转换GeoJSON文本
6. convert_geojson_shp: 转换Shapefile文件

支持的坐标参考系统(CRS):
- EPSG代码(如4326)
- EPSG字符串(如"EPSG:4326")
- WKT字符串
- PROJ字符串

示例:
    >>> # GeoJSON文件转换
    >>> convert_geojson_file("input.geojson", "output.geojson", "EPSG:32648", "EPSG:4326")
    
    >>> # Shapefile转换
    >>> convert_geojson_shp("input.shp", "output.shp", "EPSG:32648", "EPSG:4326")

注意事项:
1. 转换大文件时注意内存使用
2. Shapefile转换需要安装geopandas库
3. 确保输入输出文件路径有效
4. 转换后的坐标精度取决于源数据精度
"""

def transform_single_coord(coord, transformer):
    """转换单个坐标点 [x, y] 或 [x, y, z]
    
    参数:
        coord (list): 坐标点列表
            支持格式:
            - [x, y]: 二维坐标
            - [x, y, z]: 三维坐标
            示例: [116.404, 39.915] (北京坐标)
            
        transformer (pyproj.Transformer): 坐标转换器对象
            通过pyproj.Transformer.from_crs()创建
            
    返回:
        list: 转换后的坐标点列表
            格式:
            - 二维坐标: [lon, lat]
            - 三维坐标: [lon, lat, z] (z值保持不变)
            示例: [116.404, 39.915] (WGS84坐标)
            
    异常:
        ValueError: 坐标格式不正确时抛出
        RuntimeError: 坐标转换失败时抛出
        
    示例:
        >>> transformer = Transformer.from_crs("EPSG:32648", "EPSG:4326")
        >>> transform_single_coord([566353.96, 2899559.66], transformer)
        [116.404, 39.915]
        
    注意事项:
    1. 输入坐标顺序应为[x, y]或[x, y, z]
    2. 输出坐标顺序为[经度, 纬度]
    3. 三维坐标的z值将保持不变
    """
    x, y = coord[0], coord[1]  # 提取x和y坐标
    lon, lat = transformer.transform(x, y)  # 使用转换器转换坐标
    if len(coord) == 2:  # 如果是二维坐标
        return [lon, lat]
    else:  # 如果是三维坐标
        return [lon, lat] + coord[2:]  # 保留z坐标不变

def transform_geometry_coords(coords, transformer):
    """递归转换几何图形坐标
    
    支持转换所有GeoJSON几何类型的坐标结构，包括：
    - Point(点): [x, y]
    - MultiPoint(多点): [[x, y], ...]
    - LineString(线): [[x, y], ...]
    - MultiLineString(多线): [[[x, y], ...], ...]
    - Polygon(面): [[[x, y], ...], ...]
    - MultiPolygon(多面): [[[[x, y], ...], ...], ...]

    参数:
        coords (list): 几何图形的坐标结构
            支持格式:
            - 点: [x, y]
            - 多点/线: [[x, y], ...]
            - 多线/面: [[[x, y], ...], ...]
            - 多面: [[[[x, y], ...], ...], ...]
            示例: [[116.404, 39.915], [116.405, 39.916]] (线坐标)
            
        transformer (pyproj.Transformer): 坐标转换器对象
            通过pyproj.Transformer.from_crs()创建

    返回:
        list: 转换后的坐标结构
            保持原始几何类型结构不变
            示例: [[116.404, 39.915], [116.405, 39.916]] (转换后的线坐标)

    异常:
        ValueError: 坐标结构格式不正确时抛出
        RuntimeError: 坐标转换失败时抛出

    示例:
        >>> transformer = Transformer.from_crs("EPSG:32648", "EPSG:4326")
        >>> coords = [[566353.96, 2899559.66], [566354.96, 2899560.66]]
        >>> transform_geometry_coords(coords, transformer)
        [[116.404, 39.915], [116.405, 39.916]]

    注意事项:
    1. 递归处理所有层级的坐标结构
    2. 保持原始几何图形的拓扑结构不变
    3. 支持嵌套几何类型(如MultiPolygon中的多个Polygon)
    4. 三维坐标的z值将保持不变
    """
    if isinstance(coords[0], (float, int)):  # 如果是单个点（坐标列表的第一个元素是数字）
        return transform_single_coord(coords, transformer)  # 转换单个点
    else:
        # 递归转换每个子坐标
        return [transform_geometry_coords(c, transformer) for c in coords]

def convert_geojson_data(data, src_crs, dst_crs):
    """转换GeoJSON数据中的坐标参考系统
    
    支持转换GeoJSON FeatureCollection中的所有要素的坐标系统，
    包括点、线、面等几何类型。

    参数:
        data (dict): GeoJSON格式的数据字典
            必须包含"features"字段，每个要素包含"geometry"字段
            示例:
            {
                "type": "FeatureCollection",
                "features": [
                    {
                        "type": "Feature",
                        "geometry": {
                            "type": "Point",
                            "coordinates": [116.404, 39.915]
                        }
                    }
                ]
            }
            
        src_crs (str): 源坐标参考系统
            支持格式:
            - EPSG代码(如4326)
            - EPSG字符串(如"EPSG:4326")
            - WKT字符串
            - PROJ字符串
            示例: "EPSG:32648" (UTM 48N)
            
        dst_crs (str): 目标坐标参考系统
            格式同src_crs
            示例: "EPSG:4326" (WGS84)

    返回:
        dict: 转换后的GeoJSON数据
            包含更新后的坐标和CRS信息
            示例:
            {
                "type": "FeatureCollection",
                "crs": {
                    "type": "name",
                    "properties": {"name": "urn:ogc:def:crs:EPSG::4326"}
                },
                "features": [
                    {
                        "type": "Feature",
                        "geometry": {
                            "type": "Point",
                            "coordinates": [116.404, 39.915]
                        }
                    }
                ]
            }

    异常:
        ValueError: 输入数据格式不正确时抛出
        RuntimeError: 坐标转换失败时抛出
        KeyError: 缺少必要字段时抛出

    示例:
        >>> data = {
        ...     "type": "FeatureCollection",
        ...     "features": [
        ...         {
        ...             "type": "Feature",
        ...             "geometry": {
        ...                 "type": "Point",
        ...                 "coordinates": [566353.96, 2899559.66]
        ...             }
        ...         }
        ...     ]
        ... }
        >>> convert_geojson_data(data, "EPSG:32648", "EPSG:4326")
        {
            "type": "FeatureCollection",
            "crs": {
                "type": "name",
                "properties": {"name": "urn:ogc:def:crs:EPSG::4326"}
            },
            "features": [
                {
                    "type": "Feature",
                    "geometry": {
                        "type": "Point",
                        "coordinates": [116.404, 39.915]
                    }
                }
            ]
        }

    注意事项:
    1. 输入数据必须是有效的GeoJSON FeatureCollection
    2. 会更新数据中的CRS信息
    3. 支持嵌套几何类型(如GeometryCollection)
    4. 转换会保留所有要素属性
    """
    transformer = Transformer.from_crs(src_crs, dst_crs, always_xy=True)  # 创建坐标转换器

    # 遍历所有要素
    for feature in data.get('features', []):
        geometry = feature.get('geometry')  # 获取要素的几何信息
        if geometry and geometry.get('coordinates'):  # 如果几何信息存在且有坐标
            coords = geometry['coordinates']  # 获取坐标
            new_coords = transform_geometry_coords(coords, transformer)  # 转换坐标
            feature['geometry']['coordinates'] = new_coords  # 更新坐标

    # 更新CRS（坐标参考系统）信息
    data['crs'] = {
        "type": "name",
        "properties": {"name": f"urn:ogc:def:crs:{dst_crs}"}  # 使用目标CRS
    }

    return data

def convert_geojson_file(input_path, output_path, src_crs="EPSG:32648", dst_crs="EPSG:4326"):
    """转换GeoJSON文件的坐标参考系统
    
    读取输入GeoJSON文件，转换所有要素的坐标系统，
    并将结果写入输出文件。

    参数:
        input_path (str): 输入GeoJSON文件路径
            支持格式:
            - 标准GeoJSON文件(.geojson)
            - JSON文件(.json)
            示例: "input/data.geojson"
            
        output_path (str): 输出文件路径
            文件扩展名应与输入文件一致
            示例: "output/data_converted.geojson"
            
        src_crs (str): 源坐标参考系统，默认为"EPSG:32648"(UTM 48N)
            支持格式:
            - EPSG代码(如4326)
            - EPSG字符串(如"EPSG:4326")
            - WKT字符串
            - PROJ字符串
            
        dst_crs (str): 目标坐标参考系统，默认为"EPSG:4326"(WGS84)
            格式同src_crs

    返回:
        无返回值，但会打印转换状态信息到控制台

    异常:
        FileNotFoundError: 输入文件不存在时抛出
        ValueError: 文件格式不正确时抛出
        RuntimeError: 坐标转换失败时抛出
        IOError: 文件写入失败时抛出

    示例:
        >>> convert_geojson_file(
        ...     "input/data.geojson",
        ...     "output/data_converted.geojson",
        ...     "EPSG:32648",
        ...     "EPSG:4326"
        ... )
        ✅ 文件转换成功，保存至：output/data_converted.geojson

    注意事项:
    1. 输入文件必须是有效的GeoJSON格式
    2. 输出目录必须已存在
    3. 大文件处理可能需要较多内存
    4. 会保留输入文件中的所有属性和元数据
    5. 输出文件将包含更新后的CRS信息
    6. 默认使用UTF-8编码读写文件
    """
    if not Path(output_path).exists():
        # 使用 mkdir() 创建文件夹
        # parents=True: 如果父目录不存在，会一并创建。类似于Linux命令 `mkdir -p`
        # exist_ok=True: 如果文件夹已经存在，不会报错。
        Path(output_path).touch()
        print(f"文件夹 '{output_path}' 不存在，已创建。")
    with open(input_path, 'r', encoding='utf-8') as f:
        data = json.load(f)  # 读取输入文件

    converted_data = convert_geojson_data(data, src_crs, dst_crs)  # 转换数据

    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(converted_data, f, ensure_ascii=False, indent=2)  # 写入输出文件

    print(f"文件转换成功，保存至：{output_path}")

def convert_geojson_text(input_text, src_crs="EPSG:32648", dst_crs="EPSG:4326"):
    """转换GeoJSON文本中的坐标参考系统
    
    解析输入的GeoJSON文本，转换所有要素的坐标系统，
    并返回转换后的GeoJSON文本。

    参数:
        input_text (str): GeoJSON格式的文本
            必须符合GeoJSON规范
            示例: '{"type":"FeatureCollection","features":[{"type":"Feature","geometry":{"type":"Point","coordinates":[116.404,39.915]}}]}'
            
        src_crs (str): 源坐标参考系统，默认为"EPSG:32648"(UTM 48N)
            支持格式:
            - EPSG代码(如4326)
            - EPSG字符串(如"EPSG:4326")
            - WKT字符串
            - PROJ字符串
            
        dst_crs (str): 目标坐标参考系统，默认为"EPSG:4326"(WGS84)
            格式同src_crs

    返回:
        str: 转换后的GeoJSON文本
            格式化的JSON字符串，包含缩进和换行
            示例: '{\n  "type": "FeatureCollection",\n  "features": [\n    {\n      "type": "Feature",\n      "geometry": {\n        "type": "Point",\n        "coordinates": [116.404, 39.915]\n      }\n    }\n  ]\n}'

    异常:
        json.JSONDecodeError: 输入文本不是有效的JSON时抛出
        ValueError: 输入文本不符合GeoJSON规范时抛出
        RuntimeError: 坐标转换失败时抛出

    示例:
        >>> text = '{"type":"FeatureCollection","features":[{"type":"Feature","geometry":{"type":"Point","coordinates":[566353.96,2899559.66]}}]}'
        >>> convert_geojson_text(text, "EPSG:32648", "EPSG:4326")
        '{\n  "type": "FeatureCollection",\n  "features": [\n    {\n      "type": "Feature",\n      "geometry": {\n        "type": "Point",\n        "coordinates": [116.404, 39.915]\n      }\n    }\n  ]\n}'

    注意事项:
    1. 输入文本必须是有效的GeoJSON格式
    2. 返回的文本会进行格式化(缩进2个空格)
    3. 会保留输入文本中的所有属性和元数据
    4. 输出文本将包含更新后的CRS信息
    5. 使用ensure_ascii=False保留非ASCII字符
    """
    data = json.loads(input_text)  # 解析JSON文本
    converted_data = convert_geojson_data(data, src_crs, dst_crs)  # 转换数据
    return json.dumps(converted_data, ensure_ascii=False, indent=2)  # 返回JSON文本


def convert_geojson_shp(input_path, output_path, src_crs="EPSG:32648", dst_crs="EPSG:4326"):
    """
    转换Shapefile文件中的坐标参考系统
    参数:
        input_path: 输入Shapefile文件路径
        output_path: 输出Shapefile文件路径（或GeoJSON格式）
        src_crs: 源坐标参考系统（默认EPSG:32648）
        dst_crs: 目标坐标参考系统（默认EPSG:4326）
    """
    try:
        # 读取Shapefile文件
        gdf = gpd.read_file(input_path)

        # 检查是否指定了源坐标系，如果没有则使用传入的src_crs
        if gdf.crs is None:
            gdf.set_crs(src_crs, inplace=True)
        else:
            src_crs = gdf.crs

        # 转换坐标系
        gdf = gdf.to_crs(dst_crs)

        # 保存转换后的文件
        if output_path.endswith('.shp'):
            gdf.to_file(output_path, encoding='utf-8')
        else:
            # 如果输出路径不是shp文件，可以保存为GeoJSON
            gdf.to_file(output_path, driver='GeoJSON', encoding='utf-8')

        print(f"Shapefile转换成功，保存至：{output_path}")
        return output_path

    except Exception as e:
        print(f"Shapefile转换失败：{str(e)}")


if __name__ == "__main__":
    # 示例：文件转换
    convert_geojson_file(
        f'G:\\testcode\\ceshitiff\\output_actual_polygon.geojson',
        f'G:\\testcode\\ceshitiff\\output.geojson',
        "EPSG:32648",
        "EPSG:4326"
    )

    # 示例：文本转换
    sample_geojson_text = '{"type": "FeatureCollection","name": "output_bounds","crs": { "type": "name", "properties": { "name": "urn:ogc:def:crs:EPSG::32648" } },"features": [{ "type": "Feature", "properties": { }, "geometry": { "type": "Polygon", "coordinates": [ [ [ 566353.962730000028387, 2899559.663550000172108 ], [ 566353.962730000028387, 2899939.746550000272691 ], [ 565658.46473, 2899939.746550000272691 ], [ 565658.46473, 2899559.663550000172108 ], [ 566353.962730000028387, 2899559.663550000172108 ] ] ] } }]}'
    result_text = convert_geojson_text(sample_geojson_text, "EPSG:32648", "EPSG:4326")
    print(result_text)


    # 示例：Shapefile转换
    convert_geojson_shp(
        'G:\\testcode\\ceshitiff\\aaaa.shp',
        'G:\\testcode\\ceshitiff\\output_shp.shp',
        "EPSG:32648",
        "EPSG:4326"
    )
