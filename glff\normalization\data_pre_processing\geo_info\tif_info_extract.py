import rasterio
from rasterio.features import shapes
from shapely.geometry import shape, box
from shapely.ops import unary_union
import geopandas as gpd
import numpy as np
import os
from tqdm import tqdm
import warnings
from rasterio.windows import Window

# 忽略Shapely的警告
warnings.filterwarnings("ignore", category=UserWarning)

"""
    GeoTiff 文件信息提取
"""
def read_metadata(tif_path):
    """读取并返回元信息"""
    print("=== 元信息 (Metadata) ===")
    with rasterio.open(tif_path) as dataset:
        metadata = dataset.meta
        for key, value in metadata.items():
            print(f"{key}: {value}")
    return metadata


def read_rpc_info(tif_path):
    """读取并返回RPC信息"""
    print("\n=== RPC 信息 ===")
    with rasterio.open(tif_path) as dataset:
        tags = dataset.tags()
        rpc_info = {key: value for key, value in tags.items() if key.startswith('RPC_')}
        if rpc_info:
            for key, value in rpc_info.items():
                print(f"{key}: {value}")
        else:
            print("未找到 RPC 信息")
    return rpc_info if rpc_info else None


def read_bounds_and_crs(tif_path):
    """读取并返回范围信息和坐标系"""
    print("\n=== 范围信息 (Bounds) 和坐标系 (CRS) ===")
    with rasterio.open(tif_path) as dataset:
        bounds = dataset.bounds
        print(f"左下角 (minx, miny): ({bounds.left}, {bounds.bottom})")
        print(f"右上角 (maxx, maxy): ({bounds.right}, {bounds.top})")
        print(f"宽度 (width): {dataset.width} 像素")
        print(f"高度 (height): {dataset.height} 像素")

        crs = dataset.crs
        if crs:
            print(f"坐标系 (CRS): {crs}")
        else:
            print("未找到坐标系信息")

        return {
            "minx": bounds.left,
            "miny": bounds.bottom,
            "maxx": bounds.right,
            "maxy": bounds.top,
            "width": dataset.width,
            "height": dataset.height
        }, str(crs) if crs else None


def save_bounds_to_geojson(tif_path, output_path):
    """将范围信息保存为GeoJSON文件"""
    bounds, crs = read_bounds_and_crs(tif_path)
    bounds_polygon = box(bounds["minx"], bounds["miny"], bounds["maxx"], bounds["maxy"])
    bounds_gdf = gpd.GeoDataFrame({'geometry': [bounds_polygon]}, crs=crs)
    print("\n=== 范围信息多边形 (Bounds Polygon) ===")
    print(f"范围多边形对象: {bounds_polygon}")

    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    bounds_gdf.to_file(output_path, driver='GeoJSON')
    print(f"范围信息多边形已保存为 GeoJSON: {output_path}")
    return bounds_polygon


def calculate_actual_polygons(tif_path, block_size=1024, simplify_tolerance=None):
    """计算实际有效数据的多边形范围"""
    print("\n=== 实际有效数据多边形范围信息 (Polygon) ===")

    with rasterio.open(tif_path) as dataset:
        # 获取NoData值
        nodata = dataset.nodata if dataset.nodata is not None else 0

        width = dataset.width
        height = dataset.height
        cols = list(range(0, width, block_size))
        rows = list(range(0, height, block_size))

        polygons = []
        print(f"使用块处理模式，块大小: {block_size}x{block_size} 像素")

        # 使用进度条显示处理进度
        with tqdm(total=len(cols) * len(rows), desc="处理图像块") as pbar:
            for x in cols:
                for y in rows:
                    # 计算当前窗口
                    w = min(block_size, width - x)
                    h = min(block_size, height - y)
                    window = Window(x, y, w, h)

                    # 读取当前窗口的数据
                    band_data = dataset.read(1, window=window)

                    # 创建掩码
                    mask = band_data != nodata

                    if not np.any(mask):
                        pbar.update(1)
                        continue

                    # 计算当前窗口的变换矩阵
                    transform = dataset.window_transform(window)

                    # 生成多边形
                    for geom, value in shapes(mask.astype(np.uint8),
                                              mask=mask,
                                              transform=transform):
                        # 只处理值为1的区域（有效数据）
                        if value == 1:
                            polygons.append(shape(geom))

                    pbar.update(1)

    if not polygons:
        print("未找到有效数据多边形，可能是影像数据全为空或无效")
        return None

    print(f"找到 {len(polygons)} 个有效数据多边形")
    print("正在合并多边形...")

    # 合并所有多边形为一个几何对象
    merged_polygon = unary_union(polygons)

    # 简化多边形（如果指定了容差）
    if simplify_tolerance is not None:
        print(f"正在简化多边形（容差={simplify_tolerance}）...")
        merged_polygon = merged_polygon.simplify(
            tolerance=simplify_tolerance,
            preserve_topology=True
        )

    print(f"最外层连续多边形对象: {merged_polygon}")
    return merged_polygon


def save_polygon_to_geojson(tif_path, output_path, block_size=1024, simplify_tolerance=None):
    """将多边形保存为GeoJSON文件"""
    polygon = calculate_actual_polygons(tif_path, block_size, simplify_tolerance)
    if polygon is None:
        print("没有有效的多边形数据可保存")
        return

    _, crs = read_bounds_and_crs(tif_path)
    gdf = gpd.GeoDataFrame({'geometry': [polygon]}, crs=crs)
    print("GeoDataFrame 预览:")
    print(gdf)

    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    gdf.to_file(output_path, driver='GeoJSON')
    print(f"实际有效数据多边形已保存为 GeoJSON: {output_path}")


def read_tif_info(tif_path, output_bounds_geojson="output_bounds.geojson",
                  output_actual_geojson="output_actual_polygon.geojson",
                  block_size=1024, simplify_tolerance=None):
    """
    读取 TIF 文件的元信息、RPC 信息、范围信息，并计算实际有效数据的多边形范围。
    将范围信息和实际有效数据多边形保存为 GeoJSON 格式。
    实际有效数据多边形将合并为一个连续的最外层多边形（非矩形，贴近实际数据分布）。

    参数:
        tif_path (str): TIF 文件路径
        output_bounds_geojson (str): 输出范围信息的 GeoJSON 文件路径
        output_actual_geojson (str): 输出实际有效数据多边形的 GeoJSON 文件路径
        block_size (int): 处理图像时的块大小（像素），用于减少内存使用
        simplify_tolerance (float): 多边形简化容差，None表示不简化

    返回:
        dict: 包含元信息、RPC 信息、范围信息和多边形范围的字典
    """
    result = {
        "metadata": None,
        "rpc_info": None,
        "bounds": None,
        "crs": None,
        "actual_polygons": None,
        "error": None
    }

    try:
        # 1. 读取元信息 (Metadata)
        result["metadata"] = read_metadata(tif_path)

        # 2. 读取 RPC 信息
        result["rpc_info"] = read_rpc_info(tif_path)

        # 3. 读取范围信息 (Bounds) 和坐标系 (CRS)
        bounds, crs = read_bounds_and_crs(tif_path)
        result["bounds"] = bounds
        result["crs"] = crs

        # 保存范围信息为 GeoJSON
        save_bounds_to_geojson(tif_path, output_bounds_geojson)

        # 4. 计算实际有效数据的多边形范围
        actual_polygons = calculate_actual_polygons(
            tif_path,
            block_size=block_size,
            simplify_tolerance=simplify_tolerance
        )
        result["actual_polygons"] = actual_polygons

        # 保存实际有效数据多边形为 GeoJSON
        save_polygon_to_geojson(tif_path, output_actual_geojson, block_size, simplify_tolerance)

    except rasterio.errors.RasterioIOError as e:
        error_msg = f"无法打开文件: {e}"
        print(error_msg)
        result["error"] = error_msg
    except Exception as e:
        error_msg = f"处理过程中发生错误: {e}"
        print(error_msg)
        result["error"] = error_msg

    return result


if __name__ == "__main__":
    # 替换为你的 TIF 文件路径
    tif_file_path = "G:\\testcode\\ceshitiff\\ffff.tif"
    # 输出 GeoJSON 文件路径
    output_bounds_geojson_path = "G:\\testcode\\ceshitiff\\output_bounds.geojson"
    output_actual_geojson_path = "G:\\testcode\\ceshitiff\\output_actual_polygon.geojson"

    # 调用函数读取 TIF 信息
    result = read_tif_info(
        tif_file_path,
        output_bounds_geojson_path,
        output_actual_geojson_path,
        block_size=2048,  # 可以根据图像大小调整
        simplify_tolerance=0.001  # 可以根据需要调整或设为None
    )

    # 打印返回结果摘要
    print("\n=== 处理结果摘要 ===")
    if result["error"]:
        print(f"处理失败: {result['error']}")
    else:
        print("处理成功:")
        print(f"元信息: {result['metadata']}")
        print(f"RPC 信息: {result['rpc_info']}")
        print(f"范围信息: {result['bounds']}")
        print(f"坐标系: {result['crs']}")
        print(f"实际有效数据多边形: {result['actual_polygons']}")
