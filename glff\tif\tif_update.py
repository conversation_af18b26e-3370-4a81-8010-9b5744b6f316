# ===========================================================================
#              TIFF 更新器-按图幅更新
# ===========================================================================

# ----------------------------------------------------------------------------
# 导入依赖
# ----------------------------------------------------------------------------
import os
import re
import uuid
import shutil
import logging
import asyncio
from concurrent.futures import ThreadPoolExecutor
from functools import partial

# Django HTTP 支持 (模拟)
try:
    from django.http import JsonResponse, HttpResponseBadRequest
    from django.views import View
    from django.utils.decorators import method_decorator
    from django.views.decorators.csrf import csrf_exempt
except ImportError:
    print("警告：未找到Django，将使用模拟类。Web API功能将不可用。")


    class View:
        def dispatch(self, request, *args, **kwargs):
            handler = getattr(self, request.method.lower(), self.http_method_not_allowed)
            return handler(request, *args, **kwargs)

        async def post(self, request): raise NotImplementedError

        def http_method_not_allowed(self, request, *args, **kwargs): return JsonResponse(
            {'error': 'Method not allowed'}, status=405)


    class JsonResponse:
        def __init__(self, data, status=200): self.data, self.status = data, status


    class HttpResponseBadRequest(JsonResponse):
        def __init__(self, message): super().__init__({'error': message}, status=400)


    def method_decorator(decorator, name='dispatch'):
        return lambda cls: cls


    def csrf_exempt(view_func):
        return view_func

import rasterio
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from rasterio.crs import CRS
from rasterio.transform import from_bounds
from rasterio.warp import reproject, Resampling

# ----------------------------------------------------------------------------
# 日志与常量配置
# ----------------------------------------------------------------------------
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(name)s: %(message)s')
logger = logging.getLogger(__name__)

ALLOWED_EXTS = {'.tif', '.tiff'}
SAFE_DATA_ROOT = os.path.abspath('safe_data_storage')
os.makedirs(SAFE_DATA_ROOT, exist_ok=True)


# ----------------------------------------------------------------------------
# 路径与文件校验助手
# ----------------------------------------------------------------------------
def validate_and_sanitize_paths(src_rel: str, patch_rel: str, out_rel: str) -> tuple[str, str, str]:
    """校验路径是否在安全目录内，并返回绝对路径。"""
    src_abs = os.path.abspath(os.path.join(SAFE_DATA_ROOT, src_rel))
    patch_abs = os.path.abspath(os.path.join(SAFE_DATA_ROOT, patch_rel))
    out_abs = os.path.abspath(os.path.join(SAFE_DATA_ROOT, out_rel))
    if not all(p.startswith(SAFE_DATA_ROOT) for p in [src_abs, patch_abs, out_abs]):
        raise PermissionError("不允许访问安全数据目录之外的路径")
    for path in (src_abs, patch_abs):
        if not os.path.isfile(path): raise FileNotFoundError(f"找不到文件：{path}")
        ext = os.path.splitext(path)[1].lower()
        if ext not in ALLOWED_EXTS: raise ValueError(f"不支持的文件后缀 '{ext}'")
    os.makedirs(os.path.dirname(out_abs), exist_ok=True)
    return src_abs, patch_abs, out_abs


# ----------------------------------------------------------------------------
# 专业版图幅边界计算
# ----------------------------------------------------------------------------
SCALE_INFO_V2 = {
    "1:1000000": (6.0, 4.0, 1, 1, 'A'), "1:500000": (3.0, 2.0, 2, 2, 'B'),
    "1:250000": (1.5, 1.0, 4, 4, 'C'), "1:100000": (0.5, 1 / 3, 12, 12, 'D'),
    "1:50000": (0.25, 1 / 6, 24, 24, 'E'), "1:25000": (0.125, 1 / 12, 48, 48, 'F'),
    "1:10000": (1 / 16, 1 / 24, 96, 96, 'G'), "1:5000": (1 / 32, 1 / 48, 192, 192, 'H'),
}


def get_bounds_from_mapsheet(scale: str, mapsheet: str) -> tuple:
    """根据国家标准图幅编号规则计算WGS84地理边界。"""
    m = mapsheet.upper()
    if scale == "1:1000000":
        match = re.match(r'^([A-P])(\d{2})$', m)
        if not match: raise ValueError(f"1:100万图幅号格式错误：{m}")
        r, c = match.groups();
        idx_r, idx_c = ord(r) - 65, int(c)
        lon0, lat0 = (idx_c - 31) * 6.0, idx_r * 4.0
        return lon0, lat0, lon0 + 6.0, lat0 + 4.0
    if scale not in SCALE_INFO_V2: raise ValueError(f"不支持的比例尺：{scale}")
    match = re.match(r'^([A-P])(\d{2})([A-H])(\d{3})(\d{3})$', m)
    if not match: raise ValueError(f"图幅号格式错误：{m}")
    r, c, code, ir_s, ic_s = match.groups()
    lon_span, lat_span, rows, cols, exp_code = SCALE_INFO_V2[scale]
    if code != exp_code: raise ValueError(f"标识 '{code}' 与比例尺 '{scale}' 不匹配")
    ir, ic = int(ir_s), int(ic_s)
    if not (1 <= ir <= rows and 1 <= ic <= cols): raise ValueError(f"序号超出范围：({ir},{ic})")
    idx_r, idx_c = ord(r) - 65, int(c)
    lon0, lat0 = (idx_c - 31) * 6.0, idx_r * 4.0
    west = lon0 + (ic - 1) * lon_span
    south = lat0 + (rows - ir) * lat_span
    return west, south, west + lon_span, south + lat_span


# ----------------------------------------------------------------------------
# 核心逻辑 - 专业更新流程
# ----------------------------------------------------------------------------
def _core_update_pro(src: str, patch: str, out: str, bounds: tuple, inplace: bool = False,
                     resampling_method: Resampling = Resampling.nearest):
    """核心更新函数，处理所有地理空间计算和文件I/O。"""
    target = src if inplace else out
    if not inplace: shutil.copy(src, target)

    with rasterio.open(patch) as p_src, rasterio.open(target, 'r+') as dst:
        win_left, win_bottom, win_right, win_top = rasterio.warp.transform_bounds('EPSG:4326', p_src.crs, *bounds)
        p_b = p_src.bounds
        i_l, i_b, i_r, i_t = max(win_left, p_b.left), max(win_bottom, p_b.bottom), min(win_right, p_b.right), min(
            win_top, p_b.top)

        if i_l >= i_r or i_b >= i_t: raise ValueError("补丁与图幅在补丁坐标系下无重叠区域")

        src_win = rasterio.windows.from_bounds(i_l, i_b, i_r, i_t, p_src.transform)
        src_data = p_src.read(window=src_win)
        src_tf = p_src.window_transform(src_win)

        dst_i_bds = rasterio.warp.transform_bounds(p_src.crs, dst.crs, i_l, i_b, i_r, i_t)
        dst_win = rasterio.windows.from_bounds(*dst_i_bds, dst.transform).round_offsets().round_lengths()
        dst_win = dst_win.intersection(rasterio.windows.Window(0, 0, dst.width, dst.height))

        if not dst_win.width or not dst_win.height:
            logger.warning("计算出的目标窗口为空，无需更新。");
            return

        dest_arr = np.zeros((dst.count, int(dst_win.height), int(dst_win.width)), dtype=dst.dtypes[0])
        dest_tf = dst.window_transform(dst_win)

        reproject(
            source=src_data, destination=dest_arr, src_transform=src_tf, src_crs=p_src.crs,
            src_nodata=p_src.nodata, dst_transform=dest_tf, dst_crs=dst.crs,
            dst_nodata=dst.nodata, resampling=resampling_method
        )
        dst.write(dest_arr, window=dst_win)
        logger.info(f"成功将补丁 '{os.path.basename(patch)}' 更新到 '{os.path.basename(target)}'")


# ----------------------------------------------------------------------------
# 同步与异步接口函数
# ----------------------------------------------------------------------------
def update_tiff(scale: str, mapsheet: str, src: str, patch: str, out: str,
                inplace: bool = False, resampling: str = 'nearest') -> dict:
    """
    同步接口，封装所有校验和业务逻辑，返回字典结果。
    """
    try:
        resampling_method = getattr(Resampling, resampling, Resampling.nearest)
        src_abs, patch_abs, out_abs = validate_and_sanitize_paths(src, patch, out)
        bounds = get_bounds_from_mapsheet(scale, mapsheet)

        # 预检查：在代价高昂的操作前，先进行一次粗略的重叠判断
        with rasterio.open(patch_abs) as p_src:
            patch_bounds_wgs84 = rasterio.warp.transform_bounds(p_src.crs, 'EPSG:4326', *p_src.bounds)
            if not (bounds[0] < patch_bounds_wgs84[2] and bounds[2] > patch_bounds_wgs84[0] and
                    bounds[1] < patch_bounds_wgs84[3] and bounds[3] > patch_bounds_wgs84[1]):
                raise ValueError("补丁与图幅无重叠区域")

        _core_update_pro(src_abs, patch_abs, out_abs, bounds, inplace, resampling_method)
        # 关键：原地更新时，返回的输出路径应为源文件路径
        output_rel_path = src if inplace else out
        return {"status": "SUCCESS", "output": output_rel_path}
    except Exception as e:
        logger.exception("更新Tiff文件时发生错误")
        return {"status": "ERROR", "message": str(e)}


_executor = ThreadPoolExecutor(max_workers=os.cpu_count() or 4)


async def async_update_tiff(*args, **kwargs) -> dict:
    """异步接口，将同步函数放入线程池执行，避免阻塞事件循环。"""
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(_executor, partial(update_tiff, *args, **kwargs))


# ----------------------------------------------------------------------------
# Web API 接口 (Django View)
# ----------------------------------------------------------------------------
@method_decorator(csrf_exempt, name='dispatch')
class TiffUpdateView(View):
    async def post(self, request):
        task_id = str(uuid.uuid4())
        data = getattr(request, 'POST', {})
        req_params = ['scale', 'mapsheet', 'src_path', 'patch_path', 'out_path']
        if not all(p in data for p in req_params):
            return HttpResponseBadRequest(f"缺少参数: {', '.join(p for p in req_params if p not in data)}")

        kwargs = {
            'scale': data['scale'], 'mapsheet': data['mapsheet'],
            'src': data['src_path'], 'patch': data['patch_path'], 'out': data['out_path'],
            'inplace': data.get('inplace', 'false').lower() == 'true',
            'resampling': data.get('resampling', 'nearest')
        }

        result = await async_update_tiff(**kwargs)
        return JsonResponse({'task_id': task_id, **result}, status=200 if result['status'] == 'SUCCESS' else 500)


# ----------------------------------------------------------------------------
# 本地测试验证套件
# ----------------------------------------------------------------------------
def get_font(size=40):
    """安全地获取字体，如果系统字体不存在则使用默认字体。"""
    try:
        return ImageFont.truetype("arial.ttf", size)
    except IOError:
        return ImageFont.load_default()


def create_test_tiff(path, bounds, w, h, color, text=None, crs_epsg=4326):
    """创建用于测试的TIFF文件，可包含文字标签。"""
    os.makedirs(os.path.dirname(path), exist_ok=True)
    transform = from_bounds(*bounds, w, h)
    data = np.zeros((3, h, w), dtype=np.uint8);
    data[:3] = np.array(color).reshape(3, 1, 1)
    if text:
        img = Image.fromarray(data.transpose(1, 2, 0));
        draw = ImageDraw.Draw(img)
        bb = draw.textbbox((0, 0), text, font=get_font(20))
        x, y = (w - (bb[2] - bb[0])) // 2, (h - (bb[3] - bb[1])) // 2
        draw.text((x, y), text, font=get_font(20), fill=(255, 255, 255))
        data = np.array(img).transpose(2, 0, 1)
    profile = {'driver': 'GTiff', 'dtype': 'uint8', 'count': 3, 'width': w, 'height': h,
               'crs': CRS.from_epsg(crs_epsg), 'transform': transform}
    with rasterio.open(path, 'w', **profile) as dst: dst.write(data)


async def local_test():
    """
    全面的本地测试套件，覆盖各种边界情况，并提供清晰的彩色控制台输出。
    """
    TEST_ROOT_REL = 'test_tiffs'
    TEST_ROOT_ABS = os.path.join(SAFE_DATA_ROOT, TEST_ROOT_REL)
    if os.path.exists(TEST_ROOT_ABS): shutil.rmtree(TEST_ROOT_ABS)

    scale, ms = '1:50000', 'J50E001001'
    bounds = get_bounds_from_mapsheet(scale, ms)
    west, south, east, top = bounds
    mercator_bounds_overlap = (12704000, 4851000, 12705000, 4852000)

    scenarios = {
        '01_完全匹配': {
            'desc': '补丁与图幅的范围、分辨率、坐标系完全一致。',
            'patch_params': {'bounds': bounds, 'size': (512, 512), 'color': (200, 50, 50)},
        },
        '02_超大补丁': {
            'desc': '补丁范围完全覆盖并超出图幅，测试自动裁剪功能。',
            'patch_params': {'bounds': (west - 0.1, south - 0.1, east + 0.1, top + 0.1), 'size': (1024, 1024),
                             'color': (50, 200, 50)},
        },
        '03_不同坐标系': {
            'desc': '补丁为墨卡托(EPSG:3857)，与WGS84图幅有重叠，测试坐标转换。',
            'patch_params': {'bounds': mercator_bounds_overlap, 'crs': 3857, 'size': (512, 512),
                             'color': (150, 150, 50)},
        },
        '04_不同分辨率': {
            'desc': '补丁与图幅范围相同但像素尺寸不同，测试重采样。',
            'patch_params': {'bounds': bounds, 'size': (256, 256), 'color': (50, 150, 150)},
            'resampling': 'bilinear'
        },
        '05_部分重叠': {
            'desc': '补丁只覆盖图幅的右下角，测试窗口计算。',
            'patch_params': {'bounds': (west + (east - west) * 0.5, south, east, south + (top - south) * 0.5),
                             'size': (256, 256), 'color': (255, 165, 0)},
        },
        '06_原地更新': {
            'desc': '测试 inplace=True，验证源文件被直接修改。',
            'patch_params': {'bounds': (west, south, west + (east - west) * 0.5, top), 'size': (256, 512),
                             'color': (100, 50, 200)},
            'inplace': True
        },
        '07_无重叠': {
            'desc': '补丁与图幅在地理上无任何重叠区域，预期应报错。',
            'patch_params': {'bounds': (east + 0.1, top + 0.1, east + 0.2, top + 0.2), 'size': (256, 256),
                             'color': (100, 100, 100)},
            'expect_fail': True
        },
    }

    print("--- 开始本地测试 ---")
    for code, config in scenarios.items():
        print(f"\n--- 测试场景: {code} ({config['desc']}) ---")

        dir_path_rel = os.path.join(TEST_ROOT_REL, code)
        src_rel = os.path.join(dir_path_rel, "src.tif")
        patch_rel = os.path.join(dir_path_rel, "patch.tif")
        out_rel = os.path.join(dir_path_rel, "out.tif")

        create_test_tiff(os.path.join(SAFE_DATA_ROOT, src_rel), bounds, 512, 512, (20, 20, 20), text='SRC')

        patch_p = config['patch_params']
        create_test_tiff(
            path=os.path.join(SAFE_DATA_ROOT, patch_rel),
            bounds=patch_p['bounds'], w=patch_p['size'][0], h=patch_p['size'][1],
            color=patch_p['color'], text='PATCH', crs_epsg=patch_p.get('crs', 4326)
        )

        test_kwargs = {
            'scale': scale, 'mapsheet': ms, 'src': src_rel, 'patch': patch_rel, 'out': out_rel,
            'inplace': config.get('inplace', False),
            'resampling': config.get('resampling', 'nearest')
        }

        result = await async_update_tiff(**test_kwargs)

        is_success = result['status'] == 'SUCCESS'
        should_fail = config.get('expect_fail', False)

        if is_success and not should_fail:
            output_path_rel = result['output']
            print(f"[{code}] \033[92m测试通过\033[0m. 输出文件: {os.path.join(SAFE_DATA_ROOT, output_path_rel)}")
        elif not is_success and should_fail:
            print(f"[{code}] \033[92m测试通过\033[0m. 按预期抛出错误: '{result['message']}'")
        elif is_success and should_fail:
            print(f"[{code}] \033[91m测试失败\033[0m. 预期应报错但却成功了！")
        else:  # not is_success and not should_fail
            print(f"[{code}] \033[91m测试失败\033[0m. 错误原因: {result['message']}")


if __name__ == '__main__':
    asyncio.run(local_test())
