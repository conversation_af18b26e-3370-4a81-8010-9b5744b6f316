from django.apps import AppConfig


class GlffConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'glff'
    def ready(self):
        """应用启动时初始化连接池"""
        from database.create_table import DatabaseConnectionPool
        DatabaseConnectionPool()

    def __del__(self):
        """应用关闭时清理连接池"""
        from database.create_table import DatabaseConnectionPool
        if DatabaseConnectionPool._instance:
            DatabaseConnectionPool._instance.close_all()