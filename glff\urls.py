from django.urls import path
from django.shortcuts import render

from glff.tms.dynamics_tif2tiles_pro_plus import TiffDyViewPro
from glff.tms.generate_tiles import GenerateTilesView
from glff.import3d.import3dbuild import ImportDataView, ExportGeoJSONView, ProgressView
from glff.tms.tif2tiles import TiffView
from glff.tms.dynamics_tif2tiles import TiffDyView
from glff.s3torage.views import demo_res
from glff.tms.tiff_dy_processor import GenerateDyTilesView
from glff.normalization.service.views import (
    FileExtractTextView, FilePreprocessView,
    ReprojectTransformView, ReprojectBatchTransformView, ReprojectSingleCoordView, ReprojectConvertGeojsonView,
    GeoInfoShpView, GeoInfoTifView,
    ImageProcessView, MosaicView,
    AttributeCheckView, LogicalConsistencyView, TopologyCheckView, GeoIntegrityCheckView, SpatialReferenceCheckView,
    ReprojectConvertGeojsonShpView, AttributeCheckDbView
)
from glff.normalization.service.views_preprocess import PreprocessUploadView, PreprocessRunView, PreprocessDownloadView, \
    PreprocessStatusView
from glff.normalization.service.views_quality import (
    QualityUploadView, QualityRunView, QualityDownloadView, QualityStatusView,
    AttributeCheckView as QualityAttributeCheckView,
    AttributeCheckDbView as QualityAttributeCheckDbView,
    LogicalConsistencyView as QualityLogicalConsistencyView,
    TopologyView as QualityTopologyView,
    IntegrityView as QualityIntegrityView,
    SpatialReferenceView as QualitySpatialReferenceView,
    TopologyDBView, SpatialReferenceDbView
)
from glff.normalization.service.views import index_view, preprocess_view, quality_view
app_name = 'tile_service'
urlpatterns = [
    path('', index_view, name='index'),
    path('preprocess/', preprocess_view, name='preprocess'),
    path('quality/', quality_view, name='quality'),
    path("routerDemo/", demo_res),

    ##########################################TIF预览##########################################################
    # tif预览，不支持动态投影
    path('tif/tiles/view/<int:TileMatrix>/<int:TileCol>/<int:TileRow>', TiffView.as_view(), name='wmts-tile'),

    # tif预览，支持动态投影（示例：http://127.0.0.1:8000/glff/tif/tiles/dy-view/1905218442958090242/4326/0/0/0）
    path('tif/tiles/dy-view/<str:FILEID>/<int:EPSGCODE>/<int:TileMatrix>/<int:TileCol>/<int:TileRow>', TiffDyView.as_view(), name='dy-wmts-tile'),

    # tif预览
    # --- 新增的KVP模式统一入口 ---
    # 所有 ?service=WMTS... 形式的请求都将指向这个URL(http://127.0.0.1:8000/glff/wmts?service=WMTS&request=GetTile&version=1.0.0&layer=1927997513131560962&tilematrixset=EPSG_3857&tilematrix=0&tilecol=0&tilerow=0&format=image/png)
    path('wmts/', TiffDyViewPro.as_view(), name='wmts_kvp_endpoint'),
    # GetCapabilities 请求(（示例：http://127.0.0.1:8000/glff/tiles/dy-view/1905218442958090242/4326/capabilities.xml）)
    path('tiles/dy-view/<str:FILEID>/<str:EPSGCODE>/capabilities.xml',
         TiffDyViewPro.as_view(),
         name='get_capabilities_restful'),
    # GetTile 请求(（示例：http://127.0.0.1:8000/glff/tiles/dy-view/1905218442958090242/4326/0/0/0.png）)
    path('tiles/dy-view/<str:FILEID>/<str:EPSGCODE>/<str:TileMatrix>/<str:TileCol>/<str:TileRow>.png',
         TiffDyViewPro.as_view(),
         name='get_tile_restful'),
    ###########################################TIF预览结束#########################################################


    # tif为瓦片
    path('tif/tiles/generate/', GenerateTilesView.as_view(), name='wmts-generate'),
    path('tif/tiles/generate2/', GenerateDyTilesView.as_view(), name='wmts-dy-generate'),

    # 处理 3d 文件的导入 包括导入、导出为geojson、查看导入进入三个方法
    path('import/', ImportDataView.as_view(), name='import-data'),
    path('export/', ExportGeoJSONView.as_view(), name='export-geojson'),
    path('progress/', ProgressView.as_view(), name='progress'),

    # normalization标准化与质检API
    path('normalization/file/extract_text/', FileExtractTextView.as_view(), name='normalization-file-extract-text'),
    path('normalization/file/preprocess/', FilePreprocessView.as_view(), name='normalization-file-preprocess'),
    path('normalization/reproject/transform/', ReprojectTransformView.as_view(), name='normalization-reproject-transform'),
    path('normalization/reproject/batch_transform/', ReprojectBatchTransformView.as_view(), name='normalization-reproject-batch-transform'),
    path('normalization/reproject/transform_single_coord/', ReprojectSingleCoordView.as_view(), name='normalization-reproject-single-coord'),
    path('normalization/reproject/convert_geojson/', ReprojectConvertGeojsonView.as_view(), name='normalization-reproject-convert-geojson'),
    path('normalization/reproject/convert_geojson_shp/', ReprojectConvertGeojsonShpView.as_view(), name='normalization-reproject-convert-geojson-shp'),
    path('normalization/geo_info/shp_info/', GeoInfoShpView.as_view(), name='normalization-geo-info-shp'),
    path('normalization/geo_info/tif_info/', GeoInfoTifView.as_view(), name='normalization-geo-info-tif'),
    path('normalization/image/process/', ImageProcessView.as_view(), name='normalization-image-process'),
    path('normalization/mosaic/', MosaicView.as_view(), name='normalization-mosaic'),
    path('normalization/quality/attribute_check/', AttributeCheckView.as_view(), name='normalization-quality-attribute-check'),
    path('normalization/quality/logical_consistency/', LogicalConsistencyView.as_view(), name='normalization-quality-logical-consistency'),
    path('normalization/quality/topology/', TopologyCheckView.as_view(), name='normalization-quality-topology'),
    path('normalization/quality/integrity/', GeoIntegrityCheckView.as_view(), name='normalization-quality-integrity'),
    path('normalization/quality/spatial_reference/', SpatialReferenceCheckView.as_view(), name='normalization-quality-spatial-reference'),
    path('normalization/quality/attribute_check_db/', AttributeCheckDbView.as_view(), name='normalization-quality-attribute-check-db'),
    path('normalization/quality/topology_db/', TopologyDBView.as_view(), name='normalization-quality-topology-db'),
]


urlpatterns += [
    path('api/preprocess/upload/', PreprocessUploadView.as_view(), name='preprocess_upload'),
    path('api/preprocess/run/', PreprocessRunView.as_view(), name='preprocess_run'),
    path('api/preprocess/status/', PreprocessStatusView.as_view(), name='preprocess_status'),
    path('api/preprocess/download/', PreprocessDownloadView.as_view(), name='preprocess_download'),
    path('api/quality/upload/', QualityUploadView.as_view(), name='quality_upload'),
    path('api/quality/run/', QualityRunView.as_view(), name='quality_run'),
    path('api/quality/status/', QualityStatusView.as_view(), name='quality_status'),
    path('api/quality/download/', QualityDownloadView.as_view(), name='quality_download'),
    # 直接质量检查API端点（兼容现有测试）
    path('api/quality/attribute_check/', QualityAttributeCheckView.as_view(), name='quality_attribute_check'),
    path('api/quality/attribute_check_db/', QualityAttributeCheckDbView.as_view(), name='quality_attribute_check_db'),
    path('api/quality/logical_consistency/', QualityLogicalConsistencyView.as_view(), name='quality_logical_consistency'),
    path('api/quality/topology/', QualityTopologyView.as_view(), name='quality_topology'),
    path('api/quality/integrity/', QualityIntegrityView.as_view(), name='quality_integrity'),
    path('api/quality/spatial_reference/', QualitySpatialReferenceView.as_view(), name='quality_spatial_reference'),
    path('api/quality/spatial_reference_db/', SpatialReferenceDbView.as_view(), name='quality_spatial_reference_db'),
    path('api/quality/db_integrity/', QualityRunView.as_view(), name='quality-db-integrity'),
]