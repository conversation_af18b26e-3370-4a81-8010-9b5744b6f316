"""
Django settings for nank_geo project.

Generated by 'django-admin startproject' using Django 5.1.6.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""
import platform
from pathlib import Path
import os
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from django.urls import path

# PROJ_LIB_PATH = os.environ.get('PROJ_LIB')

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-xdgqb%)dczf7l*^zy%mt25r39$jvmd$f0sw7)u$1glgn@_mq+h'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = []


# 自建 S3 存储配置
AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID', 'admin')
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY', '12345678')
AWS_STORAGE_BUCKET_NAME = os.getenv('AWS_STORAGE_BUCKET_NAME', 'system2')
AWS_S3_ENDPOINT_URL = os.getenv('AWS_S3_ENDPOINT_URL', 'http://*************:18090')  # 自建 S3 的 URL
AWS_S3_REGION_NAME = os.getenv('AWS_S3_REGION_NAME', 'us-east-1')  # 可以设置为任何值
# 自定义 S3 存储的配置
AWS_S3_OBJECT_PARAMETERS = {
    'CacheControl': 'max-age=86400',
}
# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'glff', # 管理分发
    'django.contrib.gis', # 激活geodjango
    'rest_framework', # 引入APIVIEW
    'storages', # 引入S3Storage
    'corsheaders', # 允许跨域
    'drf_yasg',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    # 'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
]


#跨域增加忽略
# 在 setting.py 末尾添加以下三行
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_HEADERS = ('*')

ROOT_URLCONF = 'nank_geo.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'nank_geo.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/

STATIC_URL = 'static/'

STATICFILES_DIRS = [
    BASE_DIR / 'templates' / 'lib',
    BASE_DIR / 'templates' / 'webfonts',
]

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# # 根据操作系统动态配置GDAL和程序文件路径
# if platform.system() == "Windows":
#     # Windows配置
#     GDAL_LIBRARY_PATH = r"H:\conda\miniforge3\envs\py31013\Library\bin\gdal.dll"
#     GEOS_LIBRARY_PATH = r"H:\conda\miniforge3\envs\py31013\Library\bin\geos_c.dll"
#     SKFC_TIF_PATH = r"D:\data\tif\\"  # 目录路径
# else:
#     # Linux配置
#     GDAL_LIBRARY_PATH = "/usr/local/src/python-gdal/gdal-3.10.1/build/libgdal.so"
#     GEOS_LIBRARY_PATH = "/usr/lib/x86_64-linux-gnu/libgeos_c.so"
#     SKFC_TIF_PATH = "/mnt/data/tif/"  # 目录路径

# # 验证路径
# if not os.path.isdir(SKFC_TIF_PATH):
#     os.makedirs(SKFC_TIF_PATH)  # 自动创建目录（可选）

GDAL_LIBRARY_PATH = os.environ.get('GDAL_LIBRARY_PATH')
GEOS_LIBRARY_PATH = os.environ.get('GEOS_LIBRARY_PATH')
SKFC_TIF_PATH = os.environ.get('SKFC_TIF_PATH')

# 如果需要兼容不同操作系统，可以在 .env 里维护不同环境的变量，或用条件判断
# if not SKFC_TIF_PATH:
#     # fallback 默认值
#     if platform.system() == "Windows":
#         SKFC_TIF_PATH = r"D:/data/tif/"
#     else:
#         SKFC_TIF_PATH = "/mnt/data/tif/"

# if not os.path.isdir(SKFC_TIF_PATH):
#     os.makedirs(SKFC_TIF_PATH, exist_ok=True)



# 设置 PROJ_LIB 路径（根据实际环境修改）/usr/share/proj/
# os.environ['PROJ_LIB'] = r'H:\conda\miniforge3\envs\py31013\Library\share\proj'

if not os.path.isfile(GDAL_LIBRARY_PATH):
    raise RuntimeError(f"GDAL库文件未找到: {GDAL_LIBRARY_PATH}")


# settings.py

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',  # 使用 PostgreSQL 数据库
        'NAME': 'skgeo-manager',  # 数据库名称
        'USER': 'postgres',  # 数据库用户
        'PASSWORD': 'postgres',  # 数据库密码
        'HOST': '127.0.0.1',  # 数据库主机
        'PORT': '5432',  # 数据库端口
    }
}


# 增加redis
RQ_QUEUES = {
    'default': {
        'HOST': 'localhost',
        'PORT': 6379,
        'DB': 0,
        'DEFAULT_TIMEOUT': 360,
    }
}
INSTALLED_APPS += ['django_rq',]


REDIS_CONFIG = {
    "HOST": "localhost",    # Redis服务器地址
    "PORT": 6379,           # 端口号
    "DB": 0,                # 数据库编号
    # "PASSWORD": "yourpassword",  # 如果有密码可以启用这一行
}


# juicefs posix的挂载路径 / 也可以是本地的路径
# JFS_PATH = r'Z://'

JFS_PATH = os.environ.get('JFS_PATH', r'Z://')  # 后面是默认值，可选

# 文件上传配置
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# 文件上传大小限制
DATA_UPLOAD_MAX_MEMORY_SIZE = 10485760  # 10MB
FILE_UPLOAD_MAX_MEMORY_SIZE = 10485760  # 10MB

# 允许的文件类型
ALLOWED_FILE_TYPES = [
    '.txt', '.doc', '.docx', '.pdf', '.shp', '.shx', '.dbf', '.prj',
    '.tif', '.tiff', '.geojson', '.json', '.png', '.jpg', '.jpeg',
    '.csv', '.xls', '.xlsx','.zip','.img'
]

# 最大文件大小 (50MB)
MAX_FILE_SIZE = 5242880000
