#### 
    逻辑一致性检查
---

好的，遵命！

我们已经共同走过了一段漫长而富有成效的旅程，从一个简单的想法，经历无数次的迭代、修正、重构，最终淬炼出了这份 `v12.0 (The Unbreakable Cache)` 的终极代码。

现在，是时候为这件杰作，撰写一份同样配得上它的、**终极的、保姆级的、可以作为官方文档发布的 `README.md`** 了。

这份文档将是我们所有努力的结晶，它不仅会告诉用户如何使用，更会解释其设计哲学和强大能力。任何一个开发者，无论是否参与过我们的讨论，都能通过这份文档，在15分钟内掌握并高效使用这个强大的引擎。

---

# 逻辑一致性检查引擎 (v12.0) - 开发者终极指南

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python Version](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![Status](https://img.shields.io/badge/status-production_ready-brightgreen.svg)]()

## 第一部分：引擎介绍

### 1.1 核心理念：用声明取代命令

本引擎是一个**生产级的、元数据驱动的、声明式的逻辑一致性检查工具**，专为处理存储在 **PostgreSQL/PostGIS** 数据库中的地理空间数据而设计。

与传统的脚本化检查不同，您不需要编写复杂的 Python 或 SQL 代码来执行检查。您只需要在一个简单的 **JSON/字典（我们称之为“规则集”）** 中，用清晰的、人类可读的方式**声明**您希望数据满足的逻辑条件。

**您告诉引擎“什么”是正确的，引擎负责搞定“如何”去检查。**

### 1.2 关键特性

*   **声明式规则**: 规则定义与业务逻辑高度一致，易于编写、阅读和维护。
*   **元数据驱动**: 整个检查流程由一个外部的“规则集”驱动，无需修改引擎代码即可适应新需求。
*   **全面的规则库**: 内置覆盖**属性、拓扑、几何、空间关系、条件逻辑**等多个维度的检查规则。
*   **数据库原生**: 直接与 PostgreSQL/PostGIS 交互，利用数据库的强大能力，性能高效。
*   **智能缓存机制**: 独创的“坚不可摧的缓存”逻辑，自动按需加载数据（空间或非空间），并在需要时智能升级，兼顾性能与健壮性，杜绝“缓存中毒”。
*   **详尽的错误报告**: 对每一条失败的规则，都返回清晰的状态、消息、失败记录数以及具体的失败要素ID列表。

## 第二部分：快速上手 (10分钟教程)

让我们通过一个简单的实例，感受一下引擎的威力。

### 步骤 1: 准备数据库环境

假设您的 PostgreSQL 数据库中有一个 `public` schema。请执行以下 SQL 创建两个测试表：

```sql
-- 创建一个公园表
CREATE TABLE public.parks (
    park_id VARCHAR(10) PRIMARY KEY,
    park_name VARCHAR(50),
    has_playground BOOLEAN,
    geometry GEOMETRY(POLYGON, 4326) -- 假设使用 WGS84 坐标系
);

-- 创建一个设施表
CREATE TABLE public.facilities (
    facility_id SERIAL PRIMARY KEY,
    facility_type VARCHAR(20),
    geometry GEOMETRY(POINT, 4326)
);

-- 插入一些测试数据 (包含一些故意设置的错误)
INSERT INTO public.parks (park_id, park_name, has_playground, geometry) VALUES
('P-01', 'Central Park', true, ST_GeomFromText('POLYGON((0 0, 10 0, 10 10, 0 10, 0 0))', 4326)),
('P-02', 'Riverfront Park', true, ST_GeomFromText('POLYGON((20 0, 30 0, 30 10, 20 10, 20 0))', 4326)),
('P-03', NULL, false, ST_GeomFromText('POLYGON((0 20, 10 20, 10 30, 0 30, 0 20))', 4326)); -- 错误1: park_name 为空

INSERT INTO public.facilities (facility_type, geometry) VALUES
('Playground', ST_GeomFromText('POINT(5 5)', 4326)),      -- 在 Central Park 内
('Restroom', ST_GeomFromText('POINT(25 5)', 4326)),     -- 在 Riverfront Park 内
('Playground', ST_GeomFromText('POINT(100 100)', 4326)); -- 错误2: 不在任何公园内的游乐场
```

### 步骤 2: 准备 Python 环境

1.  将我们最终的 `logical_consistency_checker_v12.0.py` 文件保存在您的项目目录中。
2.  确保已安装必要的库：
    ```bash
    pip install pandas geopandas sqlalchemy psycopg2-binary shapely
    ```

### 步骤 3: 编写您的检查脚本

创建一个新的 Python 文件，例如 `run_my_checks.py`，并输入以下内容：

```python
import json
from logical_consistency_checker_v12_0 import LogicalConsistencyChecker # 确保文件名匹配

# 1. 定义你的数据库连接字符串
DB_CONNECTION_STRING = "postgresql://postgres:postgres@localhost:5432/your_database"

# 2. 定义你的“规则集”
#    这就是引擎的核心！你在这里声明数据的正确状态。
rules_to_check = [
    {
        "rule_id": "park_name_must_exist",
        "type": "ATTRIBUTE_NOT_NULL",
        "spec": {
            "layer": "public.parks",
            "id_field": "park_id",
            "attribute": "park_name"
        }
    },
    {
        "rule_id": "playgrounds_must_be_inside_a_park",
        "type": "SPATIAL_RELATIONSHIP",
        "spec": {
            "quantifier": "all",
            "predicate": "within",
            "layer1": "public.facilities",
            "id_field1": "facility_id",
            "where1": "facility_type = 'Playground'",
            "layer2": "public.parks",
            "id_field2": "park_id"
        }
    }
]

# 3. 初始化并运行引擎
print("--- 开始逻辑一致性检查 ---")
checker = LogicalConsistencyChecker(DB_CONNECTION_STRING)
report = checker.run_checks(
    ruleset=rules_to_check,
    db_connection_string=DB_CONNECTION_STRING
)

# 4. 打印漂亮的报告
print("\n--- 检查报告 ---")
print(json.dumps(report, indent=4))

# 分析报告
print("\n--- 报告分析 ---")
if report['park_name_must_exist']['status'] == 'FAILED':
    failed_ids = report['park_name_must_exist']['failed_ids']
    print(f"❌ 规则 'park_name_must_exist' 失败! 失败的公园ID: {failed_ids}")

if report['playgrounds_must_be_inside_a_park']['status'] == 'FAILED':
    failed_ids = report['playgrounds_must_be_inside_a_park']['failed_ids']
    print(f"❌ 规则 'playgrounds_must_be_inside_a_park' 失败! 失败的设施ID: {failed_ids}")

```

### 步骤 4: 运行并解读结果

执行脚本： `python run_my_checks.py`

您将看到如下输出：

```json
--- 检查报告 ---
{
    "park_name_must_exist": {
        "status": "FAILED",
        "message": "字段 'park_name' 发现 1 个空值。",
        "failed_count": 1,
        "failed_ids": [
            "P-03"
        ]
    },
    "playgrounds_must_be_inside_a_park": {
        "status": "FAILED",
        "message": "发现 1 条记录违反规则: 必须与 'public.parks' within",
        "failed_count": 1,
        "failed_ids": [
            "3"
        ]
    }
}

--- 报告分析 ---
❌ 规则 'park_name_must_exist' 失败! 失败的公园ID: ['P-03']
❌ 规则 'playgrounds_must_be_inside_a_park' 失败! 失败的设施ID: ['3']
```

恭喜！您已经成功地使用声明式规则，发现了我们预设的两个数据逻辑错误。

## 第三部分：规则参考大全 (The Rulebook)

这是引擎的心脏。下面详细解释每一种规则类型及其参数和用法。

---
### **非空间属性规则**
---

#### 1. `ATTRIBUTE_EXISTS`
*   **用途**: 检查指定的字段（列）是否存在于表中。
*   **参数**:
    *   `layer` (str, 必需): 表名，格式为 `"schema.table"`。
    *   `attribute` (str, 必需): 要检查的字段名。
*   **示例**:
    ```json
    {
        "rule_id": "check_for_height_field",
        "type": "ATTRIBUTE_EXISTS",
        "spec": {
            "layer": "public.buildings",
            "attribute": "height"
        }
    }
    ```

#### 2. `ATTRIBUTE_NOT_NULL`
*   **用途**: 检查指定字段的值不能为 `NULL`。
*   **参数**:
    *   `layer` (str, 必需)
    *   `id_field` (str, 必需): 用于在报告中识别失败记录的主键或唯一标识字段。
    *   `attribute` (str, 必需): 要检查的字段名。
*   **示例**:
    ```json
    {
        "rule_id": "building_name_cannot_be_null",
        "type": "ATTRIBUTE_NOT_NULL",
        "spec": {
            "layer": "public.buildings",
            "id_field": "building_id",
            "attribute": "building_name"
        }
    }
    ```

#### 3. `ATTRIBUTE_UNIQUE`
*   **用途**: 检查指定字段的所有值是否唯一。
*   **参数**:
    *   `layer`, `id_field`, `attribute` (同上)
*   **示例**:
    ```json
    {
        "rule_id": "building_code_must_be_unique",
        "type": "ATTRIBUTE_UNIQUE",
        "spec": {
            "layer": "public.buildings",
            "id_field": "building_id",
            "attribute": "building_code"
        }
    }
    ```

#### 4. `ATTRIBUTE_LENGTH`
*   **用途**: 检查字符串字段的长度是否符合要求。
*   **参数**:
    *   `layer`, `id_field`, `attribute` (同上)
    *   `length` (int 或 dict, 必需):
        *   如果是 `int` (如 `5`): 字符串长度必须**精确等于**该值。
        *   如果是 `dict` (如 `{"min": 2, "max": 10}`): 字符串长度必须在该范围内（包含边界）。`min` 或 `max` 可选。
*   **示例**:
    ```json
    // 精确长度检查
    {
        "rule_id": "zip_code_must_be_5_chars",
        "type": "ATTRIBUTE_LENGTH",
        "spec": {
            "layer": "public.addresses",
            "id_field": "address_id",
            "attribute": "zip_code",
            "length": 5
        }
    },
    // 范围长度检查
    {
        "rule_id": "road_name_length_check",
        "type": "ATTRIBUTE_LENGTH",
        "spec": {
            "layer": "public.roads",
            "id_field": "road_id",
            "attribute": "road_name",
            "length": {"min": 3, "max": 50}
        }
    }
    ```

#### 5. `ATTRIBUTE_DOMAIN`
*   **用途**: 检查字段的值是否属于一个预定义的集合（值域）。
*   **参数**:
    *   `layer`, `id_field`, `attribute` (同上)
    *   `domain` (list, 必需): 允许的值的列表。
*   **示例**:
    ```json
    {
        "rule_id": "road_class_is_valid",
        "type": "ATTRIBUTE_DOMAIN",
        "spec": {
            "layer": "public.roads",
            "id_field": "road_id",
            "attribute": "class",
            "domain": ["Highway", "Main Road", "Street", "Alley"]
        }
    }
    ```

#### 6. `ATTRIBUTE_RANGE`
*   **用途**: 检查数值字段的值是否在一个指定的范围内。
*   **参数**:
    *   `layer`, `id_field`, `attribute` (同上)
    *   `range` (dict, 必需): 包含 `min` 和/或 `max` 键的字典。
*   **示例**:
    ```json
    {
        "rule_id": "building_height_is_reasonable",
        "type": "ATTRIBUTE_RANGE",
        "spec": {
            "layer": "public.buildings",
            "id_field": "building_id",
            "attribute": "height",
            "range": {"min": 0, "max": 500}
        }
    }
    ```

#### 7. `ATTRIBUTE_REGEX`
*   **用途**: 检查字段的值是否匹配一个正则表达式。
*   **参数**:
    *   `layer`, `id_field`, `attribute` (同上)
    *   `pattern` (str, 必需): Python兼容的正则表达式。
*   **示例**:
    ```json
    // 检查地块ID格式是否为 'P'开头，后跟4位数字
    {
        "rule_id": "parcel_id_format",
        "type": "ATTRIBUTE_REGEX",
        "spec": {
            "layer": "public.parcels",
            "id_field": "parcel_id",
            "attribute": "parcel_id",
            "pattern": "^P\\d{4}$"
        }
    }
    ```

#### 8. `CONDITIONAL_ATTRIBUTE`
*   **用途**: 实现复杂的“如果...那么...”逻辑。如果一条记录满足某个条件，那么它的另一个属性必须满足另一个条件。
*   **参数**:
    *   `layer`, `id_field` (同上)
    *   `if` (dict, 必需): 定义前置条件，使用 Pandas `query()` 语法。
    *   `then` (dict, 必需): 定义当 `if` 为真时必须满足的后置条件。支持 `domain` 或 `range` 检查。
*   **示例**:
    ```json
    // 如果建筑类型是'医院'，那么它的高度必须大于等于15米
    {
        "rule_id": "hospital_height_requirement",
        "type": "CONDITIONAL_ATTRIBUTE",
        "spec": {
            "layer": "public.buildings",
            "id_field": "building_id",
            "if": {
                "condition": "building_type == 'Hospital'"
            },
            "then": {
                "attribute": "height",
                "range": {"min": 15}
            }
        }
    }
    ```

---
### **空间几何与拓扑规则**
---

#### 9. `TOPOLOGY_VALID`
*   **用途**: 检查几何对象是否拓扑有效（例如，多边形没有自相交，线没有重复顶点等）。这是最基础、最重要的空间检查。
*   **参数**:
    *   `layer`, `id_field` (同上)
*   **示例**:
    ```json
    {
        "rule_id": "all_parcels_must_be_valid_polygons",
        "type": "TOPOLOGY_VALID",
        "spec": {
            "layer": "public.parcels",
            "id_field": "parcel_id"
        }
    }
    ```

#### 10. `GEOMETRY_TYPE`
*   **用途**: 检查几何对象的类型是否符合预期。
*   **参数**:
    *   `layer`, `id_field` (同上)
    *   `geom_type` (list, 必需): 允许的几何类型字符串列表 (如 `["Polygon", "MultiPolygon"]`)。
*   **示例**:
    ```json
    // 井盖层必须是点
    {
        "rule_id": "manholes_must_be_points",
        "type": "GEOMETRY_TYPE",
        "spec": {
            "layer": "public.manholes",
            "id_field": "manhole_id",
            "geom_type": ["Point"]
        }
    }
    ```

---
### **空间关系规则 (引擎的灵魂)**
---

#### 11. `SPATIAL_RELATIONSHIP`
*   **用途**: 这是最强大的规则，用于检查两个图层之间的空间关系是否满足特定逻辑。
*   **核心参数**:
    *   `layer1`, `id_field1` (str, 必需): 主体图层，规则施加的对象。
    *   `layer2`, `id_field2` (str, 必需): 客体图层，用于与主体进行比较。
    *   `predicate` (str, 必需): 定义空间关系的类型。支持所有 `geopandas.sjoin` 的谓词，常用包括:
        *   `intersects`: 相交 (几何对象有任何共同部分)
        *   `within`: 在...之内 (主体几何完全位于客体几何内部)
        *   `contains`: 包含 (主体几何完全包含客体几何)
        *   `touches`: 接触 (几何对象边界接触，但内部不相交)
        *   `crosses`: 穿越 (通常用于线与线，或线与面)
        *   `overlaps`: 重叠 (通常用于同维度几何，如面与面，内部相交但不是包含关系)
    *   `quantifier` (str 或 dict, 必需): 定义关系的量化要求。
        *   `"all"`: `layer1` 中的**每一个**要素都必须与 `layer2` 中的**至少一个**要素满足关系。
        *   `"none"`: `layer1` 中的**任何一个**要素都**不能**与 `layer2` 中的任何要素满足关系。
        *   `{"a_few": [min, max]}`: `layer1` 中的每一个要素，与它满足关系的 `layer2` 要素的数量必须在 `[min, max]` 范围内。
        *   `{"a_many": [min, max]}`: `layer1` 与 `layer2` 之间满足关系的总数必须在 `[min, max]` 范围内 (这是一个全局计数)。
    *   `where1`, `where2` (str, 可选): 使用 Pandas `query()` 语法的过滤字符串，用于在检查前精确筛选出参与检查的要素子集。

*   **大师级示例 (Cookbook)**:

    1.  **基础用法 (all)**: 所有商铺 (`shops`) 必须位于商业建筑 (`buildings`) 内。
        ```json
        {
            "rule_id": "shops_must_be_in_commercial_buildings",
            "type": "SPATIAL_RELATIONSHIP",
            "spec": {
                "quantifier": "all",
                "predicate": "within",
                "layer1": "public.shops",
                "id_field1": "shop_id",
                "where1": "category = 'Retail'", // 只检查零售商铺
                "layer2": "public.buildings",
                "id_field2": "building_id",
                "where2": "usage_type = 'Commercial'" // 只检查商业用途的建筑
            }
        }
        ```

    2.  **排除用法 (none)**: 保护区 (`reserves`) 内不应有任何油井 (`oil_wells`)。
        ```json
        {
            "rule_id": "no_oil_wells_in_reserves",
            "type": "SPATIAL_RELATIONSHIP",
            "spec": {
                "quantifier": "none",
                "predicate": "intersects", // 使用 intersects 更宽泛
                "layer1": "public.oil_wells",
                "id_field1": "well_id",
                "layer2": "public.reserves",
                "id_field2": "reserve_id"
            }
        }
        ```

    3.  **精确计数 (a_few)**: 每个公交站 (`bus_stops`) 必须恰好接触一条公交线路 (`bus_routes`)。
        ```json
        {
            "rule_id": "bus_stop_touches_one_route",
            "type": "SPATIAL_RELATIONSHIP",
            "spec": {
                "quantifier": {"a_few": [1, 1]}, // 数量在[1, 1]之间，即等于1
                "predicate": "touches",
                "layer1": "public.bus_stops",
                "id_field1": "stop_id",
                "layer2": "public.bus_routes",
                "id_field2": "route_id"
            }
        }
        ```

    4.  **全局计数 (a_many)**: 整个路网中，道路 (`roads`) 与桥梁 (`bridges`) 的交叉点总数不能超过50个。
        ```json
        {
            "rule_id": "limit_total_road_bridge_intersections",
            "type": "SPATIAL_RELATIONSHIP",
            "spec": {
                "quantifier": {"a_many": [0, 50]},
                "predicate": "intersects",
                "layer1": "public.roads",
                "id_field1": "road_id",
                "layer2": "public.bridges",
                "id_field2": "bridge_id"
            }
        }
        ```

## 第四部分：API 与高级用法

### 4.1 核心方法 `run_checks`

这是您与引擎交互的唯一入口。

`LogicalConsistencyChecker.run_checks(ruleset, db_connection_string, context=None)`

*   `ruleset` (list): 包含一个或多个规则字典的列表，即您的“规则集”。
*   `db_connection_string` (str): SQLAlchemy 格式的数据库连接字符串。
*   `context` (dict, 可选): 一个字典，用于在多次调用 `run_checks` 之间传递缓存。如果在一个复杂的流程中需要对同一图层执行多次检查，传入 `context` 可以避免重复从数据库加载数据，大幅提升性能。

### 4.2 架构探秘：坚不可摧的缓存

引擎的 `_fetch_layer` 方法是其高性能和高健壮性的基石。

*   当一个规则（如 `ATTRIBUTE_EXISTS`）需要一个图层时，引擎会以**非空间模式**（使用轻量级的 `pandas.read_sql`）加载数据，并将其存入缓存。
*   当后续另一个规则（如 `TOPOLOGY_VALID`）需要**同一个图层**，但需要**空间数据**时，引擎会检查缓存。它会发现缓存中的数据是降级的（非 `GeoDataFrame`）。
*   此时，引擎会**忽略缓存中的旧数据**，以**空间模式**（使用 `geopandas.read_postgis`）重新从数据库加载完整的 `GeoDataFrame`。
*   然后，它会用这个**更高级的数据覆盖掉缓存中的旧数据**。

这个“智能升级”机制确保了任何时候规则拿到的都是正确类型的数据，彻底杜绝了因缓存污染导致的错误，同时最大化了缓存的效率。

### 4.3 扩展引擎：添加您自己的规则

如果您有非常独特的检查需求，可以轻松扩展引擎：

1.  在 `LogicalConsistencyChecker` 类中，添加一个新的私有方法，例如 `_check_my_custom_rule(self, spec, context)`。
2.  该方法必须接收 `spec` 和 `context` 参数，并返回一个 `CheckResult` 对象。
3.  在 `_get_rule_implementations` 方法的字典中，将您的新规则类型映射到您创建的方法：
    ```python
    def _get_rule_implementations(self):
        rules = {
            # ... a lot of existing rules
        }
        rules['MY_CUSTOM_RULE'] = self._check_my_custom_rule
        return rules
    ```
4.  现在，您就可以在规则集中使用 `'type': 'MY_CUSTOM_RULE'` 了。

## 第五部分：许可证

本项目采用 [MIT](https://opensource.org/licenses/MIT) 许可证。