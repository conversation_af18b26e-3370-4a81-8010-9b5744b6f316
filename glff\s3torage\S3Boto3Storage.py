import json
import boto3
from storages.backends.s3boto3 import S3Boto3Storage
from django.conf import settings

class CustomS3Storage(S3Boto3Storage):
    def __init__(self, bucket_name=None, *args, **kwargs):
        if bucket_name:
            self.bucket_name = bucket_name
        else:
            self.bucket_name = kwargs.pop('bucket_name', None) or settings.AWS_STORAGE_BUCKET_NAME
        super().__init__(*args, **kwargs)

        # 创建 S3 客户端，确保使用自建 S3 的端点 URL
        self.s3_client = boto3.client(
            's3',
            endpoint_url=settings.AWS_S3_ENDPOINT_URL,  # 使用自建 S3 的 URL
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_S3_REGION_NAME  # 可以设置为任何值
        )

    def save(self, name, content, **kwargs):
        # 调用父类的 save 方法
        obj = super().save(name, content, **kwargs)

        # 可选：设置桶策略（如果需要）
        self.set_bucket_policy()

        return obj

    def set_bucket_policy(self):
        # 定义桶策略
        bucket_policy = {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Effect": "Allow",
                    "Principal": "*",
                    "Action": [
                        "s3:PutObject",
                        "s3:GetObject"
                    ],
                    "Resource": f"arn:aws:s3:::{self.bucket_name}/*"
                }
            ]
        }

        # 将策略转换为 JSON 字符串
        bucket_policy_json = json.dumps(bucket_policy)

        # 设置桶的策略
        self.s3_client.put_bucket_policy(
            Bucket=self.bucket_name,
            Policy=bucket_policy_json
        )
