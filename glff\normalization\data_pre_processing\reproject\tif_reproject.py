import os
import logging
import sys

import osgeo
from osgeo import gdal, osr


# os.environ['PROJ_LIB'] = r'H:\conda\miniforge3\envs\py31013\Library\share\proj'
# 配置GDAL日志
gdal.UseExceptions()  # 推荐，让GDAL错误以Python异常形式抛出


# 移除了 chunk_size，因为它不直接用于 gdal.Warp
class CompatibleGeoTiffTransformer:
    """兼容单精度环境的大文件投影/坐标系转换工具
    
    基于GDAL Warp实现的高效GeoTIFF坐标转换工具，支持：
    - 单精度(Float32)和双精度(Float64)数据
    - 大文件处理（通过内存限制控制）
    - 多种重采样方法
    - 批量转换
    - 七参数坐标转换
    
    主要功能：
    1. 单文件坐标转换（transform方法）
    2. 批量文件转换（batch_transform方法）
    3. 自动精度控制（根据输入数据自动选择单/双精度）
    4. 内存优化处理
    
    使用示例：
    >>> # 单文件转换
    >>> transformer = CompatibleGeoTiffTransformer(max_memory_mb=2048)
    >>> success = transformer.transform(
    ...     input_path="input.tif",
    ...     output_path="output.tif",
    ...     target_crs="EPSG:4326"
    ... )
    
    >>> # 批量转换
    >>> results = transformer.batch_transform(
    ...     input_files=["file1.tif", "file2.tif"],
    ...     output_dir="output",
    ...     target_crs="EPSG:3857"
    ... )
    
    注意事项：
    1. 输入文件必须是有效的GeoTIFF文件
    2. 输出目录必须存在或有写入权限
    3. 大文件处理时适当增加max_memory_mb参数
    4. 七参数转换需要提供完整的转换参数
    """

    def __init__(self, input_path=None, output_path=None, temp_dir=None,  # temp_dir 几乎不用
                 max_memory_mb=4096, precision='auto', logger=None):
        self.input_path = input_path
        self.output_path = output_path
        # self.temp_dir = temp_dir or tempfile.gettempdir() # gdal.Warp不直接用，除非用于其他辅助操作
        
        # 跳过路径验证和GDAL初始化（如果路径未提供）
        if input_path is None or output_path is None:
            self.src_ds = None
            self.logger = logger or logging.getLogger(self.__class__.__name__)
            return
        self.max_memory_bytes = int(max_memory_mb * 1024 * 1024)  # **重要：转换为字节**
        self.logger = logger or logging.getLogger(self.__class__.__name__)

        if not os.path.isfile(self.input_path):
            self.logger.error(f"输入文件不存在: {self.input_path}")
            raise FileNotFoundError(f"输入文件不存在: {self.input_path}")

        output_dir = os.path.dirname(self.output_path)
        if output_dir:  # 确保目录名不为空（例如，如果输出路径只是文件名）
            os.makedirs(output_dir, exist_ok=True)

        self.src_ds = gdal.Open(self.input_path, gdal.GA_ReadOnly)
        if not self.src_ds:
            self.logger.error(f"无法打开输入文件: {self.input_path} (GDAL error: {gdal.GetLastErrorMsg()})")
            raise RuntimeError(f"无法打开输入文件: {self.input_path}")

        self.geotransform = self.src_ds.GetGeoTransform()
        self.projection = self.src_ds.GetProjection()  # 可能为空字符串
        self.x_size = self.src_ds.RasterXSize
        self.y_size = self.src_ds.RasterYSize
        self.band_count = self.src_ds.RasterCount
        if self.band_count == 0:
            self.logger.error(f"输入文件 {self.input_path} 没有波段。")
            self.src_ds = None  # Close dataset
            raise ValueError(f"输入文件 {self.input_path} 没有波段。")
        self.src_data_type = self.src_ds.GetRasterBand(1).DataType

        self.x_res = abs(self.geotransform[1]) if self.geotransform else 0
        self.y_res = abs(self.geotransform[5]) if self.geotransform else 0

        self.logger.info(f"输入文件: {self.input_path}")
        self.logger.info(
            f"尺寸: {self.x_size}x{self.y_size}, 波段数: {self.band_count}, 数据类型: {gdal.GetDataTypeName(self.src_data_type)}")
        if self.geotransform:
            self.logger.info(f"原始分辨率: {self.x_res}x{self.y_res}")
        if self.projection:
            self.logger.info(f"原始投影 (WKT): {self.projection[:100]}...")  # 只打印部分WKT
        else:
            self.logger.warning("输入文件没有投影信息。")

        self._determine_output_precision(precision)
        self.logger.info(
            f"使用精度模式: {precision} -> {'单精度 Float32/CFloat32 (如果适用)' if self.use_single_precision else '保持源精度或双精度 Float64/CFloat64'}")
        self.logger.info(f"目标输出 GDAL 数据类型: {gdal.GetDataTypeName(self.output_gdal_data_type)}")

    def _determine_output_precision(self, precision_mode):
        self.use_single_precision = False

        if precision_mode == 'single':
            self.use_single_precision = True
        elif precision_mode == 'double':
            self.use_single_precision = False
        elif precision_mode == 'auto':
            # 自动模式下，如果源是高精度浮点，默认输出高精度浮点
            # 如果源是单精度浮点，默认输出单精度浮点
            # 如果源是整型，保持整型
            self.use_single_precision = (
                        self.src_data_type == gdal.GDT_Float32 or self.src_data_type == gdal.GDT_CFloat32)
        else:
            raise ValueError("precision 参数必须是 'auto', 'single', 或 'double'")

        # 根据 self.use_single_precision 和源数据类型决定输出数据类型
        if self.use_single_precision:  # 倾向于单精度
            if self.src_data_type == gdal.GDT_Float64:
                self.output_gdal_data_type = gdal.GDT_Float32
            elif self.src_data_type == gdal.GDT_CFloat64:
                self.output_gdal_data_type = gdal.GDT_CFloat32
            # 如果已经是单精度或更低，或者不是浮点型，则保持原样
            else:
                self.output_gdal_data_type = self.src_data_type
        else:  # 倾向于双精度或保持原样
            if self.src_data_type == gdal.GDT_Float32 and precision_mode == 'double':
                self.output_gdal_data_type = gdal.GDT_Float64  # 强制升级
            elif self.src_data_type == gdal.GDT_CFloat32 and precision_mode == 'double':
                self.output_gdal_data_type = gdal.GDT_CFloat64  # 强制升级
            else:
                self.output_gdal_data_type = self.src_data_type

    def transform(self, target_crs, resample_method='bilinear',
                  creation_options=None,  # 允许用户覆盖默认创建选项
                  target_x_res=None, target_y_res=None,  # 允许指定目标分辨率
                  transformation_params=None, progress_callback=None,
                  input_path=None, output_path=None, precision='auto',
                  force_single_precision=None, force_double_precision=None,
                  seven_params=None, **kwargs):
        """执行单文件坐标转换
        
        参数:
            target_crs (str/int): 目标坐标系
                支持格式:
                - EPSG代码(如4326)
                - EPSG字符串(如"EPSG:4326")
                - WKT字符串
                - PROJ字符串
                示例: "EPSG:3857" (Web墨卡托投影)
                
            resample_method (str): 重采样方法，默认"bilinear"
                可选值: 
                - "nearest": 最近邻(适用于分类数据)
                - "bilinear": 双线性(适用于连续数据)
                - "cubic": 三次卷积(高质量重采样)
                - "average": 平均值(适用于降采样)
                
            creation_options (list): 自定义创建选项，默认None
                示例: ["COMPRESS=LZW", "TILED=YES"]
                
            target_x_res (float): 目标X分辨率，默认None(自动计算)
            target_y_res (float): 目标Y分辨率，默认None(自动计算)
            
            transformation_params (list): 转换参数，默认None
                用于高级坐标转换，如七参数转换
                
            progress_callback (callable): 进度回调函数，默认None
                接收两个参数: (complete, message)
                
            input_path (str): 输入文件路径，默认None(使用初始化时设置的值)
            output_path (str): 输出文件路径，默认None(使用初始化时设置的值)
            
            precision (str): 精度控制，默认"auto"
                可选值:
                - "auto": 自动选择(默认)
                - "single": 强制单精度(Float32)
                - "double": 强制双精度(Float64)
                
            force_single_precision (bool): 强制单精度，默认None
            force_double_precision (bool): 强制双精度，默认None
            seven_params (dict): 七参数转换参数，默认None
                格式:
                {
                    "dx": x平移量(m),
                    "dy": y平移量(m),
                    "dz": z平移量(m),
                    "rx": x旋转量(秒),
                    "ry": y旋转量(秒),
                    "rz": z旋转量(秒),
                    "scale": 比例因子(ppm)
                }
            **kwargs: 其他GDAL Warp选项
            
        返回:
            bool: 转换是否成功
            
        异常:
            ValueError: 参数无效时抛出
            RuntimeError: GDAL操作失败时抛出
            FileNotFoundError: 输入文件不存在时抛出
            
        示例:
            >>> transformer = CompatibleGeoTiffTransformer()
            >>> success = transformer.transform(
            ...     target_crs="EPSG:4326",
            ...     input_path="input.tif",
            ...     output_path="output.tif",
            ...     resample_method="bilinear"
            ... )
            
        注意事项:
        1. 如果同时指定precision和force_*_precision，后者优先级更高
        2. 七参数转换需要提供完整的转换参数
        3. 大文件处理时建议设置progress_callback监控进度
        """
        
        # 确保每次转换前状态重置
        if hasattr(self, 'src_ds') and self.src_ds:
            self.src_ds = None

        # 处理输入/输出路径
        self.input_path = input_path if input_path is not None else self.input_path
        self.output_path = output_path if output_path is not None else self.output_path
        
        if not self.input_path or not self.output_path:
            raise ValueError("必须提供input_path和output_path参数(在初始化或transform方法中)")

        # 初始化GDAL数据集（如果尚未初始化或路径已更改）
        if not hasattr(self, 'src_ds') or self.src_ds is None:
            if not os.path.isfile(self.input_path):
                self.logger.error(f"输入文件不存在: {self.input_path}")
                raise FileNotFoundError(f"输入文件不存在: {self.input_path}")

            os.makedirs(os.path.dirname(self.output_path), exist_ok=True)
            self.src_ds = gdal.Open(self.input_path, gdal.GA_ReadOnly)
            if not self.src_ds:
                raise RuntimeError(f"无法打开输入文件: {self.input_path} (GDAL error: {gdal.GetLastErrorMsg()})")

            # 初始化元数据
            self.geotransform = self.src_ds.GetGeoTransform()
            self.projection = self.src_ds.GetProjection()
            self.x_size = self.src_ds.RasterXSize
            self.y_size = self.src_ds.RasterYSize
            self.band_count = self.src_ds.RasterCount
            self.src_data_type = self.src_ds.GetRasterBand(1).DataType
            self.x_res = abs(self.geotransform[1]) if self.geotransform else 0
            self.y_res = abs(self.geotransform[5]) if self.geotransform else 0

            self.logger.info(f"输入文件: {self.input_path}")
            self.logger.info(f"尺寸: {self.x_size}x{self.y_size}, 波段数: {self.band_count}")
            if self.projection:
                self.logger.info(f"原始投影: {self.projection[:100]}...")
            
            # 初始化精度相关属性
            self.use_single_precision = False
            self.output_gdal_data_type = self.src_data_type
            
            # 初始化内存限制（默认4GB）
            if not hasattr(self, 'max_memory_bytes'):
                self.max_memory_bytes = 4096 * 1024 * 1024  # 默认4GB
            
        # 处理精度参数
        if force_single_precision:
            precision = 'single'
        elif force_double_precision:
            precision = 'double'
            
        if precision != 'auto':
            self._determine_output_precision(precision)
            
        # 处理7参数转换
        if seven_params:
            transformation_params = seven_params

        self.logger.info(f"开始转换到目标CRS: {target_crs} 使用重采样: {resample_method}")

        dst_wkt = self._validate_crs(target_crs)

        src_srs = osr.SpatialReference()
        src_wkt = None
        if self.projection:
            src_srs.ImportFromWkt(self.projection)
            if src_srs.Validate() == 0:  # OGRERR_NONE
                src_wkt = src_srs.ExportToWkt()
            else:
                self.logger.warning(f"源文件的投影WKT无效: {self.projection}")

        if not src_wkt:
            self.logger.warning("源文件没有有效投影信息或投影信息为空。gdal.Warp 将尝试猜测，"
                                "但这可能导致不准确或失败。建议为源数据提供正确的投影。")
            # 对于某些格式，如原始二进制，可能真的没有投影。
            # gdal.Warp 如果 srcSRS=None，则假定像素/行坐标。

        # GDAL Warp 选项
        warp_options_dict = {
            'format': 'GTiff',
            'srcSRS': src_wkt,  # 如果 src_wkt is None, gdal.Warp 会处理
            'dstSRS': dst_wkt,
            'resampleAlg': self._get_resample_algorithm(resample_method),
            'outputType': self.output_gdal_data_type,
            'warpMemoryLimit': self.max_memory_bytes,  # **使用字节单位**
            'multithread': True,
            # 'dstalpha': True, # 如果需要输出alpha波段
        }

        # 默认创建选项
        default_creation_options = [
            "BIGTIFF=YES",  # 允许大于4GB的TIFF
            "TILED=YES",  # 使用分块TIF，提高I/O效率
            "BLOCKXSIZE=512",  # 根据数据特性调整
            "BLOCKYSIZE=512",
            "COMPRESS=LZW",  # LZW压缩在速度和压缩比之间有较好平衡
            # "COMPRESS=DEFLATE", "PREDICTOR=2" # for floating point or 3 for integer (better compression, slower)
            # "COMPRESS=ZSTD", "ZSTD_LEVEL=9" # (if GDAL built with ZSTD, good compression)
            # "PHOTOMETRIC=MINISBLACK" # for single band. For multiband, it's often MULTIBAND.
            # Let GDAL decide unless specific need.
        ]
        # 如果是RGB三波段Byte类型，可以考虑 PHOTOMETRIC=RGB
        if self.output_gdal_data_type == gdal.GDT_Byte and self.band_count == 3:
            # 检查是否已存在PHOTOMETRIC，避免重复
            if not any("PHOTOMETRIC" in opt for opt in (creation_options or [])):
                default_creation_options.append("PHOTOMETRIC=RGB")

        warp_options_dict[
            'creationOptions'] = creation_options if creation_options is not None else default_creation_options

        # 处理目标分辨率
        if target_x_res is not None:
            warp_options_dict['xRes'] = target_x_res
        if target_y_res is not None:
            warp_options_dict['yRes'] = target_y_res
        # 注意: 同时指定 target_x_res/target_y_res 和 width/height 通常是冲突的。
        # gdal.Warp 会根据目标CRS、范围和分辨率（或目标像素数）计算输出栅格的尺寸。

        # 处理 NoData
        # 注意：每个波段可能具有不同的NoData值。这里简化为使用第一个波段的。
        # 对于更复杂的情况，可能需要为每个波段设置 srcNodata。
        # 处理 NoData，支持多波段
        src_nodata_values = []
        for band_idx in range(1, self.band_count + 1):
            src_band = self.src_ds.GetRasterBand(band_idx)
            nodata_val = src_band.GetNoDataValue()
            if nodata_val is not None:
                src_nodata_values.append(nodata_val)
            else:
                src_nodata_values.append(None)  # 如果某个波段没有NoData值，则用None表示

        if src_nodata_values and any(val is not None for val in src_nodata_values):
            self.logger.info(f"源文件NoData值 (按波段): {src_nodata_values}")
            # gdal.Warp 的 srcNodata 可以是一个值（对所有波段）或一个列表（每个波段一个值）
            # 如果列表中有None，gdal.Warp会忽略该波段的NoData设置
            warp_options_dict['srcNodata'] = src_nodata_values

            # 处理 dstNodata，目标NoData值通常与源一致，但需要考虑数据类型转换
            dst_nodata_values = []
            is_float_type = gdal.GetDataTypeName(self.output_gdal_data_type).lower().startswith('float')
            is_complex_type = gdal.GetDataTypeIsComplex(self.output_gdal_data_type)

            for nodata_val in src_nodata_values:
                if nodata_val is None:
                    dst_nodata_values.append(None)
                else:
                    if is_float_type:
                        dst_nodata_values.append(float(nodata_val))
                    elif is_complex_type:
                        self.logger.warning("dstNodata对于复数类型可能不适用或行为未定义。")
                        dst_nodata_values.append(nodata_val)  # 保持原值，依赖gdal.Warp处理
                    else:  # 整型
                        try:
                            dst_nodata_values.append(int(nodata_val))  # 尝试转换为整型
                        except (ValueError, OverflowError):
                            self.logger.warning(
                                f"无法将NoData值 {nodata_val} 转换为目标类型 {gdal.GetDataTypeName(self.output_gdal_data_type)}，保持原值。")
                            dst_nodata_values.append(nodata_val)
            warp_options_dict['dstNodata'] = dst_nodata_values

        if transformation_params:
            warp_options_dict['transformerOptions'] = transformation_params

        # 设置回调
        current_callback = None
        if progress_callback:
            current_callback = progress_callback
        elif progress_callback is None:  # None 表示用户希望有默认回调
            current_callback = gdal.TermProgress_nocb
        # 如果 progress_callback is False, 则表示不要任何回调

        if current_callback:
            warp_options_dict['callback'] = current_callback
            # warp_options_dict['callback_data'] = pass_your_data_here # If callback needs it

        self.logger.info(f"gdal.Warp options: {warp_options_dict}")

        dst_ds = None  # 初始化
        try:
            # gdal.Warp 可以直接操作数据集对象或文件路径
            # 使用 self.src_ds (打开的数据集) 作为源
            # 使用 self.output_path (字符串) 作为目标，gdal.Warp会创建并关闭它
            dst_ds = gdal.Warp(self.output_path, self.src_ds, **warp_options_dict)

            if not dst_ds:
                # 某些情况下，gdal.Warp 在失败时可能不抛出异常但返回 None
                error_msg = gdal.GetLastErrorMsg()
                self.logger.error(f"gdal.Warp 失败，无法创建输出文件: {self.output_path}. GDAL Error: {error_msg}")
                raise RuntimeError(f"gdal.Warp 失败. GDAL Error: {error_msg}")

            # gdal.Warp 在这种用法下会返回一个已关闭的数据集对象 (实际上是None或指向已关闭资源的指针)
            # 或者，如果指定了 dstDSName=None 而提供了 dstDS=dataset，它会操作该数据集。
            # 在我们的情况 (dstDSName=filepath, srcDSOrSrcDSName=dataset), 它创建并关闭文件。
            # 所以 dst_ds 变量在这里可能不是一个可用的打开的数据集。
            # 清理返回的对象，避免混淆
            dst_ds = None

            self.logger.info("转换完成，输出文件已保存到: {}".format(self.output_path))

            # 可选：验证输出文件是否可打开 (简单的健全性检查)
            # test_ds = gdal.Open(self.output_path, gdal.GA_ReadOnly)
            # if not test_ds:
            #     self.logger.warning(f"输出文件 {self.output_path} 创建后无法重新打开进行验证。")
            # else:
            #     self.logger.info(f"输出文件 {self.output_path} 验证成功 (可打开)。")
            #     test_ds = None # 关闭

        except RuntimeError as e:
            self.logger.error(f"gdal.Warp 执行错误: {e}")
            # 尝试清理可能创建的不完整输出文件
            if os.path.exists(self.output_path):
                try:
                    # 使用GDAL驱动删除可能更安全，但os.remove通常也行
                    driver = gdal.GetDriverByName("GTiff")
                    if driver:
                        # driver.Delete(self.output_path) # GDAL Delete仅适用于某些驱动
                        pass  # 通常不需要，os.remove即可
                    os.remove(self.output_path)
                    self.logger.info(f"已删除失败的输出文件: {self.output_path}")
                except Exception as del_e:
                    self.logger.error(f"清理失败的输出文件 {self.output_path} 时出错: {del_e}")
            return False  # 表示转换失败
        finally:
            if dst_ds is not None:  # 以防万一 gdal.Warp 返回了需要关闭的对象
                dst_ds = None
        return True

    def _validate_crs(self, crs_input):
        srs = osr.SpatialReference()
        try:
            if isinstance(crs_input, int):
                if srs.ImportFromEPSG(crs_input) != 0:  # 0 on success
                    raise ValueError(f"无效的EPSG代码: {crs_input}")
            elif isinstance(crs_input, str):
                if crs_input.upper().startswith('EPSG:'):
                    epsg_code = int(crs_input.split(':')[1])
                    if srs.ImportFromEPSG(epsg_code) != 0:
                        raise ValueError(f"无效的EPSG代码: {crs_input}")
                else:  # WKT, Proj String, etc.
                    if srs.SetFromUserInput(crs_input) != 0:
                        # SetFromUserInput可以接受很多格式，如果失败，错误信息可能在gdal.GetLastErrorMsg()
                        raise ValueError(
                            f"无法从用户输入解析坐标系: '{crs_input}'. GDAL Error: {gdal.GetLastErrorMsg()}")
            else:
                raise TypeError("坐标系输入必须是 EPSG代码 (int), 'EPSG:XXXX' 字符串, WKT字符串, 或 Proj字符串.")

            if srs.Validate() != 0:  # 0 for OGRERR_NONE (valid)
                self.logger.warning(f"提供的坐标系 '{crs_input}' 未通过验证. OSR Validate Msg: {srs.Validate()}")
                # 即使未通过Validate，某些情况下仍可使用，但最好是有效的

            wkt = srs.ExportToWkt()
            if not wkt:  # 再次检查 WKT 是否成功导出
                raise ValueError(f"无法将坐标系 '{crs_input}' 转换为WKT格式.")
            return wkt
        except Exception as e:  # 捕获所有可能的错误并重新抛出为 ValueError
            self.logger.error(f"坐标系验证/转换时出错: {crs_input}, Error: {e}")
            raise ValueError(f"无法解析或验证坐标系定义 '{crs_input}': {e}")

    def _get_resample_algorithm(self, method_str):
        # gdal.Warp 的 resampleAlg 参数可以直接接受字符串名称，也接受 gdal.GRA_xxxx 枚举
        # 为保持一致性和明确性，仍可做检查
        # 注意：GDAL 3.1+ `resampleAlg`可以直接是字符串名
        method_str_lower = method_str.lower()
        # 这些是 gdal.WarpOptions 和命令行工具支持的常见字符串
        valid_methods_str = [
            'near', 'nearest', 'bilinear', 'cubic', 'cubicspline', 'lanczos',
            'average', 'mode', 'max', 'min', 'med', 'q1', 'q3',
            'sum'  # GDAL 3.3+
        ]
        if method_str_lower == 'near': method_str_lower = 'nearest'  # 'near'是'nearest'的别名

        if method_str_lower in valid_methods_str:
            return method_str_lower
        else:
            # 尝试匹配GDAL枚举（尽管字符串通常更好用）
            gdal_alg_map = {
                'nearest': gdal.GRA_NearestNeighbour,
                'bilinear': gdal.GRA_Bilinear,
                'cubic': gdal.GRA_Cubic,
                'cubicspline': gdal.GRA_CubicSpline,
                'lanczos': gdal.GRA_Lanczos,
                'average': gdal.GRA_Average,
                'mode': gdal.GRA_Mode,
                # 'max': gdal.GRA_Max, # 这些在warp中通常用字符串
                # 'min': gdal.GRA_Min,
                # 'med': gdal.GRA_Med,
                # 'q1': gdal.GRA_Q1,
                # 'q3': gdal.GRA_Q3
            }
            if method_str_lower in gdal_alg_map:
                self.logger.warning(f"重采样方法 '{method_str}' 正在使用其GDAL枚举值。推荐直接使用字符串名称。")
                return gdal_alg_map[method_str_lower]

            self.logger.warning(f"未知的重采样方法 '{method_str}', 将使用 'bilinear'. "
                                f"有效方法 (字符串): {', '.join(valid_methods_str)}")
            return 'bilinear'

    def close(self):
        if hasattr(self, 'src_ds') and self.src_ds:
            self.src_ds = None  # GDAL Python绑定中，将dataset设为None会触发关闭和资源释放
        self.logger.info("源数据集资源已释放 (如果已打开)")

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        self.close()

    def batch_transform(self, input_files, output_dir, target_crs, **kwargs):
        """批量转换多个TIFF文件坐标系
        
        参数:
            input_files (list): 输入文件路径列表
                示例: ["file1.tif", "file2.tif"]
                
            output_dir (str): 输出目录路径
                输出文件名将与输入文件名相同，添加"_reprojected"后缀
                
            target_crs (str): 目标坐标系
                支持格式:
                - EPSG代码(如4326)
                - EPSG字符串(如"EPSG:4326")
                - WKT字符串
                - PROJ字符串
                示例: "EPSG:3857" (Web墨卡托投影)
                
            **kwargs: 其他transform()方法的参数
                可选参数包括:
                - resample_method: 重采样方法，默认"bilinear"
                - creation_options: 自定义创建选项
                - target_x_res: 目标X分辨率
                - target_y_res: 目标Y分辨率
                - precision: 精度控制("auto"/"single"/"double")
                - seven_params: 七参数转换参数
                - progress_callback: 进度回调函数
                
        返回:
            list: 转换结果列表
                每个元素为元组: (input_path, output_path, success)
                - input_path: 输入文件路径
                - output_path: 输出文件路径
                - success: 转换是否成功(True/False)
                
        异常:
            ValueError: 参数无效时抛出
            RuntimeError: GDAL操作失败时抛出
            FileNotFoundError: 输入文件不存在时抛出
            NotADirectoryError: 输出目录无效时抛出
            
        示例:
            >>> transformer = CompatibleGeoTiffTransformer()
            >>> results = transformer.batch_transform(
            ...     input_files=["file1.tif", "file2.tif"],
            ...     output_dir="output",
            ...     target_crs="EPSG:4326",
            ...     resample_method="bilinear"
            ... )
            
        注意事项:
        1. 输出目录必须已存在
        2. 批量处理时建议设置progress_callback监控进度
        3. 七参数转换将应用于所有输入文件
        4. 内存使用受max_memory_mb参数限制
        5. 输出文件名格式为: {原文件名}_reprojected.tif
        """
        if not input_files:
            self.logger.warning("No input files provided for batch transform")
            return []
            
        results = []
        os.makedirs(output_dir, exist_ok=True)
        
        for idx, input_file in enumerate(input_files):
            output_file = os.path.join(
                output_dir,
                f"{os.path.splitext(os.path.basename(input_file))[0]}_reprojected.tif"
            )
            try:
                # 完全重置状态
                self.close()
                self.input_path = input_file
                self.output_path = output_file
                self.src_ds = None
                
                # 添加进度信息
                self.logger.info(f"Processing file {idx+1}/{len(input_files)}: {input_file}")
                
                success = self.transform(
                    target_crs=target_crs,
                    input_path=input_file,
                    output_path=output_file,
                    **kwargs
                )
                results.append((input_file, output_file, success))
                if success:
                    self.logger.info(f"Successfully transformed to {output_file}")
            except Exception as e:
                self.logger.error(f"Failed to transform {input_file}: {str(e)}", exc_info=True)
                results.append((input_file, None, False))
                
        return results


# --- 简单的 GDAL 进度回调函数示例 (与之前类似，略作调整) ---
def gdal_progress_callback_logger(complete, message, cb_data_dict_or_logger):
    """
    GDAL 进度回调函数，使用传入的logger。
    :param cb_data_dict_or_logger: 可以是一个logger实例，或者一个包含'logger'键的字典。
    """
    logger_instance = None
    if isinstance(cb_data_dict_or_logger, logging.Logger):
        logger_instance = cb_data_dict_or_logger
    elif isinstance(cb_data_dict_or_logger, dict) and 'logger' in cb_data_dict_or_logger:
        logger_instance = cb_data_dict_or_logger['logger']
    else:
        logger_instance = logging.getLogger('gdal_progress')  # Fallback

    percent = complete * 100

    # 避免过于频繁的日志输出
    # 使用 last_reported_percent 作为函数属性来跨调用保持状态
    if not hasattr(gdal_progress_callback_logger, "last_reported_int_percent"):
        gdal_progress_callback_logger.last_reported_int_percent = -1

    current_int_percent = int(percent)
    # 每5%或消息非空时报告一次
    if current_int_percent > gdal_progress_callback_logger.last_reported_int_percent:
        if (
                current_int_percent % 5 == 0 and current_int_percent != gdal_progress_callback_logger.last_reported_int_percent) or \
                (message and len(message.strip()) > 0):  # 如果有具体消息也打印
            logger_instance.info(f"GDAL Warp 进度: {current_int_percent:.0f}% {message if message else ''}")
            gdal_progress_callback_logger.last_reported_int_percent = current_int_percent
            if complete >= 1.0:  # 重置以便下次调用
                gdal_progress_callback_logger.last_reported_int_percent = -1

    return 1  # 返回1表示继续，0表示取消


def main():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    main_logger = logging.getLogger("GeoTiffTransformDemo")

    # --- 示例输入输出路径 ---
    base_path = 'G:\\testcode\\ceshitiff\\'  # **请修改为你的实际路径**
    input_tif = os.path.join(base_path, 'ffff.tif')  # **确保此文件存在且是有效的GeoTIFF**

    if not os.path.exists(input_tif):
        main_logger.error(f"测试输入文件 {input_tif} 不存在! 请创建或修改路径。")
        main_logger.info("你可以使用QGIS等软件创建一个测试用的GeoTIFF文件，例如：")
        main_logger.info("1. 创建一个 Float32 或 Float64 数据类型的单波段栅格。")
        main_logger.info("2. 给它赋予一个已知的坐标系，如 EPSG:4326 (WGS84) 或一个UTM带。")
        return

    output_auto_wgs84_tif = os.path.join(base_path, 'ffff_output_auto_wgs84.tif')
    output_single_utm_tif = os.path.join(base_path, 'ffff_output_single_utm.tif')

    # --- 说明关于照片查看器的问题 ---
    main_logger.info("-" * 50)
    main_logger.info("关于 '照片查看器显示已损坏' 的说明:")
    main_logger.info(
        "标准照片查看器 (如 Windows 照片查看器) 通常不支持浮点型 (Float32/64) GeoTIFF 文件或具有复杂地理空间元数据的TIFF。")
    main_logger.info("它们期望的是8位整型 (Byte) 的RGB图像或灰度图。")
    main_logger.info("QGIS等GIS软件可以正常打开这些文件，因为它们是为处理地理空间数据设计的。")
    main_logger.info(
        "如果需要照片查看器兼容性，通常需要额外步骤将数据转换为8位并进行拉伸/缩放 (例如使用 gdal_translate -ot Byte -scale)。")
    main_logger.info("本工具专注于地理空间数据的精确转换。")
    main_logger.info("-" * 50)

    # 示例1: 自动精度检测, 目标 WGS84 (EPSG:4326)
    try:
        main_logger.info("\n==== 示例1: 自动精度, 目标 WGS84 (EPSG:4326) ====")
        with CompatibleGeoTiffTransformer(
                input_path=input_tif,
                output_path=output_auto_wgs84_tif,
                max_memory_mb=2048,  # 例如2GB
                precision='auto',
                logger=main_logger.getChild("Transformer.AutoWGS84")  # 使用子logger
        ) as transformer:
            success = transformer.transform(
                target_crs='EPSG:4326',  # WGS84
                resample_method='nearest',  # 最近邻，速度快
                # progress_callback=gdal.TermProgress_nocb # 标准终端进度条
                progress_callback=lambda c, m, cbd: gdal_progress_callback_logger(c, m, main_logger)  # 自定义回调
            )
            if success:
                main_logger.info(f"示例1转换成功: {output_auto_wgs84_tif}")
                # 验证
                ds_test = gdal.Open(output_auto_wgs84_tif)
                if ds_test:
                    srs_out = osr.SpatialReference(wkt=ds_test.GetProjectionRef())
                    main_logger.info(
                        f"示例1输出投影: EPSG:{srs_out.GetAttrValue('AUTHORITY', 1)} ({srs_out.GetName()})")
                    main_logger.info(f"示例1输出数据类型: {gdal.GetDataTypeName(ds_test.GetRasterBand(1).DataType)}")
                    ds_test = None
            else:
                main_logger.error(f"示例1转换失败。")


    except Exception as e:
        main_logger.error(f"示例1执行过程中发生错误: {e}", exc_info=True)

    # 示例2: 强制单精度, 目标 UTM Zone 50N (EPSG:32650)
    try:
        main_logger.info("\n==== 示例2: 强制单精度, 目标 UTM Zone 50N (EPSG:32650) ====")
        # 确保输入文件在示例1后没有被修改或transformer对象状态影响
        with CompatibleGeoTiffTransformer(
                input_path=input_tif,
                output_path=output_single_utm_tif,
                max_memory_mb=1024,  # 1GB
                precision='single',  # 强制输出 Float32 (如果源是 Float64)
                logger=main_logger.getChild("Transformer.SingleUTM")
        ) as transformer:
            success = transformer.transform(
                target_crs='EPSG:32650',  # UTM Zone 50N
                resample_method='bilinear',  # 双线性插值
                progress_callback=gdal.TermProgress_nocb  # 使用GDAL默认的简单进度条
            )


            # 也支持根据7参数进行坐标系转换
            """
            # 设置七参数转换 (北京54坐标系)
            transformation_params = [
                "COORD_OP=+proj=helmert",
                "+dx=-128.5",  # X轴平移
                "+dy=54.8",  # Y轴平移
                "+dz=156.4",  # Z轴平移
            ]
            # 执行坐标系转换
            success = transformer.transform(
                target_crs=4214,  # 北京54地理坐标系
                transformation_params=transformation_params,
                resample_method='bilinear',
                progress_callback=gdal.TermProgress_nocb  # 使用GDAL默认的简单进度条
            )
            """

            if success:
                main_logger.info(f"示例2转换成功: {output_single_utm_tif}")
                ds_test = gdal.Open(output_single_utm_tif)
                if ds_test:
                    srs_out = osr.SpatialReference(wkt=ds_test.GetProjectionRef())
                    main_logger.info(
                        f"示例2输出投影: EPSG:{srs_out.GetAttrValue('AUTHORITY', 1)} ({srs_out.GetName()})")
                    main_logger.info(f"示例2输出数据类型: {gdal.GetDataTypeName(ds_test.GetRasterBand(1).DataType)}")
                    ds_test = None
            else:
                main_logger.error(f"示例2转换失败。")

    except Exception as e:
        main_logger.error(f"示例2执行过程中发生错误: {e}", exc_info=True)


if __name__ == "__main__":
    main()

