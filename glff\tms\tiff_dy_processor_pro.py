# views.py
import os
import json
import uuid
import tempfile
import logging
import threading
import time
from collections import defaultdict
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

from PIL import Image
from osgeo import gdal, osr
import numpy as np
from django.http import JsonResponse
from django.views import View

from glff.dao.geo_api.geo_api import GeoApi

# 启用 GDAL 异常模式
gdal.UseExceptions()

# 配置日志：Django 会捕获日志，根据项目配置决定输出
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


def configure_gdal(cache_max=None, num_threads=None):
    """
    配置 GDAL 全局参数：
    - cache_max: 内存缓存大小（字节），例如 256*1024*1024
    - num_threads: GDAL 内部线程数，如 'ALL_CPUS' 或整数
    """
    if cache_max is not None:
        try:
            gdal.SetCacheMax(cache_max)
            logger.info(f"GDAL cache max 设置为 {cache_max} bytes")
        except Exception as e:
            logger.warning(f"设置 GDAL cache max 失败: {e}")
    if num_threads is not None:
        try:
            os.environ['GDAL_NUM_THREADS'] = str(num_threads)
            logger.info(f"GDAL_NUM_THREADS 设置为 {num_threads}")
        except Exception as e:
            logger.warning(f"设置 GDAL_NUM_THREADS 失败: {e}")


def ensure_overviews(src_path, overview_levels):
    """
    检查并构建源 TIFF overviews（若用户允许）。
    overview_levels: list of int, e.g. [2,4,8,16]
    注意：此操作会修改源 TIFF 或生成 .ovr，需确保用户可接受。
    """
    try:
        ds = gdal.Open(src_path, gdal.GA_Update)
    except Exception as e:
        logger.warning(f"无法以写模式打开源 TIFF 构建 overviews: {e}")
        return
    if ds is None:
        logger.warning("无法打开源 TIFF 构建 overviews")
        return
    try:
        band = ds.GetRasterBand(1)
        count = band.GetOverviewCount()
        if count > 0:
            logger.info(f"已有 {count} 个 overview，跳过构建")
        else:
            logger.info(f"开始构建 overviews: levels={overview_levels}")
            ds.BuildOverviews('NEAREST', overview_levels)
            logger.info("overviews 构建完成")
    except Exception as e:
        logger.warning(f"构建 overviews 失败: {e}")
    finally:
        ds = None


class TiffDyProcessor:
    """
    处理 TIFF 文件动态切片生成的类，供 Django View 调用。
    优化要点：
    - GDAL 全局缓存与线程数配置
    - 可选构建/使用 overviews 提速低 zoom
    - VRT 虚拟重投影（gdal.Warp format='VRT'），避免整图写磁盘
    - 跨经度 wrap 处理（仅地理 CRS）
    - 线程本地缓存 VRT Dataset，减少重复打开
    - 并发任务分块调度，记录性能统计与 Summary
    - 重试机制、异常捕获、日志替代 print
    - 保持接口 process_tiles() 不变，Django View 调用方式兼容
    """
    def __init__(self, epsg_code, tiff_path, min_level, max_level, file_id, file_name):
        # 日志
        self.logger = logger

        # 参数验证
        try:
            self.epsg_code = int(epsg_code)
        except Exception:
            raise ValueError(f"无效的 EPSG 代码: {epsg_code}")
        self.tiff_path = tiff_path
        try:
            self.min_level = int(min_level)
            self.max_level = int(max_level)
        except Exception:
            raise ValueError("min_level 和 max_level 必须为整数")
        if self.min_level < 0 or self.max_level < self.min_level:
            raise ValueError("级别范围错误：min_level 应 >=0，且 max_level >= min_level")

        self.file_id = file_id
        self.file_name = file_name

        # 常量
        self.tile_size = 256

        # 可配置属性，用户可在实例化后修改或通过 View 参数传入
        self.num_workers = 8                 # 并发线程数
        self.use_overviews = True            # 是否使用/构建 overviews
        self.overview_levels = [2, 4, 8, 16] # 构建 overviews 的采样因子
        self.build_overviews_auto = False    # 是否自动构建 overviews（会修改源 TIFF）
        self.gdal_cache_max = 256 * 1024 * 1024  # GDAL 缓存大小，字节
        self.gdal_num_threads = 'ALL_CPUS'       # GDAL 内部线程数
        self.wrap_longitude = True           # 对地理 CRS 是否处理跨经度 wrap
        self.retry_count = 1                 # 单瓦片重试次数

        # VRT 管理和 metrics
        self._vrt_path = None
        self._thread_local = threading.local()
        self._metrics_lock = threading.Lock()
        # metrics: zoom -> dict {'total','success','fail','skip','time'}
        self._metrics = defaultdict(lambda: {'total':0, 'success':0, 'fail':0, 'skip':0, 'time':0.0})

        # 目标 SRS
        self.dst_srs = osr.SpatialReference()
        # ImportFromEPSG 可能返回 None 或 0，但若 EPSG 无效，后续 Warp 会失败
        self.dst_srs.ImportFromEPSG(self.epsg_code)

    def _validate_epsg(self):
        """
        验证 EPSG 是否有效，若无效抛异常。
        """
        tmp_srs = osr.SpatialReference()
        if tmp_srs.ImportFromEPSG(self.epsg_code) != 0:
            # 部分 GDAL 版本返回 None 或 0，无明确错误；进一步检查 WKT
            try:
                wkt = tmp_srs.ExportToWkt()
                if not wkt:
                    raise ValueError
            except Exception:
                raise ValueError(f"无效或不支持的 EPSG 代码: {self.epsg_code}")

    def process_tiles(self):
        """
        主流程，保持调用接口不变：
        - 检查 TIFF 存在与投影
        - GDAL 配置、overview 管理
        - 创建 VRT、获取边界
        - 计算 tile 任务并并发裁剪
        - Summary 日志、清理资源
        - 最后保存 Tile 服务信息到数据库 via GeoApi
        """
        start_all = time.time()

        # GDAL 全局配置
        configure_gdal(self.gdal_cache_max, self.gdal_num_threads)

        # 校验 EPSG
        try:
            self._validate_epsg()
        except Exception as e:
            raise RuntimeError(f"EPSG 校验失败: {e}")

        # 检查源 TIFF
        if not os.path.exists(self.tiff_path):
            raise FileNotFoundError(f"指定的 TIFF 文件不存在: {self.tiff_path}")
        try:
            src_ds = gdal.Open(self.tiff_path, gdal.GA_ReadOnly)
        except Exception as e:
            raise RuntimeError(f"无法打开源 TIFF: {e}")
        if src_ds is None:
            raise RuntimeError(f"无法打开源 TIFF: {self.tiff_path}")

        # 检查投影信息
        proj_wkt = src_ds.GetProjection()
        if not proj_wkt:
            src_ds = None
            raise RuntimeError("源 TIFF 无投影信息，无法生成瓦片")
        # GeoTransform 旋转检测
        try:
            gt = src_ds.GetGeoTransform()
            if abs(gt[2]) > 1e-6 or abs(gt[4]) > 1e-6:
                self.logger.warning(
                    "源 TIFF 存在旋转/仿射倾斜 (GeoTransform[2] or [4] 非0)，"
                    "裁剪范围可能不精确，建议使用无旋转的正射纠正影像以获得最佳效果。"
                )
        except Exception:
            pass

        # 概览管理
        if self.use_overviews and self.build_overviews_auto:
            ensure_overviews(self.tiff_path, self.overview_levels)

        # 创建或使用 VRT
        try:
            self._create_vrt(src_ds, proj_wkt)
        except Exception as e:
            src_ds = None
            raise RuntimeError(f"创建重投影 VRT 失败: {e}")
        # 关闭 src_ds
        src_ds = None

        # 打开 VRT Dataset 获取边界
        try:
            vrt_ds = gdal.Open(self._vrt_path, gdal.GA_ReadOnly)
        except Exception as e:
            self._cleanup_vrt()
            raise RuntimeError(f"无法打开 VRT 数据集: {e}")
        if vrt_ds is None:
            self._cleanup_vrt()
            raise RuntimeError("无法打开 VRT 数据集")
        xmin, xmax, ymin, ymax = self._get_dataset_bounds(vrt_ds)
        self.logger.info(f"VRT 重投影后边界 (EPSG {self.epsg_code}): xmin={xmin}, xmax={xmax}, ymin={ymin}, max_y={ymax}")
        vrt_ds = None

        # 计算并发任务
        total_tiles = 0
        futures = {}
        # ThreadPoolExecutor 中使用线程本地缓存 VRT Dataset
        with ThreadPoolExecutor(max_workers=self.num_workers) as executor:
            for zoom in range(self.min_level, self.max_level + 1):
                # 计算行列范围列表（考虑 wrap 经度）
                try:
                    ranges = self._compute_tile_ranges_for_zoom(xmin, xmax, ymin, ymax, zoom)
                except Exception as e:
                    self.logger.warning(f"计算级别 {zoom} 瓦片索引失败: {e}")
                    continue
                zoom_count = 0
                for (min_row, max_row, min_col, max_col) in ranges:
                    if min_row > max_row or min_col > max_col:
                        continue
                    zoom_count += (max_row - min_row + 1) * (max_col - min_col + 1)
                    # 按行优先顺序提交任务，利于缓存命中
                    for row in range(min_row, max_row + 1):
                        for col in range(min_col, max_col + 1):
                            fut = executor.submit(self._process_single_tile_with_retry, zoom, col, row)
                            futures[fut] = (zoom, col, row)
                if zoom_count > 0:
                    self.logger.info(f"Level {zoom}: 计划生成瓦片 {zoom_count} 个")
                total_tiles += zoom_count

            if total_tiles == 0:
                self._cleanup_vrt()
                self.logger.info("无瓦片需要生成，退出")
                # 也可以返回早期响应
                return

            self.logger.info(f"待生成瓦片总数: {total_tiles}")

            # 收集结果，metrics 已由内部记录
            for fut in as_completed(futures):
                zoom, col, row = futures[fut]
                try:
                    status = fut.result()
                    # status 用于日志或进一步处理
                    # 例如，如果大量失败，可记录或告警
                except Exception as e:
                    self._record_metric(zoom, 'fail', 0.0)
                    self.logger.error(f"瓦片 {zoom}/{col}/{row} 生成时抛异常: {e}")

        # Summary 日志
        self._report_metrics()
        end_all = time.time()
        self.logger.info(f"全部瓦片生成完成，总耗时: {end_all - start_all:.2f}s")

        # 保存切片信息到数据库（示例 URL 构造，需根据实际项目路由调整）
        try:
            # 构造示例 URL 模板，注意替换 epsgcode、{z}/{x}/{y} 位置
            # 这里假设 Django 路由对应 pattern: /glff/tif/tiles/dy-view/<file_id>/<epsg>/<z>/<x>/<y>/
            # 具体请根据项目实际路由调整
            api_id = uuid.uuid4().hex
            # 例如:
            apiUrlTemplate = f"/glff/tif/tiles/dy-view/{self.file_id}/{self.epsg_code}/{{z}}/{{x}}/{{y}}/"
            # 存储到 GeoApi，具体字段请根据项目 DAO 接口调整
            GeoApi.create_geo_api(
                id=api_id,
                url=apiUrlTemplate,
                api_type="TMS",
                file_id=self.file_id,
                remarks=self.file_name,
                params=json.dumps({
                    "apiName": self.file_name,
                    "epsg_code": str(self.epsg_code),
                    "z": "瓦片 z 级别",
                    "x": "瓦片 x 索引",
                    "y": "瓦片 y 索引",
                    "demo": apiUrlTemplate
                }, ensure_ascii=False)
            )
            self.logger.info("GeoApi 服务信息保存成功")
        except Exception as e:
            # 记录错误，不影响整体切片生成
            self.logger.error(f"保存服务信息错误: {e}")

        # 清理 VRT Dataset 和临时文件
        self._cleanup_vrt()

    def _create_vrt(self, src_ds, proj_wkt):
        """
        创建或使用 VRT：
        - 若源 CRS 与目标 EPSG 相同，直接使用源 TIFF
        - 否则使用 gdal.Warp(format='VRT') 生成虚拟重投影 VRT，并写入临时 .vrt 文件
        """
        need_vrt = True
        try:
            src_srs = osr.SpatialReference()
            src_srs.ImportFromWkt(proj_wkt)
            src_auth = src_srs.GetAttrValue('AUTHORITY', 1)
            if src_auth is not None and int(src_auth) == self.epsg_code:
                need_vrt = False
        except Exception:
            need_vrt = True

        if not need_vrt:
            self._vrt_path = self.tiff_path
            self.logger.info("源 TIFF CRS 与目标相同，直接使用源数据，不创建 VRT")
            return

        # 生成 VRT 重投影
        warp_opts = gdal.WarpOptions(
            format='VRT',
            dstSRS=self.dst_srs.ExportToWkt(),
            resampleAlg='bilinear'
            # 可添加 NUM_THREADS 等 GDAL Warp 参数
        )
        vrt_ds = gdal.Warp('', src_ds, options=warp_opts)
        if vrt_ds is None:
            raise RuntimeError("gdal.Warp 生成 VRT 失败，返回 None")
        # 写入临时 .vrt
        tmp_vrt = tempfile.NamedTemporaryFile(delete=False, suffix=".vrt")
        vrt_path = tmp_vrt.name
        tmp_vrt.close()
        vrt_driver = gdal.GetDriverByName('VRT')
        out_vrt_ds = vrt_driver.CreateCopy(vrt_path, vrt_ds)
        if out_vrt_ds is None:
            raise RuntimeError("写入 VRT 文件失败")
        vrt_ds = None
        out_vrt_ds = None
        self._vrt_path = vrt_path
        self.logger.info(f"生成临时重投影 VRT: {vrt_path}")

    def _get_dataset_bounds(self, ds):
        """
        获取 Dataset 边界 (xmin, xmax, ymin, ymax)：
        - 若无旋转 GeoTransform，直接计算
        - 否则用四角近似，并警告
        - 对 Web Mercator (EPSG:3857) 做合法范围裁剪
        """
        gt = ds.GetGeoTransform()
        xsize = ds.RasterXSize
        ysize = ds.RasterYSize
        if abs(gt[2]) < 1e-6 and abs(gt[4]) < 1e-6:
            xmin = gt[0]
            ymax = gt[3]
            xmax = gt[0] + gt[1] * xsize
            ymin = gt[3] + gt[5] * ysize
        else:
            xs = []
            ys = []
            corners = [(0, 0), (xsize, 0), (0, ysize), (xsize, ysize)]
            for i, j in corners:
                x = gt[0] + i * gt[1] + j * gt[2]
                y = gt[3] + i * gt[4] + j * gt[5]
                xs.append(x); ys.append(y)
            xmin = min(xs); xmax = max(xs)
            ymin = min(ys); ymax = max(ys)
            self.logger.warning("VRT Dataset 存在旋转 GeoTransform，仅使用四角近似边界")
        # Web Mercator 裁剪
        if self.epsg_code == 3857:
            maxm = 20037508.342789244
            xmin = max(xmin, -maxm); xmax = min(xmax, maxm)
            ymin = max(ymin, -maxm); ymax = min(ymax, maxm)
        return xmin, xmax, ymin, ymax

    def _compute_tile_ranges_for_zoom(self, xmin, xmax, ymin, ymax, zoom):
        """
        计算给定 zoom 级别下的瓦片行列范围列表，考虑 wrap_longitude:
        返回列表，每项为 (min_row, max_row, min_col, max_col)。
        """
        if self.wrap_longitude and self.epsg_code in (4326, 4490):
            segments = self._split_longitude_ranges(xmin, xmax)
        else:
            segments = [(xmin, xmax)]
        ranges = []
        for seg_min_x, seg_max_x in segments:
            min_row, max_row, min_col, max_col = self._compute_range_segment(
                seg_min_x, seg_max_x, ymin, ymax, zoom
            )
            if min_row <= max_row and min_col <= max_col:
                ranges.append((min_row, max_row, min_col, max_col))
        return ranges

    def _split_longitude_ranges(self, min_x, max_x):
        """
        针对地理 CRS 跨越 -180/180 情形，拆分为一或两个区间。
        返回 list of (seg_min_x, seg_max_x)。
        """
        # 全覆盖
        if max_x - min_x >= 360.0:
            return [(-180.0, 180.0)]
        segs = []
        if min_x < -180.0 or max_x > 180.0:
            # 跨界两段
            if min_x < -180.0:
                segs.append((min_x + 360.0, 180.0))
                segs.append((-180.0, max_x))
            elif max_x > 180.0:
                segs.append((min_x, 180.0))
                segs.append((-180.0, max_x - 360.0))
        else:
            segs.append((min_x, max_x))
        return segs

    def _compute_range_segment(self, min_x, max_x, min_y, max_y, zoom):
        """
        对单个经度段计算 tile 行列范围，无 wrap：
        返回 (min_row, max_row, min_col, max_col)，并截断到 [0,2^zoom-1]。
        """
        ts = self.tile_size
        if self.epsg_code == 3857:
            initial_resolution = 2 * 20037508.342789244 / ts
            res = initial_resolution / (2 ** zoom)
            min_col = int((min_x + 20037508.342789244) / (ts * res))
            max_col = int((max_x + 20037508.342789244) / (ts * res))
            min_row = int((20037508.342789244 - max_y) / (ts * res))
            max_row = int((20037508.342789244 - min_y) / (ts * res))
            max_index = (2 ** zoom) - 1
            min_col = max(min_col, 0); max_col = min(max_col, max_index)
            min_row = max(min_row, 0); max_row = min(max_row, max_index)
        elif self.epsg_code in (4326, 4490):
            initial_resolution = 360.0 / ts
            res = initial_resolution / (2 ** zoom)
            min_col = int((min_x + 180.0) / (ts * res))
            max_col = int((max_x + 180.0) / (ts * res))
            min_row = int((90.0 - max_y) / (ts * res))
            max_row = int((90.0 - min_y) / (ts * res))
            max_index = (2 ** zoom) - 1
            min_col = max(min_col, 0); max_col = min(max_col, max_index)
            min_row = max(min_row, 0); max_row = min(max_row, max_index)
        else:
            raise Exception(f"不支持的坐标系: {self.epsg_code}")
        return min_row, max_row, min_col, max_col

    def _process_single_tile_with_retry(self, zoom, tile_col, tile_row):
        """
        包装重试逻辑，调用 _process_single_tile，并记录 metrics。
        """
        attempt = 0
        while attempt <= self.retry_count:
            t0 = time.time()
            try:
                status = self._process_single_tile(zoom, tile_col, tile_row)
                elapsed = time.time() - t0
                self._record_metric(zoom, status, elapsed)
                return status
            except Exception as e:
                attempt += 1
                if attempt > self.retry_count:
                    elapsed = time.time() - t0
                    self._record_metric(zoom, 'fail', elapsed)
                    self.logger.error(f"瓦片 {zoom}/{tile_col}/{tile_row} 重试后失败: {e}")
                    return 'fail'
                else:
                    self.logger.warning(f"瓦片 {zoom}/{tile_col}/{tile_row} 异常，重试 {attempt}/{self.retry_count}: {e}")

    def _process_single_tile(self, zoom, tile_col, tile_row):
        """
        单瓦片生成逻辑。利用线程本地缓存打开 VRT Dataset。
        返回 'success'/'skip'/'fail'。
        """
        # 获取线程本地 VRT Dataset
        ds = getattr(self._thread_local, 'vrt_ds', None)
        if ds is None:
            try:
                ds = gdal.Open(self._vrt_path, gdal.GA_ReadOnly)
            except Exception as e:
                self.logger.warning(f"线程无法打开 VRT: {e}")
                return 'fail'
            if ds is None:
                self.logger.warning("线程无法打开 VRT Dataset，跳过瓦片")
                return 'fail'
            self._thread_local.vrt_ds = ds

        # 计算瓦片边界
        try:
            x_min, y_max, x_max, y_min = self._calculate_tile_bounds(zoom, tile_col, tile_row)
        except Exception as e:
            self.logger.warning(f"计算瓦片边界失败 {zoom}/{tile_col}/{tile_row}: {e}")
            return 'fail'

        # 构造输出路径：tile_cache/<epsg>/<zoom>/<col>/<row>.png
        base_dir = os.path.dirname(os.path.abspath(self.tiff_path))
        tile_cache_dir = os.path.join(base_dir, "tile_cache", str(self.epsg_code),
                                      str(zoom), str(tile_col))
        os.makedirs(tile_cache_dir, exist_ok=True)
        tile_cache_path = os.path.join(tile_cache_dir, f"{tile_row}.png")
        # 若已存在且大小>0，跳过
        if os.path.exists(tile_cache_path) and os.path.getsize(tile_cache_path) > 0:
            return 'skip'

        # 裁剪到内存
        try:
            tile_ds = gdal.Translate(
                '',
                ds,
                format='MEM',
                projWin=[x_min, y_max, x_max, y_min],
                width=self.tile_size,
                height=self.tile_size,
                outputType=ds.GetRasterBand(1).DataType
                # , bandList=[1,2,3]  # 如仅需 RGB，可启用
            )
        except Exception as e:
            self.logger.warning(f"Translate 裁剪失败 {zoom}/{tile_col}/{tile_row}: {e}")
            return 'fail'
        if tile_ds is None:
            return 'skip'

        # 转为 PIL Image 并透明化
        try:
            img = self._tile_ds_to_image(tile_ds)
        except Exception as e:
            self.logger.error(f"PIL 转换异常 {zoom}/{tile_col}/{tile_row}: {e}")
            tile_ds = None
            return 'fail'
        tile_ds = None
        if img is None:
            return 'skip'

        # 保存 PNG
        try:
            img.save(tile_cache_path, format='PNG')
            # self.logger.info(f"瓦片已保存: {tile_cache_path}")
            return 'success'
        except Exception as e:
            self.logger.error(f"保存瓦片失败 {zoom}/{tile_col}/{tile_row}: {e}")
            return 'fail'

    def _calculate_tile_bounds(self, zoom, tile_col, tile_row):
        """
        计算瓦片边界 (x_min, y_max, x_max, y_min)，与原逻辑保持一致。
        """
        ts = self.tile_size
        if self.epsg_code == 3857:
            initial_resolution = 2 * 20037508.342789244 / ts
            res = initial_resolution / (2 ** zoom)
            x_min = -20037508.342789244 + tile_col * ts * res
            y_max = 20037508.342789244 - tile_row * ts * res
            x_max = x_min + ts * res
            y_min = y_max - ts * res
        elif self.epsg_code in (4326, 4490):
            initial_resolution = 360.0 / ts
            res = initial_resolution / (2 ** zoom)
            x_min = -180.0 + tile_col * ts * res
            y_max = 90.0 - tile_row * ts * res
            x_max = x_min + ts * res
            y_min = y_max - ts * res
        else:
            raise Exception(f"不支持的坐标系: {self.epsg_code}")
        return x_min, y_max, x_max, y_min

    def _tile_ds_to_image(self, tile_ds):
        """
        将 MEM Dataset 转 PIL Image 并透明化 NoData 区域；若空瓦返回 None。
        """
        band_count = tile_ds.RasterCount
        if band_count < 1:
            return None
        arrays = []
        ndvs = []
        for i in range(band_count):
            band = tile_ds.GetRasterBand(i+1)
            try:
                arr = band.ReadAsArray()
            except Exception:
                return None
            arrays.append(arr)
            try:
                ndv = band.GetNoDataValue()
            except Exception:
                ndv = None
            ndvs.append(ndv)
        # 构建 RGBA
        if band_count == 1:
            gray = arrays[0]
            img = Image.fromarray(gray).convert("RGBA")
            data = np.array(img)
            ndv = ndvs[0]
            if ndv is not None:
                mask = (gray == ndv)
                data[mask] = (0, 0, 0, 0)
            if np.all(data[:, :, 3] == 0):
                return None
            return Image.fromarray(data)
        elif band_count == 3:
            r, g, b = arrays
            img = Image.merge('RGB', [Image.fromarray(ch) for ch in (r, g, b)]).convert("RGBA")
            data = np.array(img)
            if all(ndv is not None for ndv in ndvs[:3]):
                mask = (r == ndvs[0]) & (g == ndvs[1]) & (b == ndvs[2])
                data[mask] = (0, 0, 0, 0)
            if np.all(data[:, :, 3] == 0):
                return None
            return Image.fromarray(data)
        elif band_count == 4:
            r, g, b, a = arrays
            img = Image.merge('RGBA', [Image.fromarray(ch) for ch in (r, g, b, a)])
            if np.all(a == 0):
                return None
            return img
        else:
            return None

    def _record_metric(self, zoom, status, elapsed):
        """
        记录每瓦片执行状态和耗时，供 Summary 使用。
        """
        with self._metrics_lock:
            m = self._metrics[zoom]
            m['total'] += 1
            if status == 'success':
                m['success'] += 1
            elif status == 'fail':
                m['fail'] += 1
            elif status == 'skip':
                m['skip'] += 1
            m['time'] += elapsed

    def _report_metrics(self):
        """
        打印 Summary 日志，包括每级别总数、成功/失败/跳过数、平均耗时。
        """
        self.logger.info("瓦片生成 Summary:")
        for zoom in sorted(self._metrics.keys()):
            info = self._metrics[zoom]
            total = info['total']
            success = info['success']
            skip = info['skip']
            fail = info['fail']
            total_time = info['time']
            avg = total_time / total if total > 0 else 0.0
            self.logger.info(f" Level {zoom}: total={total}, success={success}, skip={skip}, fail={fail}, avgTime={avg:.2f}s")

    def _cleanup_vrt(self):
        """
        清理临时 VRT 文件及线程本地 Dataset。
        """
        # 关闭线程本地 ds
        ds = getattr(self._thread_local, 'vrt_ds', None)
        if ds:
            try:
                ds = None
                del self._thread_local.vrt_ds
            except Exception:
                pass
        # 删除临时 VRT 文件
        if self._vrt_path and self._vrt_path != self.tiff_path:
            try:
                os.remove(self._vrt_path)
                self.logger.info(f"已删除临时 VRT 文件: {self._vrt_path}")
            except Exception as e:
                self.logger.warning(f"删除临时 VRT 文件失败: {e}")
        self._vrt_path = None


class GenerateDyTilesView(View):
    """
    通过 POST 请求接收 JSON 参数：
    {
      "epsg_code": "3857",
      "tiff_path": "...",
      "min_level": "0",
      "max_level": "5",
      "file_id": "...",
      "file_name": "..."
    }
    然后动态生成切片。
    """
    def post(self, request, *args, **kwargs):
        try:
            # 解析 JSON 或表单
            try:
                data = json.loads(request.body)
            except Exception:
                data = request.POST

            epsg_code = data.get('epsg_code')
            tiff_path = data.get('tiff_path')
            min_level = data.get('min_level')
            max_level = data.get('max_level')
            file_id = data.get('file_id')
            file_name = data.get('file_name')

            if not all([epsg_code, tiff_path, min_level, max_level, file_id, file_name]):
                return JsonResponse({'error': '缺少必要参数'}, status=400)

            # 可根据需要从请求中获取并设置可选配置，例如并发数、是否自动构建 overviews 等
            processor = TiffDyProcessor(epsg_code, tiff_path, min_level, max_level, file_id, file_name)
            # 示例：如果前端传递 workers, no_overviews, build_overviews 等参数，可设置：
            # workers = data.get('workers')
            # if workers: processor.num_workers = int(workers)
            # if data.get('no_overviews') == 'true': processor.use_overviews = False
            # if data.get('build_overviews') == 'true': processor.build_overviews_auto = True
            # if data.get('cache_max'): processor.gdal_cache_max = int(data.get('cache_max'))
            # if data.get('gdal_threads'): processor.gdal_num_threads = data.get('gdal_threads')
            # if data.get('no_wrap') == 'true': processor.wrap_longitude = False
            # if data.get('retry'): processor.retry_count = int(data.get('retry'))

            processor.process_tiles()

            return JsonResponse({'message': '切片生成成功'})
        except Exception as e:
            logger.error(f"切片生成失败: {e}")
            return JsonResponse({'error': str(e)}, status=500)
