#### 
    拓扑关系检查

# 高性能拓扑关系检查器 (Topology Checker)

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

这是一个专业、高效的 Python 工具，用于对地理空间矢量数据（点、线、面）进行全面的拓扑关系检查。它能帮助您在数据入库前或质量评估流程中，自动化地识别和定位不符合拓扑规则的几何要素。

本工具的核心优势在于其**高性能算法**、**清晰的API**以及经过严格验证的**公理化检查逻辑**，特别是对“悬挂线”等复杂场景的精确处理。

## ✨ 核心功能

- **支持多种数据源**: 直接从 **Shapefile** (`.shp`)、**GeoJSON** (`.geojson`) 文件或 **GeoJSON 字符串**加载数据。
- **全面的检查规则**: 内置一套完整的拓扑检查规则集：
  - **通用规则**:
    - `must_be_valid`: 检查几何有效性（例如，面不自相交）。
  - **面要素规则**:
    - `must_not_overlap`: 检查面与面之间是否重叠。
    - `must_not_have_gaps`: 检查连续的面要素组之间是否存在缝隙。
  - **线要素规则**:
    - `must_not_self_intersect`: 检查线是否自相交。
    - `must_not_intersect`: 检查线与线之间是否交叉（非端点接触）。
    - `must_not_have_dangles`: 检查是否存在孤立的悬挂线。
  - **点要素规则**:
    - `must_be_unique_points`: 检查是否存在坐标完全重复的点。
- **详尽的检查报告**: 返回双重结果：
  1.  一个易于解析的 **JSON 格式化报告**，包含总体状态、各规则的检查结果、失败统计等。
  2.  一个包含所有**失败要素**的 `GeoDataFrame` 字典，按失败规则分类，便于进行可视化分析或后续的自动化修复。
- **高性能**: 利用 `geopandas` 的空间索引（`sindex`）和 `pandas` 的高效数据处理能力，确保在大数据集上也能快速完成检查。

## ⚙️ 环境要求与安装

本工具主要依赖 `geopandas` 及其生态。强烈建议使用 `conda` 或 `mamba` 来安装，以避免复杂的依赖问题。

**1. 推荐方式：使用 Conda / Mamba**

```bash
# 推荐使用 mamba，速度更快
conda install -c conda-forge mamba
mamba install -c conda-forge geopandas networkx
```

**2. Pip 方式 (需要您自行处理GDAL等底层库的安装)**

```bash
pip install geopandas networkx pandas numpy shapely
```

**3. 将脚本放入您的项目**

将 `topology_checker_v4.1_final.py` 文件复制到您的项目目录中。

## 🚀 如何使用 (详细调用方法)

使用本工具的流程非常简单，主要分为三步：**实例化检查器** -> **运行检查** -> **处理结果**。

### 步骤 1: 导入并实例化检查器

首先，从文件中导入 `TopologyChecker` 类。

```python
from topology_checker_v4_1_final import TopologyChecker
```

您可以通过以下两种方式之一来创建检查器实例：

**方式 A: 从文件加载 (推荐)**

```python
# 从 Shapefile 加载，并指定 'id' 字段作为唯一标识符
checker = TopologyChecker.from_file(
    file_path='path/to/your/data.shp',
    id_field='id'
)

# 从 GeoJSON 文件加载
checker_geojson = TopologyChecker.from_file(
    file_path='path/to/your/data.geojson',
    id_field='feature_id'
)
```
> **提示**: 如果不提供 `id_field`，工具会默认使用 GeoDataFrame 的索引 (`index`) 作为唯一标识。

**方式 B: 从 GeoJSON 字符串加载**

```python
geojson_string = """
{
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "properties": { "id": 1 },
      "geometry": { "type": "Point", "coordinates": [ 0, 0 ] }
    },
    {
      "type": "Feature",
      "properties": { "id": 2 },
      "geometry": { "type": "Point", "coordinates": [ 0, 0 ] }
    }
  ]
}
"""
checker_str = TopologyChecker.from_geojson_string(
    geojson_string=geojson_string,
    id_field='id'
)
```

### 步骤 2: 运行检查

调用实例的 `run_checks()` 方法来执行检查。

**方式 A: 运行所有适用的规则**

系统会自动根据数据的几何类型（点、线或面）选择所有支持的规则进行检查。

```python
report, failed_gdfs = checker.run_checks()
```

**方式 B: 运行指定的规则子集**

如果您只关心特定的几个规则，可以传入一个规则名称列表。

```python
# 只检查面是否重叠和是否存在缝隙
specific_rules = ['must_not_overlap', 'must_not_have_gaps']
report, failed_gdfs = checker.run_checks(rules=specific_rules)
```

### 步骤 3: 解析和使用结果

`run_checks()` 方法返回一个元组 `(report, failed_gdfs)`，这是您需要处理的核心结果。

- **`report` (字典)**: 这是一个JSON友好的字典，包含了完整的检查摘要。

  ```json
  // report 结构示例
  {
      "overall_status": "失败",
      "checks": {
          "must_not_overlap": {
              "status": "FAILED",
              "message": "发现互相重叠的面要素。",
              "failed_count": 2,
              "failed_ids_preview": [1, 2],
              "total_applicable": 8,
              "passed_count": 6
          },
          "must_not_have_gaps": {
              "status": "PASSED",
              "message": "所有面要素组内部均未发现缝隙。",
              "failed_count": 0,
              // ...
          }
      }
  }
  ```

- **`failed_gdfs` (字典)**: 这是一个特殊的字典，其 `键`是失败的规则名称，`值`是一个包含所有该规则失败要素的 `GeoDataFrame`。

  ```python
  # failed_gdfs 结构示例
  {
      'must_not_overlap': <GeoDataFrame with 2 rows (IDs: 1, 2)>,
      'must_be_valid': <GeoDataFrame with 1 row (ID: 4)>
  }
  ```
  您可以直接对这些 GeoDataFrame 进行操作，例如保存到文件或进行可视化。

## 📋 完整使用示例

下面是一个检查面数据 Shapefile 的完整流程。

```python
import json
from topology_checker_v4_1_final import TopologyChecker, setup_polygon_data, setup_test_environment
import os

# --- 1. 准备测试数据 (在实际使用中，您会使用自己的文件) ---
TEST_DIR = setup_test_environment("user_demo")
poly_gdf = setup_polygon_data()
my_shapefile = os.path.join(TEST_DIR, 'my_polygons.shp')
poly_gdf.to_file(my_shapefile, driver='ESRI Shapefile')

print(f"测试数据已创建: {my_shapefile}")

# --- 2. 实例化检查器并运行 ---
try:
    # 从文件加载数据，指定 'id' 字段
    checker = TopologyChecker.from_file(my_shapefile, id_field='id')

    # 运行所有适用的检查
    report, failed_gdfs = checker.run_checks()

    # --- 3. 处理和展示结果 ---
    print("\n" + "="*20 + " 质量检查报告 " + "="*20)
    print(json.dumps(report, indent=4, ensure_ascii=False))
    print("="*54)

    if report['overall_status'] == '失败':
        print("\n发现拓扑错误！以下是按规则分类的失败要素详情：")
        for rule_name, failed_gdf in failed_gdfs.items():
            failed_ids = sorted(failed_gdf['id'].tolist())
            print(f"\n--- 规则: '{rule_name}' ---")
            print(f"    失败要素数量: {len(failed_gdf)}")
            print(f"    失败要素ID: {failed_ids}")
            
            # 您可以在这里进行更多操作，例如保存失败要素
            output_path = os.path.join(TEST_DIR, f"failed_{rule_name}.shp")
            failed_gdf.to_file(output_path)
            print(f"    失败要素已保存至: {output_path}")

    else:
        print("\n恭喜！所有拓扑检查均已通过。")

except Exception as e:
    print(f"发生错误: {e}")

finally:
    # 清理测试环境
    import shutil
    if os.path.exists(TEST_DIR):
        shutil.rmtree(TEST_DIR)
        print(f"\n测试目录 '{TEST_DIR}' 已清理。")

```

## 🔬 运行内置的自动化测试

脚本自带一套完整的单元测试和演示。您可以直接运行此 Python 文件来验证您的环境是否配置正确，并查看所有功能的输出示例。

```bash
python topology_checker_v4.1_final.py
```

如果一切正常，您将在最后看到 `🎉 所有演示与自动化测试全部通过！` 的消息。

## 📄 许可证

本项目采用 [MIT](https://opensource.org/licenses/MIT) 许可证。