# -*- coding: utf-8 -*-
# =============================================================================
#  Geo-TIFF Dynamic WMTS Tile Service
#
#  Author: [Your Name/Team]
#  Version: 2.0.0
#  Date: 2025-06-12
#
#  Features:
#  - Serves GeoTIFF files as a standard WMTS service.
#  - Supports both RESTful and KVP (Key-Value Pair) request styles.
#  - Dynamically generates tiles, no pre-tiling required.
#  - Implements an asynchronous file-based caching system for performance.
#  - Handles single-band (e.g., DEM) and multi-band (RGB/RGBA) rasters.
#  - Applies a customizable color map for single-band data.
#  - Correctly handles Y-axis orientation for proper image display.
#  - Robust error handling and OGC-compliant exception reporting.
#  - Generates a valid GetCapabilities document advertising all features.
# =============================================================================

import io
import logging
import math
import os
from concurrent.futures import ThreadPoolExecutor
from typing import Optional, Tuple
from xml.etree.ElementTree import Element, SubElement, tostring
from xml.dom import minidom

import numpy as np
from PIL import Image
from django.http import HttpResponse, HttpRequest
from django.conf import settings as django_settings
from django.urls import reverse
from pyproj import Transformer, CRS
from django.views import View
from osgeo import gdal, osr

# 导入您项目中的模型
# !!! 【重要】请确保这个导入路径在您的项目中是正确的 !!!
from glff.dao.geo_file.geo_file_detail import GeoFileDetail

# --- 全局配置与初始化 ---

gdal.UseExceptions()

# 线程池用于I/O密集型任务，如写入缓存
# 线程数可由Django的settings.py配置，默认为CPU核心数
io_executor = ThreadPoolExecutor(
    max_workers=getattr(django_settings, 'TILE_SERVICE_IO_WORKERS', os.cpu_count() or 4),
    thread_name_prefix='tile_cache_writer'
)


# --- 核心处理逻辑封装 (与视图解耦) ---

class TileProcessor:
    """
    一个封装了瓦片生成核心算法的处理器类。
    它与HTTP请求无关，专注于栅格数据的处理，使其更易于测试和维护。
    """
    TILE_SIZE = 256

    def generate_tile(self, dataset: gdal.Dataset, tile_bounds: tuple, target_srs: osr.SpatialReference,
                      color_map: Optional[dict] = None) -> Optional[Image.Image]:
        """
        生成一个瓦片图像，并可选择性地应用色彩映射。
        """
        source_srs = osr.SpatialReference(wkt=dataset.GetProjection())
        no_data_value = dataset.GetRasterBand(1).GetNoDataValue()

        warp_options = {
            'format': 'MEM',
            'outputBounds': tile_bounds,
            'width': self.TILE_SIZE,
            'height': self.TILE_SIZE,
            'dstSRS': target_srs,
            'srcSRS': source_srs,
            'resampleAlg': gdal.GRIORA_Cubic,
            'outputType': gdal.GDT_Float32 if color_map else gdal.GDT_Byte,
            'dstNodata': 0 if no_data_value is not None else None,
        }
        if no_data_value is not None:
            warp_options['srcNodata'] = no_data_value

        tile_dataset = gdal.Warp('', dataset, **warp_options)
        if not tile_dataset:
            return None
        return self._convert_gdal_to_pil(tile_dataset, color_map)

    def _convert_gdal_to_pil(self, tile_dataset: gdal.Dataset, color_map: Optional[dict]) -> Optional[Image.Image]:
        """
        将GDAL数据集转换为PIL图像，支持色彩映射，并修正Y轴方向。
        """
        band_count = tile_dataset.RasterCount
        if band_count == 0: return None

        # 路径1: 单波段数据并应用色彩映射
        if band_count == 1 and color_map:
            band_data = tile_dataset.GetRasterBand(1).ReadAsArray().astype(np.float32)
            band_data = np.flipud(band_data)  # 【核心修正】垂直翻转数组以修正Y轴方向
            no_data = tile_dataset.GetRasterBand(1).GetNoDataValue()
            rgba = np.zeros((self.TILE_SIZE, self.TILE_SIZE, 4), dtype=np.uint8)
            stops = sorted(color_map.keys())
            red_values = [color[0] for color in color_map.values()]
            green_values = [color[1] for color in color_map.values()]
            blue_values = [color[2] for color in color_map.values()]
            rgba[:, :, 0] = np.interp(band_data, stops, red_values)
            rgba[:, :, 1] = np.interp(band_data, stops, green_values)
            rgba[:, :, 2] = np.interp(band_data, stops, blue_values)
            rgba[:, :, 3] = 255
            if no_data is not None: rgba[band_data == no_data, 3] = 0
            return Image.fromarray(rgba, 'RGBA')

        # 路径2: 标准RGB/RGBA或灰度图处理
        bands_data = [np.flipud(tile_dataset.GetRasterBand(i + 1).ReadAsArray()) for i in range(band_count)]
        if band_count == 1:
            image = Image.fromarray(bands_data[0]).convert("RGBA")
        elif band_count == 3:
            image = Image.merge('RGB', [Image.fromarray(b) for b in bands_data]).convert("RGBA")
        elif band_count >= 4:
            image = Image.merge('RGBA', [Image.fromarray(b) for b in bands_data[:4]])
        else:
            return None

        # 将纯黑色的NoData区域设为透明
        data = np.array(image)
        mask = (data[:, :, 0] == 0) & (data[:, :, 1] == 0) & (data[:, :, 2] == 0)
        data[mask, 3] = 0
        return Image.fromarray(data)


# --- 视图类 ---

class TiffDyViewPro(View):
    """
    【最终版】工业级动态瓦片服务视图。
    同时支持 WMTS RESTful 和 KVP 两种接口模式。
    """
    # 瓦片尺寸 (像素)
    TILE_SIZE = 256

    # 示例DEM色彩映射 (value: [R, G, B])
    DEM_COLOR_MAP = {
        0: [67, 103, 135], 500: [91, 145, 165], 1000: [157, 192, 163],
        2000: [224, 237, 168], 3000: [216, 187, 122], 4000: [181, 129, 80],
    }

    # 实例化瓦片处理器
    processor = TileProcessor()

    # --- 1. 请求调度中心 ---
    def get(self, request: HttpRequest, *args, **kwargs) -> HttpResponse:
        """
        作为总调度中心，根据请求类型分发任务。
        """
        # 统一将查询参数的键转为小写，以实现大小写不敏感
        params = {k.lower(): v for k, v in request.GET.items()}

        # 检查是否为 KVP (Key-Value Pair) 模式的请求
        if params.get('service', '').upper() == 'WMTS':
            request_type = params.get('request', '').lower()
            if request_type == 'getcapabilities':
                return self._handle_kvp_get_capabilities(request, params)
            elif request_type == 'gettile':
                return self._handle_kvp_get_tile(request, params)
            else:
                return self._create_exception_report("InvalidRequest", f"不支持的请求类型: {params.get('request')}")

        # 如果不是KVP，则按 RESTful 模式处理
        if 'capabilities.xml' in request.path.lower():
            return self.get_capabilities(request, **kwargs)
        else:
            return self.get_tile(request, **kwargs)

    # --- 2. KVP模式处理器 ---
    def _handle_kvp_get_capabilities(self, request: HttpRequest, params: dict) -> HttpResponse:
        """处理KVP模式的GetCapabilities请求。"""
        # 为简化，我们要求KVP请求必须提供图层和坐标系信息
        file_id = params.get('layer')
        epsg_code_str = params.get('tilematrixset')

        if not file_id or not epsg_code_str:
            return self._create_exception_report("MissingParameter",
                                                 "KVP GetCapabilities请求需要'layer'和'tilematrixset'参数。")

        # 提取EPSG代码，例如从 "EPSG_3857" 中提取 "3857"
        epsg_code = epsg_code_str.upper().replace('EPSG_', '')
        return self.get_capabilities(request, FILEID=file_id, EPSGCODE=epsg_code)

    def _handle_kvp_get_tile(self, request: HttpRequest, params: dict) -> HttpResponse:
        """处理KVP模式的GetTile请求。"""
        try:
            kwargs = {
                'FILEID': params['layer'],
                'EPSGCODE': params['tilematrixset'].upper().replace('EPSG_', ''),
                'TileMatrix': params['tilematrix'],
                'TileCol': params['tilecol'],
                'TileRow': params['tilerow'],
                'FORMAT': params.get('format', 'image/png')
            }
        except KeyError as e:
            return self._create_exception_report("MissingParameter", f"缺少必要的KVP参数: {e}")

        return self.get_tile(request, **kwargs)

    # --- 3. 核心业务逻辑 (RESTful模式的直接入口) ---
    def get_tile(self, request: HttpRequest, FILEID: str, EPSGCODE: str, TileMatrix: str, TileCol: str,
                 TileRow: str,FORMAT: Optional[str] = None) -> HttpResponse:
        """处理瓦片请求。该方法现在被RESTful和KVP两种模式共用。"""
        try:
            tile_z, tile_x, tile_y = int(TileMatrix), int(TileCol), int(TileRow)
            target_epsg = int(EPSGCODE)
        except (ValueError, TypeError):
            return self._create_exception_report("InvalidParameterValue", "无效的瓦片参数格式。")

        try:
            tiff_path = self._get_tiff_path(FILEID)
            cache_path = self._get_cache_path(tiff_path, target_epsg, tile_z, tile_x, tile_y)

            # 优先从缓存读取
            if os.path.exists(cache_path):
                with open(cache_path, 'rb') as f:
                    return HttpResponse(f.read(), content_type='image/png')

            # 使用 `with` 语句确保数据集被正确关闭，即使发生错误
            with gdal.Open(tiff_path, gdal.GA_ReadOnly) as dataset:
                source_srs, source_gt = osr.SpatialReference(wkt=dataset.GetProjection()), dataset.GetGeoTransform()

                tile_bounds = self._calculate_tile_bounds(tile_z, tile_x, tile_y, target_epsg)
                tiff_bounds_in_tile_crs = self._transform_tiff_bounds(source_gt, dataset.RasterXSize,
                                                                      dataset.RasterYSize, source_srs, target_epsg)

                # 如果瓦片完全在数据范围外，返回透明空瓦片
                if self._is_tile_out_of_bounds(tile_bounds, tiff_bounds_in_tile_crs):
                    return self._create_image_response(None)

                # 根据文件名判断是否为DEM数据，以应用色彩映射
                color_map = self.DEM_COLOR_MAP if 'dem' in tiff_path.lower() else None

                # 生成瓦片
                tile_image = self.processor.generate_tile(
                    dataset=dataset,
                    tile_bounds=tile_bounds,
                    target_srs=osr.SpatialReference(wkt=CRS.from_epsg(target_epsg).to_wkt()),
                    color_map=color_map
                )

            # 如果生成的是空图像，返回标准空瓦片
            if not tile_image or self._is_image_empty(tile_image):
                return self._create_image_response(None)

            # 异步写入缓存
            self._save_tile_to_cache_async(cache_path, tile_image)
            return self._create_image_response(tile_image)

        except FileNotFoundError as e:
            return self._create_exception_report("InvalidParameterValue", str(e), status=404)
        except Exception as e:
            logging.error(f"生成瓦片时发生严重错误: {e}", exc_info=True)
            return self._create_exception_report("NoApplicableCode", "服务器内部错误，无法生成瓦片。", status=500)

    def get_capabilities(self, request: HttpRequest, FILEID: str, EPSGCODE: str, **kwargs) -> HttpResponse:
        """生成 WMTS GetCapabilities XML 文档。该方法现在被RESTful和KVP两种模式共用。"""
        try:
            target_epsg = int(EPSGCODE)
            tiff_path = self._get_tiff_path(FILEID)

            with gdal.Open(tiff_path, gdal.GA_ReadOnly) as dataset:
                source_srs, source_gt = osr.SpatialReference(wkt=dataset.GetProjection()), dataset.GetGeoTransform()
                bounds = self._transform_tiff_bounds(source_gt, dataset.RasterXSize, dataset.RasterYSize, source_srs,
                                                     target_epsg)

            xml_string = self._build_capabilities_xml(request, FILEID, f"Layer for {FILEID}", target_epsg, bounds)
            return HttpResponse(xml_string, content_type='application/xml')
        except FileNotFoundError as e:
            return self._create_exception_report("InvalidParameterValue", str(e), status=404)
        except Exception as e:
            logging.error(f"生成Capabilities文档时发生错误: {e}", exc_info=True)
            return self._create_exception_report("NoApplicableCode", "无法生成Capabilities文档。", status=500)

    # --- 4. 辅助方法 ---
    def _build_capabilities_xml(self, request: HttpRequest, layer_id: str, title: str, epsg: int, bounds: tuple) -> str:
        """构建WMTS GetCapabilities XML，同时声明KVP和RESTful。"""
        restful_base_url = request.build_absolute_uri().split('/capabilities.xml')[0]
        kvp_endpoint_url = request.build_absolute_uri(reverse('tile_service:wmts_kvp_endpoint'))

        root = Element('Capabilities', {'version': '1.0.0', 'xmlns': 'http://www.opengis.net/wmts/1.0',
                                        'xmlns:ows': 'http://www.opengis.net/ows/1.1',
                                        'xmlns:xlink': 'http://www.w3.org/1999/xlink'})

        operations_metadata = SubElement(root, 'ows:OperationsMetadata')
        for op_name in ['GetCapabilities', 'GetTile']:
            op = SubElement(operations_metadata, 'ows:Operation', {'name': op_name})
            dcp = SubElement(op, 'ows:DCP')
            http = SubElement(dcp, 'ows:HTTP')
            SubElement(http, 'ows:Get', {'xlink:href': kvp_endpoint_url})

        contents = SubElement(root, 'Contents')
        layer = SubElement(contents, 'Layer')
        SubElement(layer, 'ows:Title').text = title
        SubElement(layer, 'ows:Identifier').text = layer_id

        bbox = SubElement(layer, 'ows:WGS84BoundingBox', {'crs': f'urn:ogc:def:crs:EPSG::{epsg}'})
        SubElement(bbox, 'ows:LowerCorner').text = f'{bounds[0]} {bounds[1]}'
        SubElement(bbox, 'ows:UpperCorner').text = f'{bounds[2]} {bounds[3]}'

        tms_link = SubElement(layer, 'TileMatrixSetLink')
        SubElement(tms_link, 'TileMatrixSet').text = f'EPSG_{epsg}'

        SubElement(layer, 'ResourceURL', {'format': 'image/png', 'resourceType': 'tile',
                                          'template': f'{restful_base_url}/{{TileMatrix}}/{{TileCol}}/{{TileRow}}.png'})

        tms = SubElement(contents, 'TileMatrixSet')
        SubElement(tms, 'ows:Identifier').text = f'EPSG_{epsg}'
        SubElement(tms, 'ows:SupportedCRS').text = f'urn:ogc:def:crs:EPSG::{epsg}'

        rough_string = tostring(root, 'utf-8')
        reparsed = minidom.parseString(rough_string)
        return reparsed.toprettyxml(indent="  ")

    def _create_exception_report(self, code: str, text: str, status: int = 400) -> HttpResponse:
        """生成一个OGC标准的异常报告XML。"""
        root = Element('ExceptionReport', {'xmlns': 'http://www.opengis.net/ows/1.1', 'version': '1.1.0'})
        exception = SubElement(root, 'Exception', {'exceptionCode': code})
        SubElement(exception, 'ExceptionText').text = text
        xml_string = tostring(root, 'utf-8')
        return HttpResponse(xml_string, content_type='application/xml', status=status)

    def _get_tiff_path(self, file_id: str) -> str:
        """根据File ID从数据库获取文件物理路径。"""
        file_detail = GeoFileDetail.get_by_id(file_id)
        if not file_detail: raise FileNotFoundError(f"数据库中未找到ID为 {file_id} 的记录。")
        return os.path.join(django_settings.JFS_PATH, file_detail.base_path, file_detail.path, file_detail.filename)

    def _get_cache_path(self, tiff_path: str, epsg: int, z: int, x: int, y: int) -> str:
        """根据瓦片参数生成缓存文件的绝对路径。"""
        tile_cache_dir = os.path.join(os.path.dirname(tiff_path), "tile_cache")
        return os.path.join(tile_cache_dir, str(epsg), str(z), str(x), f"{y}.png")

    def _is_image_empty(self, image: Image.Image) -> bool:
        """检查一个PIL图像是否完全透明，从而判断是否为空。"""
        if image.mode != 'RGBA': return False
        return not image.getbbox() or np.all(np.array(image.getchannel('A')) == 0)

    def _save_tile_to_cache_async(self, cache_path: str, image: Image.Image):
        """使用线程池异步地将瓦片图像保存到文件缓存。"""

        def save_task():
            try:
                os.makedirs(os.path.dirname(cache_path), exist_ok=True)
                image.save(cache_path, format='PNG')
            except Exception as e:
                logging.error(f"异步写入缓存失败: {e}", exc_info=True)

        io_executor.submit(save_task)

    def _create_image_response(self, image: Optional[Image.Image]) -> HttpResponse:
        """将PIL图像转换为Django的HttpResponse对象。"""
        if image is None: image = Image.new("RGBA", (self.TILE_SIZE, self.TILE_SIZE), (0, 0, 0, 0))
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='PNG')
        img_byte_arr.seek(0)
        return HttpResponse(img_byte_arr.getvalue(), content_type='image/png')

    def _transform_tiff_bounds(self, gt: tuple, x_size: int, y_size: int, source_srs: osr.SpatialReference,
                               target_epsg: int) -> tuple:
        """将源栅格的范围从其自身坐标系转换到目标瓦片坐标系。"""
        min_x, max_y = gt[0], gt[3]
        max_x = min_x + gt[1] * x_size
        min_y = max_y + gt[5] * y_size
        transformer = Transformer.from_crs(CRS(source_srs.ExportToWkt()), CRS.from_epsg(target_epsg), always_xy=True)
        points = [(min_x, min_y), (min_x, max_y), (max_x, min_y), (max_x, max_y)]
        transformed_points = [transformer.transform(x, y) for x, y in points]
        xs = [p[0] for p in transformed_points]
        ys = [p[1] for p in transformed_points]
        return min(xs), min(ys), max(xs), max(ys)

    def _is_tile_out_of_bounds(self, tile_bounds: tuple, tiff_bounds: tuple) -> bool:
        """【修正版】检查瓦片范围与TIFF数据范围是否相交。"""
        tile_min_x_in, tile_max_y_in, tile_max_x_in, tile_min_y_in = tile_bounds
        tile_std_bounds = (tile_min_x_in, tile_min_y_in, tile_max_x_in, tile_max_y_in)
        tile_min_x, tile_min_y, tile_max_x, tile_max_y = tile_std_bounds
        tiff_min_x, tiff_min_y, tiff_max_x, tiff_max_y = tiff_bounds
        if tile_min_x >= tiff_max_x: return True
        if tile_max_x <= tiff_min_x: return True
        if tile_min_y >= tiff_max_y: return True
        if tile_max_y <= tiff_min_y: return True
        return False

    def _calculate_tile_bounds(self, z: int, x: int, y: int, epsg: int) -> tuple:
        """根据TMS标准计算指定瓦片的地理边界。返回 (min_x, max_y, max_x, min_y)"""
        if epsg == 3857:
            origin_x, origin_y = -20037508.342789244, 20037508.342789244
            initial_resolution = 2 * origin_y / self.TILE_SIZE
        elif epsg in [4326, 4490]:
            origin_x, origin_y = -180.0, 90.0
            initial_resolution = 360.0 / self.TILE_SIZE
        else:
            raise ValueError(f"不支持的瓦片方案坐标系: EPSG:{epsg}")

        resolution = initial_resolution / (2 ** z)
        x_min = origin_x + x * self.TILE_SIZE * resolution
        y_max = origin_y - y * self.TILE_SIZE * resolution
        x_max = x_min + self.TILE_SIZE * resolution
        y_min = y_max - self.TILE_SIZE * resolution
        return x_min, y_max, x_max, y_min

