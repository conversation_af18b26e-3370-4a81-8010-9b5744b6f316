import psycopg2
import re
import logging
import time
from psycopg2 import pool, errors
from psycopg2 import sql as psycopg_sql
from django.conf import settings

logger = logging.getLogger(__name__)


class DatabaseConnectionPool:
    """线程安全的数据库连接池"""
    _instance = None
    _pool = None

    def __new__(cls):
        if not cls._instance:
            cls._instance = super().__new__(cls)
            cls._initialize_pool()
        return cls._instance

    @classmethod
    def _initialize_pool(cls):
        """从Django配置初始化连接池"""
        db_config = settings.DATABASES['default']
        cls._pool = pool.ThreadedConnectionPool(
            minconn=5,
            maxconn=20,
            host=db_config['HOST'],
            user=db_config['USER'],
            password=db_config['PASSWORD'],
            dbname=db_config['NAME'],
            port=db_config['PORT']
        )
        logger.info(f"Initialized DB pool: min=5, max=20")

    def get_conn(self, timeout=10):
        """带超时的连接获取"""
        start = time.time()
        while time.time() - start < timeout:
            try:
                return self._pool.getconn(False)  # nonblock=True
            except psycopg2.pool.PoolError:
                time.sleep(0.1)
        raise TimeoutError(f"Connection timeout after {timeout}s")

    def put_conn(self, conn):
        """安全归还连接"""
        try:
            if not conn.closed:
                self._pool.putconn(conn)
        except Exception as e:
            logger.error(f"Error returning connection: {str(e)}")

    def close_all(self):
        """关闭所有连接"""
        self._pool.closeall()
        logger.info("DB pool closed")


class DatabaseManager:
    def __init__(self):
        self.pool = DatabaseConnectionPool()

    def execute_operation(self, schema: str, sql_statement: str) -> dict:
        """主执行方法"""
        conn = None
        try:
            # 解析SQL
            parse_result = self._parse_table_name(sql_statement, schema)
            if not parse_result['status']:
                return parse_result

            target_schema, table_name = parse_result['data']

            # 获取连接并执行
            conn = self.pool.get_conn(timeout=5)
            return self._execute_with_connection(
                conn, target_schema, table_name, sql_statement
            )
        except TimeoutError as e:
            logger.error(str(e))
            return {'status': False, 'message': '系统繁忙，请稍后重试'}
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            return {'status': False, 'message': '系统内部错误'}
        finally:
            if conn:
                self.pool.put_conn(conn)

    def _execute_with_connection(self, conn, schema, table, sql_str):
        """使用连接执行操作"""
        try:
            with conn.cursor() as cursor:
                # 检查表是否存在
                if self._table_exists(cursor, schema, table):
                    logger.info(f"表已存在: {schema}.{table}")
                    return {'status': False, 'message': '表已存在'}

                # 创建模式
                self._create_schema(cursor, schema)

                # 执行SQL (关键修复：使用重命名的psycopg_sql)
                cursor.execute(psycopg_sql.SQL(sql_str))
                conn.commit()
                return {'status': True, 'message': '创建成功'}

        except errors.DuplicateTable:
            logger.warning(f"表已存在（并发冲突）: {schema}.{table}")
            conn.rollback()
            return {'status': False, 'message': '表已存在'}
        except errors.SyntaxError as e:
            logger.error(f"SQL语法错误: {str(e)}")
            return {'status': False, 'message': f'无效SQL: {e}'}
        except Exception as e:
            conn.rollback()
            raise

    def _parse_table_name(self, sql_str: str, schema: str) -> dict:
        """解析SQL语句获取表信息（兼容标准表和分区表）"""
        try:
            # 改进后的正则表达式，同时匹配两种语法
            match = re.match(
                r'^CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?'
                r'(?:"?([\w_]+)"?\.)?'  # 捕获模式名（允许引号）
                r'(?:"?([\w_]+)"?)'  # 捕获表名（允许引号）
                r'\s*(?:\(|PARTITION\s+OF\s+\S+)',  # 匹配列定义或分区子句
                sql_str.strip(),
                re.IGNORECASE
            )

            if not match:
                return {'status': False, 'message': '无效的CREATE TABLE语法', 'data': None}

            sql_schema, table = match.groups()

            # 模式一致性检查
            if sql_schema and sql_schema != schema:
                msg = f"模式冲突: 请求模式[{schema}]但SQL使用[{sql_schema}]"
                return {'status': False, 'message': msg, 'data': None}

            return {'status': True, 'message': '解析成功', 'data': (schema, table)}
        except Exception as e:
            return {'status': False, 'message': f'SQL解析失败: {str(e)}', 'data': None}

    def _create_schema(self, cursor, schema: str):
        """创建模式（如果不存在）"""
        cursor.execute(
            psycopg_sql.SQL("CREATE SCHEMA IF NOT EXISTS {}")
            .format(psycopg_sql.Identifier(schema))
        )

    def _table_exists(self, cursor, schema: str, table: str) -> bool:
        """检查表是否存在"""
        cursor.execute("""
            SELECT EXISTS (
                SELECT 1 
                FROM information_schema.tables 
                WHERE table_schema = %s AND table_name = %s
            )
        """, (schema, table))
        return cursor.fetchone()[0]
