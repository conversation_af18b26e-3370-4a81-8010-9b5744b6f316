import io
import os
from PIL import Image
from django.http import HttpResponse
from rest_framework.response import Response
from rest_framework.views import APIView
from osgeo import gdal, osr
import numpy as np
from concurrent.futures import ThreadPoolExecutor

# 启用异常模式
gdal.UseExceptions()


# 创建一个全局的线程池，设置较小的线程数
executor = ThreadPoolExecutor(max_workers=8)  # 根据实际情况调整

class TiffView(APIView):

    # 不支持动态投影
    def get(self, request, TileMatrix, TileCol, TileRow):
        try:
            tile_matrix = int(TileMatrix)
            tile_col = int(TileCol)
            tile_row = int(TileRow)
        except ValueError:
            return Response({"error": "TileMatrix, TileCol, TileRow 参数错误"}, status=400)

        tiff_path = r"G:\testcode\ceshitiff\world.tif"
        if not os.path.exists(tiff_path):
            return Response({"error": "指定的 TIFF 文件不存在"}, status=404)

        # 打开 TIFF 文件
        dataset = gdal.Open(tiff_path, gdal.GA_ReadOnly)
        if not dataset:
            return Response({"error": "文件未找到"}, status=404)

        # 获取 TIFF 文件的有效范围和投影信息
        geo_transform = dataset.GetGeoTransform()
        min_x = geo_transform[0]
        max_y = geo_transform[3]
        max_x = min_x + geo_transform[1] * dataset.RasterXSize
        min_y = max_y + geo_transform[5] * dataset.RasterYSize

        # 获取投影信息
        projection = dataset.GetProjection()
        srs = osr.SpatialReference()
        srs.ImportFromWkt(projection)
        epsg_code = srs.GetAttrValue('AUTHORITY', 1)  # 获取 EPSG 代码
        print(f"当前tif文件坐标系为：{epsg_code}")

        # 根据 EPSG 代码选择目标坐标系
        if epsg_code == '3857':
            dst_srs = osr.SpatialReference()
            dst_srs.ImportFromEPSG(3857)
            coordinate_system = 'EPSG:3857'
        elif epsg_code == '4326':
            dst_srs = osr.SpatialReference()
            dst_srs.ImportFromEPSG(4326)
            coordinate_system = 'EPSG:4326'
        elif epsg_code == '4490':
            dst_srs = osr.SpatialReference()
            dst_srs.ImportFromEPSG(4490)
            coordinate_system = 'EPSG:4490'
        else:
            return Response({"error": "不支持的投影类型"}, status=400)

        # 生成瓦片的缓存路径，按照 TMS 结构
        tile_cache_dir = os.path.dirname(tiff_path)
        tile_cache_dir = tile_cache_dir + "\\tile_cache\\"
        tile_cache_path = os.path.join(tile_cache_dir, str(tile_matrix), str(tile_col), f"{tile_row}.png")

        # 优先检查缓存
        if os.path.exists(tile_cache_path):
            print(f"从缓存中返回瓦片: {tile_cache_path}")
            with open(tile_cache_path, 'rb') as f:
                return HttpResponse(f.read(), content_type='image/png')

        # 计算瓦片边界
        tile_size = 256
        x_min, y_max, x_max, y_min = self.calculate_tile_bounds(tile_matrix, tile_col, tile_row, tile_size, coordinate_system)

        # 检查瓦片边界是否在 TIFF 文件的有效范围内
        # 检查瓦片边界是否与 TIFF 文件的有效范围相交
        if (x_min > max_x or x_max < min_x or
                y_min > max_y or y_max < min_y):
            print("瓦片超出有效范围，返回空瓦片。")
            return self.generate_empty_tile()

        # 直接重投影数据集
        reprojected_dataset = gdal.Warp('', dataset, format='MEM', dstSRS=dst_srs)

        if not reprojected_dataset:
            return Response({"error": "重投影失败"}, status=500)

        # 使用线程池生成瓦片
        future = executor.submit(self.create_tile, reprojected_dataset, x_min, y_max, x_max, y_min, tile_size)
        tile_image = future.result()

        if tile_image is None:
            return Response({"error": "提取瓦片失败"}, status=500)

        # 确保无效区域透明
        no_data_value = 0  # 根据您的数据设置无效值
        tile_image = self.ensure_transparency(tile_image, no_data_value)

        # 检查图像是否为空
        if self.is_image_empty(tile_image):
            print("生成的瓦片为空，不保存到缓存。")
            return self.generate_empty_tile()

        # 确保目录存在
        os.makedirs(os.path.dirname(tile_cache_path), exist_ok=True)

        # 将瓦片保存到文件系统
        executor.submit(self.save_tile_to_file, tile_cache_path, tile_image)

        # 返回生成的瓦片
        img_byte_arr = io.BytesIO()
        tile_image.save(img_byte_arr, format='PNG')
        img_byte_arr.seek(0)
        return HttpResponse(img_byte_arr.getvalue(), content_type='image/png')

    def save_tile_to_file(self, tile_cache_path, tile_image):
        """将瓦片保存到文件系统"""
        tile_image.save(tile_cache_path, format='PNG')
        print(f"瓦片已保存到: {tile_cache_path}")

    def is_image_empty(self, image):
        """检查图像是否为空（全透明）"""
        data = np.array(image)
        return np.all(data[:, :, 3] == 0)  # 检查 alpha 通道是否全为 0

    def calculate_tile_bounds(self, tile_matrix, tile_col, tile_row, tile_size, coordinate_system):
        if coordinate_system == 'EPSG:3857':
            origin_x, origin_y = -20037508.342789244, 20037508.342789244
            initial_resolution = 2 * 20037508.342789244 / tile_size
            resolution = initial_resolution / (2 ** tile_matrix)
            x_min = origin_x + tile_col * tile_size * resolution
            y_max = origin_y - tile_row * tile_size * resolution
            x_max = x_min + tile_size * resolution
            y_min = y_max - tile_size * resolution
        elif coordinate_system == 'EPSG:4326':
            initial_resolution = 360.0 / tile_size
            resolution = initial_resolution / (2 ** tile_matrix)
            x_min = -180 + tile_col * tile_size * resolution
            y_max = 90 - tile_row * tile_size * resolution
            x_max = x_min + tile_size * resolution
            y_min = y_max - tile_size * resolution
        elif coordinate_system == 'EPSG:4490':
            # EPSG:4490 的计算方式与 EPSG:4326 类似，但需要根据具体情况调整
            initial_resolution = 360.0 / tile_size  # 这里假设与 WGS 84 相同
            resolution = initial_resolution / (2 ** tile_matrix)
            x_min = -180 + tile_col * tile_size * resolution
            y_max = 90 - tile_row * tile_size * resolution
            x_max = x_min + tile_size * resolution
            y_min = y_max - tile_size * resolution
        else:
            raise Exception(f"不支持的坐标系: {coordinate_system}")

        return x_min, y_max, x_max, y_min

    def create_tile(self, reprojected_dataset, x_min, y_max, x_max, y_min, tile_size):
        tile_dataset = gdal.Translate(
            '',
            reprojected_dataset,
            format='MEM',
            projWin=[x_min, y_max, x_max, y_min],
            width=tile_size,
            height=tile_size,
            outputType=gdal.GDT_Byte
        )
        if not tile_dataset:
            return None

        band_count = tile_dataset.RasterCount
        bands_data = [tile_dataset.GetRasterBand(i + 1).ReadAsArray() for i in range(band_count)]

        if band_count == 3:
            image = Image.merge('RGB', [Image.fromarray(band) for band in bands_data])
            image = image.convert("RGBA")
        elif band_count == 4:
            image = Image.merge('RGBA', [Image.fromarray(band) for band in bands_data])
        elif band_count == 1:
            gray_band = Image.fromarray(bands_data[0])
            image = Image.merge('RGB', [gray_band, gray_band, gray_band])
            image = image.convert("RGBA")
        else:
            raise Exception(f"不支持的波段数: {band_count}")

        return image

    def generate_empty_tile(self):
        tile_size = 256
        empty_tile = Image.new("RGBA", (tile_size, tile_size), (0, 0, 0, 0))  # 创建透明瓦片
        img_byte_arr = io.BytesIO()
        empty_tile.save(img_byte_arr, format='PNG')
        img_byte_arr.seek(0)
        return HttpResponse(img_byte_arr.getvalue(), content_type='image/png')

    def ensure_transparency(self, image, no_data_value):
        image = image.convert("RGBA")
        data = np.array(image)
        if no_data_value is not None:
            mask = (data[:, :, 0] == no_data_value) & (data[:, :, 1] == no_data_value) & (
                        data[:, :, 2] == no_data_value)
            data[mask] = (0, 0, 0, 0)  # 设置为透明
        return Image.fromarray(data)

    def post(self, request):
        return Response(2)

    def get_tile_count_for_level(self, level, min_x, max_x, min_y, max_y, epsg_code, tile_size):
        """根据 TIFF 范围和级别计算瓦片的最大最小行列值"""

        # 计算当前级别的分辨率
        if epsg_code == '3857':
            initial_resolution = 2 * 20037508.342789244 / tile_size  # Web Mercator
            resolution = initial_resolution / (2 ** level)
        elif epsg_code in ['4326', '4490']:
            initial_resolution = 360.0 / tile_size  # WGS 84
            resolution = initial_resolution / (2 ** level)
        else:
            raise Exception(f"不支持的坐标系: {epsg_code}")

        # 计算当前层级下的最小和最大列
        if epsg_code == '3857':
            min_col = int((min_x + 20037508.342789244) / (tile_size * resolution))
            max_col = int((max_x + 20037508.342789244) / (tile_size * resolution))
        else:  # 对于 EPSG:4326 和 EPSG:4490
            min_col = int((min_x + 180) / (tile_size * resolution))
            max_col = int((max_x + 180) / (tile_size * resolution))

        # 计算当前层级下的最小和最大行
        if epsg_code == '3857':
            min_row = int((20037508.342789244 - max_y) / (tile_size * resolution))
            max_row = int((20037508.342789244 - min_y) / (tile_size * resolution))
        else:  # 对于 EPSG:4326 和 EPSG:4490
            min_row = int((90 - max_y) / (tile_size * resolution))
            max_row = int((90 - min_y) / (tile_size * resolution))

        # 确保行列范围不小于0
        min_col = max(min_col, 0)
        min_row = max(min_row, 0)

        # 确保最大行列不小于最小行列
        max_col = max(max_col, min_col)  # 确保 max_col 至少等于 min_col
        max_row = max(max_row, min_row)  # 确保 max_row 至少等于 min_row

        # 计算可能的最大行列值
        if epsg_code == '3857':
            max_col = min(max_col, int((20037508.342789244 * 2) / (tile_size * resolution)))
            max_row = min(max_row, int((20037508.342789244 * 2) / (tile_size * resolution)))
        else:  # 对于 EPSG:4326 和 EPSG:4490
            max_col = min(max_col, int(360 / (tile_size * resolution)))
            max_row = min(max_row, int(180 / (tile_size * resolution)))

        return min_row, max_row, min_col, max_col
