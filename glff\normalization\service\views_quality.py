from django.http import JsonResponse, HttpResponse, FileResponse
from django.views.decorators.csrf import csrf_exempt
from django.views import View
from django.utils.decorators import method_decorator
from django.conf import settings
import os
import uuid
import json
import traceback
import time
import threading
from concurrent.futures import ThreadPoolExecutor
import shutil
from pathlib import Path
import logging
import zipfile
import tempfile
import glob

# 导入底层处理模块
from glff.normalization.quality_inspection.attribute_checker import attribute_checker
from glff.normalization.quality_inspection.geo_integrity_checker import geo_integrity_checker
from glff.normalization.quality_inspection.logical_consistency_checker import logical_consistency_checker
from glff.normalization.quality_inspection.spatial_reference_check import spatial_reference_check
from glff.normalization.quality_inspection.topology_check import vector_quality

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建必要的目录
UPLOAD_DIR = os.path.join(settings.BASE_DIR, 'media', 'quality', 'uploads')
RESULT_DIR = os.path.join(settings.BASE_DIR, 'media', 'quality', 'results')
TEMP_DIR = os.path.join(settings.BASE_DIR, 'media', 'quality', 'temp')

def ensure_directories_exist():
    """确保所有必要的目录都存在"""
    directories = [UPLOAD_DIR, RESULT_DIR, TEMP_DIR]
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            logger.info(f"确保目录存在: {directory}")
        except Exception as e:
            logger.error(f"创建目录失败 {directory}: {e}")
            raise

# 任务状态管理
task_status = {}
task_lock = threading.Lock()

def update_task_status(task_id, status, result_file=None, error=None):
    """更新任务状态"""
    with task_lock:
        task_status[task_id] = {
            'status': status,
            'result_file': result_file,
            'error': error,
            'timestamp': time.time()
        }
        logger.info(f"任务 {task_id} 状态更新: {status}")

def get_task_status(task_id):
    """获取任务状态"""
    with task_lock:
        return task_status.get(task_id, {'status': 'not_found'})

def generate_unique_filename(original_name):
    """生成唯一文件名"""
    name, ext = os.path.splitext(original_name)
    counter = 1
    unique_name = original_name
    
    while os.path.exists(os.path.join(UPLOAD_DIR, unique_name)):
        unique_name = f"{name}_{counter}{ext}"
        counter += 1
    
    return unique_name

def extract_shp_from_zip(zip_path):
    temp_dir = os.path.join(TEMP_DIR, f"shp_{uuid.uuid4().hex}")
    os.makedirs(temp_dir, exist_ok=True)
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(temp_dir)
        # shp_files = glob.glob(os.path.join(temp_dir, '*.shp'))
        shp_files = glob.glob(os.path.join(temp_dir, '*', '*.shp'))
        if not shp_files:
            raise FileNotFoundError('zip包中未找到shp文件')
        return shp_files[0], temp_dir
    except Exception as e:
        shutil.rmtree(temp_dir, ignore_errors=True)
        raise e

@method_decorator(csrf_exempt, name='dispatch')
class QualityUploadView(View):
    """文件上传视图"""
    def post(self, request):
        try:
            # 确保目录存在
            ensure_directories_exist()
            
            file = request.FILES.get('file')
            if not file:
                return JsonResponse({'error': '请选择要上传的文件'}, status=400)
            
            logger.info(f"开始上传文件: {file.name}, 大小: {file.size} bytes")
            
            # 检查文件大小
            if file.size > settings.MAX_FILE_SIZE:
                return JsonResponse({
                    'error': f'文件大小超过限制，最大允许 {settings.MAX_FILE_SIZE // 1024 // 1024}MB'
                }, status=400)
            
            # 检查文件类型
            file_ext = os.path.splitext(file.name)[1].lower()
            if file_ext not in settings.ALLOWED_FILE_TYPES:
                return JsonResponse({
                    'error': f'不支持的文件类型: {file_ext}，支持的类型: {", ".join(settings.ALLOWED_FILE_TYPES)}'
                }, status=400)
            
            # 确保上传目录存在
            os.makedirs(UPLOAD_DIR, exist_ok=True)
            
            # 生成唯一文件名
            unique_filename = generate_unique_filename(file.name)
            file_path = os.path.join(UPLOAD_DIR, unique_filename)
            
            logger.info(f"文件路径: {file_path}")
            
            # 保存文件
            with open(file_path, 'wb+') as destination:
                for chunk in file.chunks():
                    destination.write(chunk)
            
            # 验证文件是否成功保存
            if not os.path.exists(file_path):
                raise Exception("文件保存失败")
            
            logger.info(f"文件上传成功: {file_path}")
            
            return JsonResponse({
                'file_id': unique_filename,
                'filename': unique_filename,  # 返回实际保存的文件名
                'original_name': file.name,
                'file_path': file_path,
                'file_size': file.size
            })
            
        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            return JsonResponse({'error': f'文件上传失败: {str(e)}'}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class QualityRunView(View):
    """质量检查运行视图"""
    def post(self, request):
        try:
            # 确保目录存在
            ensure_directories_exist()
            
            data = json.loads(request.body.decode('utf-8'))
            if not data:
                return JsonResponse({'error': '无效的请求数据'}, status=400)
            
            check_type = data.get('check_type')
            
            if not check_type:
                return JsonResponse({'error': 'check_type is required'}, status=400)
            
            logger.info(f"开始质量检查任务: {data}")
            
            # 生成任务ID
            task_id = str(uuid.uuid4())
            
            # 获取输入文件路径
            input_file = None
            if 'file_id' in data:
                # 使用上传的文件
                file_id = data.get('file_id')
                input_file = os.path.join(UPLOAD_DIR, file_id)
                logger.info(f"查找上传文件: {input_file}")
                if not os.path.exists(input_file):
                    logger.error(f"上传文件不存在: {input_file}")
                    logger.info(f"上传目录内容: {os.listdir(UPLOAD_DIR)}")
                    return JsonResponse({'error': 'Uploaded file not found'}, status=404)
            elif 'file_path' in data:
                # 使用指定的文件路径
                input_file = data.get('file_path')
                if not os.path.exists(input_file):
                    return JsonResponse({'error': 'Specified file not found'}, status=404)
            elif check_type in ['attribute_check_db', 'logical_consistency', 'topology_db',
                                'spatial_reference_db','db_integrity','logical_consistency']:
                # 数据库相关检查不需要文件
                pass
            else:
                return JsonResponse({'error': 'File is required for this check type'}, status=400)
            
            # 获取参数
            params = data.get('params', {})
            
            # 启动异步任务
            thread = threading.Thread(
                target=process_quality_task,
                args=(task_id, check_type, input_file, params)
            )
            thread.daemon = True
            thread.start()
            
            return JsonResponse({
                'task_id': task_id,
                'status': 'started',
                'check_type': check_type
            })
            
        except Exception as e:
            logger.error(f"质量检查任务启动失败: {str(e)}")
            return JsonResponse({'error': str(e)}, status=500)

def process_quality_task(task_id, check_type, input_file, params):
    """异步处理质量检查任务"""
    try:
        logger.info(f"开始处理质量检查任务 {task_id}: {check_type}")
        update_task_status(task_id, 'processing')
        
        result = None
        
        if check_type == 'attribute_check':
            logger.info("执行属性检查")
            schema = params.get('schema', {})
            id_field = params.get('id_field', 'id')
            shp_path, temp_dir = extract_shp_from_zip(input_file)
            checker = attribute_checker.AttributeChecker.from_shapefile(shp_path, schema, id_field)
            result = checker.run()
            shutil.rmtree(temp_dir)
        elif check_type == 'attribute_check_db':
            logger.info("执行数据库属性检查")
            schema = params.get('schema', {})
            id_field = params.get('id_field', 'id')
            db_conn_str = params.get('db_conn_str')
            table_name = params.get('table_name')
            db_schema = params.get('db_schema')
            checker = attribute_checker.AttributeChecker.from_database(db_conn_str, schema, table_name, db_schema, id_field)
            result = checker.run()
        elif check_type == 'topology':
            logger.info("执行拓扑检查")
            shp_path, temp_dir = extract_shp_from_zip(input_file)
            checker = vector_quality.TopologyChecker.from_file(shp_path)
            rules = params.get('rules', None)
            result, failed_gdfs = checker.run_checks(rules)
            shutil.rmtree(temp_dir)
        elif check_type == 'integrity':
            logger.info("执行完整性检查")
            required_fields = params.get('required_fields', [])
            not_empty_fields = params.get('not_empty_fields')
            shp_path, temp_dir = extract_shp_from_zip(input_file)
            checker = geo_integrity_checker.ShpIntegrityChecker()
            result = checker.check(shp_path, required_fields,not_empty_fields)
            shutil.rmtree(temp_dir)
        elif check_type == 'spatial_reference':
            logger.info("执行空间参考检查")
            standard_gcs = params.get('gcs', 'EPSG:4326')
            standard_pcs = params.get('pcs',None)
            shp_path, temp_dir = extract_shp_from_zip(input_file)
            checker = spatial_reference_check.SpatialReferenceChecker(standard_gcs, standard_pcs)
            result = checker.check(shp_path)
            shutil.rmtree(temp_dir)
        elif check_type == 'logical_consistency':
            logger.info("执行逻辑一致性检查")
            db_conn_str = params.get('db_conn_str')
            # 前端传 suite_rules，后端应取 suite_rules
            ruleset = params.get('suite_rules', [])
            context = params.get('context')
            checker = logical_consistency_checker.LogicalConsistencyChecker(db_conn_str)
            result = checker.run_checks(ruleset, db_conn_str, context)
            # 保存结果
            result_path = os.path.join(RESULT_DIR, f'{task_id}_logical_consistency.json')
            with open(result_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            update_task_status(task_id, 'completed', result_file=result_path)
            return JsonResponse({'task_id': task_id, 'status': 'completed'})
        elif check_type == 'topology_db':
            logger.info("执行数据库空间表拓扑检查")
            db_conn_str = params.get('db_conn_str')
            table_name = params.get('table_name')
            schema = params.get('schema')
            id_field = params.get('id_field', 'id')
            geom_column = params.get('geom_column', 'geometry')
            chunk_size = params.get('chunk_size', None)
            where_con = params.get('where_con', None)
            if not db_conn_str or not table_name or not schema:
                error_msg = 'db_conn_str, table_name, schema are required'
                logger.error(error_msg)
                update_task_status(task_id, 'error', error=error_msg)
                return
            checker = vector_quality.TopologyChecker.from_database(
                db_conn_str, table_name, schema, geom_column=geom_column, id_field=id_field, where=where_con, chunk_size=chunk_size)
            if getattr(checker, '_is_streaming', False):
                result, failed_gdfs = checker.run_checks_streaming()
            else:
                result, failed_gdfs = checker.run_checks()
        elif check_type == 'spatial_reference_db':
            logger.info("执行数据库空间参考检查")
            db_conn_str = params.get('db_conn_str')
            db_schema = params.get('db_schema')
            table_name = params.get('table_name')
            geom_column = params.get('geom_column', 'geometry')
            standard_gcs = params.get('gcs', 'EPSG:4490')
            standard_pcs = params.get('pcs', None)
            from glff.normalization.quality_inspection.spatial_reference_check.spatial_reference_check import SpatialReferenceChecker
            checker = SpatialReferenceChecker(standard_gcs, standard_pcs)
            result = checker.check_db(db_conn_str, db_schema, table_name, geom_column)
        elif check_type == 'db_integrity':
            # 数据完整性检查（库）分支
            db_conn_str = params.get('db_conn_str') or params.get('db_conn')
            db_schema = params.get('db_schema')
            db_table = params.get('table_name') or params.get('db_table')
            fields_exists = params.get('fields_exists')
            not_empty_fields = params.get('not_empty_fields')
            db_expected_count = params.get('expected_count') or params.get('db_expected_count')
            # 空间字段检查
            spatial_fields = params.get('spatial_fields')
            spatial_fields_list = []
            if spatial_fields:
                if isinstance(spatial_fields, list):
                    spatial_fields_list = spatial_fields
                else:
                    spatial_fields_list = [f.strip() for f in spatial_fields.split(',') if f.strip()]
            from glff.normalization.quality_inspection.logical_consistency_checker.logical_consistency_checker import LogicalConsistencyChecker
            checker = LogicalConsistencyChecker(db_conn_str)
            result = checker.check_db_integrity(db_conn_str, db_schema, db_table, fields_exists, not_empty_fields, db_expected_count, spatial_fields_list)
            # 保存结果
            result_path = os.path.join(RESULT_DIR, f'{task_id}_db_integrity.json')
            with open(result_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            update_task_status(task_id, 'completed', result_file=result_path)
            return JsonResponse({'task_id': task_id, 'status': 'completed'})
        else:
            error_msg = f'Unknown check_type: {check_type}'
            logger.error(error_msg)
            update_task_status(task_id, 'error', error=error_msg)
            return
        
        # 保存结果文件
        result_file = os.path.join(RESULT_DIR, f"{task_id}_result.json")
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        logger.info(f"质量检查任务 {task_id} 完成")
        update_task_status(task_id, 'completed', result_file=result_file)
        
    except Exception as e:
        error_msg = f"Error: {str(e)}\n{traceback.format_exc()}"
        logger.error(f"质量检查任务 {task_id} 失败: {error_msg}")
        update_task_status(task_id, 'error', error=error_msg)

@method_decorator(csrf_exempt, name='dispatch')
class QualityStatusView(View):
    """质量检查状态查询API"""
    def get(self, request):
        task_id = request.GET.get('task_id')
        if not task_id:
            return JsonResponse({'error': 'task_id is required'}, status=400)
        
        status = get_task_status(task_id)
        logger.info(f"查询质量检查任务状态 {task_id}: {status}")
        return JsonResponse(status)

@method_decorator(csrf_exempt, name='dispatch')
class QualityDownloadView(View):
    """质量检查结果下载API"""
    def get(self, request):
        import mimetypes
        task_id = request.GET.get('task_id')
        output_format = request.GET.get('output_format', 'json')
        if not task_id:
            return JsonResponse({'error': 'task_id is required'}, status=400)
        status = get_task_status(task_id)
        if status.get('status') != 'completed':
            return JsonResponse({'error': 'Task not completed'}, status=404)
        result_file = status.get('result_file')
        if not result_file or not os.path.exists(result_file):
            return JsonResponse({'error': 'Result file not found'}, status=404)
        try:
            with open(result_file, 'r', encoding='utf-8') as f:
                result_data = json.load(f)
            result_content = result_data.get('result')
            # 1. txt
            if output_format == 'txt' and isinstance(result_content, str):
                response = HttpResponse(result_content, content_type='text/plain; charset=utf-8')
                response['Content-Disposition'] = f'attachment; filename="{task_id}_quality_result.txt"'
                return response
            # 2. pdf/docx
            if output_format in ['pdf', 'docx'] and isinstance(result_content, dict) and 'file_path' in result_content and os.path.exists(result_content['file_path']):
                file_path = result_content['file_path']
                filename = os.path.basename(file_path)
                content_type, _ = mimetypes.guess_type(filename)
                response = FileResponse(open(file_path, 'rb'), content_type=content_type or 'application/octet-stream')
                response['Content-Disposition'] = f'attachment; filename="{filename}"'
                return response
            # 3. 默认json
            response = HttpResponse(json.dumps(result_data, ensure_ascii=False, indent=2), content_type='application/json; charset=utf-8')
            response['Content-Disposition'] = f'attachment; filename="{task_id}_quality_result.json"'
            return response
        except Exception as e:
            logger.error(f"质量检查结果下载失败: {str(e)}")
            return JsonResponse({'error': f'Download failed: {str(e)}'}, status=500)

# 直接API端点（兼容现有测试）
@method_decorator(csrf_exempt, name='dispatch')
class AttributeCheckView(View):
    """属性检查API"""
    def post(self, request):
        try:
            # 确保目录存在
            ensure_directories_exist()
            
            data = json.loads(request.body.decode('utf-8'))
            if not data:
                return JsonResponse({'error': '无效的请求数据'}, status=400)
            
            zip_path = data.get('shp_zip_path')
            schema = data.get('schema', {})
            id_field = data.get('id_field', 'id')
            shp_path, temp_dir = extract_shp_from_zip(zip_path)
            checker = attribute_checker.AttributeChecker.from_shapefile(shp_path, schema, id_field)
            result = checker.run()
            shutil.rmtree(temp_dir)
            return JsonResponse({'status': 'success', 'result': result})
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)})

@method_decorator(csrf_exempt, name='dispatch')
class AttributeCheckDbView(View):
    """数据库属性检查API"""
    def post(self, request):
        try:
            data = json.loads(request.body.decode('utf-8'))
            db_conn_str = data.get('db_conn_str')
            schema = data.get('schema', {})
            table_name = data.get('table_name')
            db_schema = data.get('db_schema')
            id_field = data.get('id_field')
            
            shp_path, temp_dir = extract_shp_from_zip(data.get('shp_zip_path'))
            checker = attribute_checker.AttributeChecker.from_database(db_conn_str, schema, table_name, db_schema, id_field)
            result = checker.run()
            shutil.rmtree(temp_dir)
            
            return JsonResponse({'status': 'success', 'result': str(result)})
            
        except Exception as e:
            return JsonResponse({'status': 'error', 'error': str(e)}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class LogicalConsistencyView(View):
    """逻辑一致性检查API"""
    def post(self, request):
        try:
            # 确保目录存在
            ensure_directories_exist()
            
            data = json.loads(request.body.decode('utf-8'))
            if not data:
                return JsonResponse({'error': '无效的请求数据'}, status=400)
            
            suite_rules = data.get('suite_rules', [])
            db_conn_str = data.get('db_conn_str')
            context = data.get('context', None)
            
            shp_path, temp_dir = extract_shp_from_zip(data.get('shp_zip_path'))
            checker = logical_consistency_checker.LogicalConsistencyChecker(db_conn_str)
            result = checker.run_checks(suite_rules, db_conn_str, context)
            shutil.rmtree(temp_dir)
            
            return JsonResponse({'status': 'success', 'result': str(result)})
            
        except Exception as e:
            return JsonResponse({'status': 'error', 'error': str(e)}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class TopologyView(View):
    """拓扑关系检查API"""
    def post(self, request):
        try:
            # 确保目录存在
            ensure_directories_exist()
            
            data = json.loads(request.body.decode('utf-8'))
            if not data:
                return JsonResponse({'error': '无效的请求数据'}, status=400)
            
            file_path = data.get('file_path')
            id_field = data.get('id_field', 'id')
            
            if not file_path or not os.path.exists(file_path):
                return JsonResponse({'error': 'Invalid file_path'}, status=400)
            
            shp_path, temp_dir = extract_shp_from_zip(file_path)
            checker = vector_quality.TopologyChecker.from_file(shp_path, id_field=id_field)
            report, failed_gdfs = checker.run_checks()
            shutil.rmtree(temp_dir)
            
            return JsonResponse({'status': 'success', 'result': str(report)})
            
        except Exception as e:
            return JsonResponse({'status': 'error', 'error': str(e)}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class IntegrityView(View):
    """数据完整性检查API"""
    def post(self, request):
        try:
            # 确保目录存在
            ensure_directories_exist()
            
            data = json.loads(request.body.decode('utf-8'))
            if not data:
                return JsonResponse({'error': '无效的请求数据'}, status=400)
            
            zip_path = data.get('shp_zip_path')
            required_fields = data.get('required_fields', [])
            not_empty_fields = data.get('not_empty_fields', [])

            shp_path, temp_dir = extract_shp_from_zip(zip_path)
            checker = geo_integrity_checker.ShpIntegrityChecker()
            result = checker.check(shp_path, required_fields, not_empty_fields)
            shutil.rmtree(temp_dir)

            return JsonResponse({'status': 'success', 'result': str(result)})
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)})

@method_decorator(csrf_exempt, name='dispatch')
class SpatialReferenceView(View):
    """空间参考检查API"""
    def post(self, request):
        try:
            # 确保目录存在
            ensure_directories_exist()
            
            data = json.loads(request.body.decode('utf-8'))
            if not data:
                return JsonResponse({'error': '无效的请求数据'}, status=400)
            
            zip_path = data.get('shp_zip_path')
            gcs = data.get('gcs')
            pcs = data.get('pcs', None)
            
            shp_path, temp_dir = extract_shp_from_zip(zip_path)
            result = spatial_reference_check.check_spatial_reference(shp_path, gcs, pcs)
            shutil.rmtree(temp_dir)
            
            return JsonResponse({'status': 'success', 'result': str(result)})
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)})

@method_decorator(csrf_exempt, name='dispatch')
class TopologyDBView(View):
    """数据库空间表拓扑关系检查API"""
    def post(self, request):
        try:
            data = json.loads(request.body.decode('utf-8'))
            db_conn_str = data.get('db_conn_str')
            table_name = data.get('table_name')
            db_schema = data.get('db_schema')
            id_field = data.get('id_field', 'id')
            geom_column = data.get('geom_column', 'geometry')
            chunk_size = data.get('chunk_size', None)
            where_con = data.get('where_con', None)
            # 参数校验
            if not db_conn_str or not table_name or not db_schema:
                return JsonResponse({'error': 'db_conn_str, table_name, db_schema are required'}, status=400)
            # 检查器调用
            checker = vector_quality.TopologyChecker.from_database(
                db_conn_str, table_name, db_schema, geom_column=geom_column, id_field=id_field, where = where_con ,chunk_size=chunk_size)
            if getattr(checker, '_is_streaming', False):
                report, failed_gdfs = checker.run_checks_streaming()
            else:
                report, failed_gdfs = checker.run_checks()
            # 只返回统计信息，不返回所有失败要素明细
            return JsonResponse({'status': 'success', 'result': report})
        except Exception as e:
            return JsonResponse({'status': 'error', 'error': str(e)}, status=500)

@method_decorator(csrf_exempt, name='dispatch')
class SpatialReferenceDbView(View):
    """数据库空间参考检查API"""
    def post(self, request):
        try:
            data = json.loads(request.body.decode('utf-8'))
            db_conn_str = data.get('db_conn_str')
            db_schema = data.get('db_schema')
            table_name = data.get('table_name')
            geom_column = data.get('geom_column', 'geometry')
            gcs = data.get('gcs', 'EPSG:4490')
            pcs = data.get('pcs', None)
            from glff.normalization.quality_inspection.spatial_reference_check.spatial_reference_check import SpatialReferenceChecker
            checker = SpatialReferenceChecker(standard_gcs_str=gcs, standard_pcs_str=pcs)
            report = checker.check_db(db_conn_str, db_schema, table_name, geom_column)
            return JsonResponse({'status': 'success', 'result': report})
        except Exception as e:
            return JsonResponse({'status': 'error', 'error': str(e)}, status=500)