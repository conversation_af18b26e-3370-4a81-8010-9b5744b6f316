#!/usr/bin/env python
"""Django's command-line utility for administrative tasks."""
import os
import os

import os

def force_load_env_file(env_path=None):
    """
    强制读取 .env 文件，将所有 KEY=VALUE 注入 os.environ（无论是否已存在，全部覆盖）。
    支持无引号/单引号/双引号包裹的值，自动去除空格。
    """
    if env_path is None:
        env_path = os.path.join(os.path.dirname(__file__), '.env')
    if not os.path.exists(env_path):
        return
    with open(env_path, encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith('#') or '=' not in line:
                continue
            key, value = line.split('=', 1)
            key = key.strip()
            value = value.strip().strip('\'"')
            # 强制覆盖所有环境变量
            os.environ[key] = value

# 在所有 import 之前调用
force_load_env_file()

import sys

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """Run administrative tasks."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nank_geo.settings')
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    main()
